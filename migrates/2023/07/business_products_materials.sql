-- sales_rep_workstation.business_products_materials definition

-- Drop table

-- DROP TABLE sales_rep_workstation.business_products_materials;

CREATE TABLE sales_rep_workstation.business_products_materials (
	business_product_id int8 NOT NULL, -- 商機商品
	material_id int8 NOT NULL, -- 料號
	CONSTRAINT business_products_materials_pkey PRIMARY KEY (business_product_id, material_id)
);
COMMENT ON TABLE sales_rep_workstation.business_products_materials IS '商機商品與料號關聯';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN sales_rep_workstation.business_products_materials.business_product_id IS '商機商品';
COMMENT ON COLUMN sales_rep_workstation.business_products_materials.material_id IS '料號';


-- sales_rep_workstation.business_products_materials foreign keys

ALTER TABLE sales_rep_workstation.business_products_materials ADD CONSTRAINT business_products_materials_business_product_id_foreign FOREIGN KEY (business_product_id) REFERENCES sales_rep_workstation.business_products(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.business_products_materials ADD CONSTRAINT business_products_materials_material_id_foreign FOREIGN KEY (material_id) REFERENCES inventory.materials(id) ON UPDATE CASCADE;