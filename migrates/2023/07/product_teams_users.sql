-- sales_rep_workstation.product_teams_users definition

-- Drop table

-- DROP TABLE sales_rep_workstation.product_teams_users;

CREATE TABLE sales_rep_workstation.product_teams_users (
	product_team_id int8 NOT NULL, -- 商品團隊
	user_id int8 NOT NULL, -- 用戶
	CONSTRAINT product_teams_users_pkey PRIMARY KEY (product_team_id, user_id)
);
COMMENT ON TABLE sales_rep_workstation.product_teams_users IS '商品團隊與用戶關聯';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN sales_rep_workstation.product_teams_users.product_team_id IS '商品團隊';
COMMENT ON COLUMN sales_rep_workstation.product_teams_users.user_id IS '用戶';


-- sales_rep_workstation.product_teams_users foreign keys

ALTER TABLE sales_rep_workstation.product_teams_users ADD CONSTRAINT product_teams_users_product_team_id_foreign FOREIGN KEY (product_team_id) REFERENCES sales_rep_workstation.product_teams(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.product_teams_users ADD CONSTRAINT product_teams_users_user_id_foreign FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE;