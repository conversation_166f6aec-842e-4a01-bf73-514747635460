-- sales_rep_workstation.product_teams definition

-- Drop table

-- DROP TABLE sales_rep_workstation.product_teams;

CREATE TABLE sales_rep_workstation.product_teams (
	id bigserial NOT NULL,
	"name" varchar(100) NOT NULL, -- 名稱
	view_order int4 NULL, -- 檢視順序
	deleted bool NOT NULL DEFAULT false, -- 資料刪除
	created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
	updated_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料修改日期
	code varchar(30) NULL, -- 編號
	CONSTRAINT product_teams_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.product_teams IS '商品團隊';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN sales_rep_workstation.product_teams."name" IS '名稱';
COMMENT ON COLUMN sales_rep_workstation.product_teams.view_order IS '檢視順序';
COMMENT ON COLUMN sales_rep_workstation.product_teams.deleted IS '資料刪除';
COMMENT ON COLUMN sales_rep_workstation.product_teams.created_at IS '資料新增日期';
COMMENT ON COLUMN sales_rep_workstation.product_teams.updated_at IS '資料修改日期';
COMMENT ON COLUMN sales_rep_workstation.product_teams.code IS '編號';