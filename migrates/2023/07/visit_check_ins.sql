-- sales_rep_workstation.visit_check_ins definition

-- Drop table

DROP TABLE sales_rep_workstation.visit_check_ins;

CREATE TABLE sales_rep_workstation.visit_check_ins (
	id bigserial NOT NULL,
	deleted bool NOT NULL DEFAULT false, -- 資料刪除
	created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
	updated_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料修改日期
	created_user_id int8 NULL, -- 資料建立人員
	updated_user_id int8 NULL, -- 資料修改人員
	visit_id int8 NOT NULL, -- 拜訪
	lat float8 NOT NULL, -- WGS84 緯度
	lng float8 NOT NULL, -- WGS84 經度
	"content" text NULL, -- 內容
	CONSTRAINT visit_check_ins_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.visit_check_ins IS '拜訪打卡紀錄';

-- <PERSON><PERSON>n comments

COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.deleted IS '資料刪除';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.created_at IS '資料新增日期';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.updated_at IS '資料修改日期';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.created_user_id IS '資料建立人員';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.updated_user_id IS '資料修改人員';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.visit_id IS '拜訪';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.lat IS 'WGS84 緯度';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins.lng IS 'WGS84 經度';
COMMENT ON COLUMN sales_rep_workstation.visit_check_ins."content" IS '內容';


-- sales_rep_workstation.visit_check_ins foreign keys

ALTER TABLE sales_rep_workstation.visit_check_ins ADD CONSTRAINT visit_check_ins_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.visit_check_ins ADD CONSTRAINT visit_check_ins_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.visit_check_ins ADD CONSTRAINT visit_check_ins_visit_id_foreign FOREIGN KEY (visit_id) REFERENCES sales_rep_workstation.visits(id) ON UPDATE CASCADE;