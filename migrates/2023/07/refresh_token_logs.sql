-- public.refresh_token_logs definition

-- Drop table

-- DROP TABLE public.refresh_token_logs;

CREATE TABLE public.refresh_token_logs (
	id bigserial NOT NULL,
	created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
	user_id int8 NOT NULL, -- 使用者
	application_name varchar(100), -- 應用程式名稱
	CONSTRAINT refresh_token_logs_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.refresh_token_logs IS 'Refresh Token 紀錄';

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN public.refresh_token_logs.created_at IS '資料新增日期';
COMMENT ON COLUMN public.refresh_token_logs.user_id IS '使用者';
COMMENT ON COLUMN public.refresh_token_logs.application_name IS '應用程式名稱';

ALTER TABLE public.refresh_token_logs ADD CONSTRAINT refresh_token_logs_user_id_foreign FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE;