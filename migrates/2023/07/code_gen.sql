COMMENT ON TABLE public.access_keys IS '存取金鑰 @GraphQLEnable=false';
COMMENT ON COLUMN sso."token"."token" IS '@GraphQLEnable=false';
COMMENT ON TABLE sso.white_list IS '@GraphQLEnable=false';

-- inventory.materials_medical_device_classifications definition

-- Drop table
DROP TABLE inventory.materials_medical_device_classifications;

CREATE TABLE inventory.materials_medical_device_classifications (
	medical_device_classifications_id int8 NOT NULL,
	material_id int8 NOT NULL,
	CONSTRAINT materials_medical_device_classifications_pkey PRIMARY KEY (medical_device_classifications_id, material_id)
);
-- inventory.materials_medical_device_classifications foreign keys
ALTER TABLE inventory.materials_medical_device_classifications ADD CONSTRAINT materials_medical_device_classifications_1_id_fkey FOREIGN KEY (medical_device_classifications_id) REFERENCES inventory.medical_device_classifications(id);
ALTER TABLE inventory.materials_medical_device_classifications ADD CONSTRAINT materials_medical_device_classifications_2_id_fkey FOREIGN KEY (material_id) REFERENCES inventory.materials(id);
