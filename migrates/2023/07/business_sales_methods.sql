-- sales_rep_workstation.business_sales_methods definition

-- Drop table

-- DROP TABLE sales_rep_workstation.business_sales_methods;

CREATE TABLE sales_rep_workstation.business_sales_methods (
	id bigserial NOT NULL,
	"name" varchar(100) NOT NULL, -- 名稱
	view_order int4 NULL, -- 檢視順序
	deleted bool NOT NULL DEFAULT false, -- 資料刪除
	created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
	updated_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料修改日期
	sales_team_group_id int8 NOT NULL, -- 業務團隊組織
	code varchar(30) NULL, -- 編號
	CONSTRAINT business_sales_methods_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.business_sales_methods IS '商機銷售方式';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN sales_rep_workstation.business_sales_methods."name" IS '名稱';
COMMENT ON COLUMN sales_rep_workstation.business_sales_methods.view_order IS '檢視順序';
COMMENT ON COLUMN sales_rep_workstation.business_sales_methods.deleted IS '資料刪除';
COMMENT ON COLUMN sales_rep_workstation.business_sales_methods.created_at IS '資料新增日期';
COMMENT ON COLUMN sales_rep_workstation.business_sales_methods.updated_at IS '資料修改日期';
COMMENT ON COLUMN sales_rep_workstation.business_sales_methods.sales_team_group_id IS '業務團隊組織';
COMMENT ON COLUMN sales_rep_workstation.business_sales_methods.code IS '編號';


-- sales_rep_workstation.business_sales_methods foreign keys

ALTER TABLE sales_rep_workstation.business_sales_methods ADD CONSTRAINT business_sales_methods_sales_team_group_id_foreign FOREIGN KEY (sales_team_group_id) REFERENCES sales_rep_workstation.sales_team_groups(id) ON UPDATE CASCADE;