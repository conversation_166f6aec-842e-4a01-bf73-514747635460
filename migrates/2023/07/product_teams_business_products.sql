-- sales_rep_workstation.product_teams_business_products definition

-- Drop table

-- DROP TABLE sales_rep_workstation.product_teams_business_products;

CREATE TABLE sales_rep_workstation.product_teams_business_products (
	product_team_id int8 NOT NULL, -- 商品團隊
	business_product_id int8 NOT NULL, -- 商機商品
	CONSTRAINT product_teams_business_products_pkey PRIMARY KEY (product_team_id, business_product_id)
);
COMMENT ON TABLE sales_rep_workstation.product_teams_business_products IS '商品團隊與商機商品關聯';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN sales_rep_workstation.product_teams_business_products.product_team_id IS '商品團隊';
COMMENT ON COLUMN sales_rep_workstation.product_teams_business_products.business_product_id IS '商機商品';


-- sales_rep_workstation.product_teams_business_products foreign keys

ALTER TABLE sales_rep_workstation.product_teams_business_products ADD CONSTRAINT product_teams_business_products_business_product_id_foreign FOREIGN KEY (business_product_id) REFERENCES sales_rep_workstation.business_products(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.product_teams_business_products ADD CONSTRAINT product_teams_business_products_product_team_id_foreign FOREIGN KEY (product_team_id) REFERENCES sales_rep_workstation.product_teams(id) ON UPDATE CASCADE;