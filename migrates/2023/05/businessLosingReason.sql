-- Completed on: 2023-05-15

CREATE TABLE sales_rep_workstation.business_losing_reasons (
	id bigserial NOT NULL,
	"name" varchar(100) NOT NULL, -- 名稱
	view_order int4 NULL, -- 檢視順序
	deleted bool NOT NULL DEFAULT false, -- 資料刪除
	created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
	updated_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料修改日期
	sales_team_group_id int8 NOT NULL, -- 業務團隊組織
	CONSTRAINT business_losing_reasons_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.business_losing_reasons IS '商機丟單原因';

COMMENT ON COLUMN sales_rep_workstation.business_losing_reasons."name" IS '名稱';
COMMENT ON COLUMN sales_rep_workstation.business_losing_reasons.view_order IS '檢視順序';
COMMENT ON COLUMN sales_rep_workstation.business_losing_reasons.deleted IS '資料刪除';
COMMENT ON COLUMN sales_rep_workstation.business_losing_reasons.created_at IS '資料新增日期';
COMMENT ON COLUMN sales_rep_workstation.business_losing_reasons.updated_at IS '資料修改日期';
COMMENT ON COLUMN sales_rep_workstation.business_losing_reasons.sales_team_group_id IS '業務團隊組織';


CREATE TABLE sales_rep_workstation.businesses_losing_reasons (
	business_id int8 NOT NULL, -- 商機
	business_losing_reason_id int8 NOT NULL, -- 丟單原因
	CONSTRAINT businesses_losing_reasons_pkey PRIMARY KEY (business_id, business_losing_reason_id)
);
COMMENT ON TABLE sales_rep_workstation.businesses_losing_reasons IS '商機與丟單原因關聯';

COMMENT ON COLUMN sales_rep_workstation.businesses_losing_reasons.business_id IS '商機';
COMMENT ON COLUMN sales_rep_workstation.businesses_losing_reasons.business_losing_reason_id IS '丟單原因';

ALTER TABLE sales_rep_workstation.business_losing_reasons ADD CONSTRAINT business_losing_reasons_sales_team_group_id_foreign FOREIGN KEY (sales_team_group_id) REFERENCES sales_rep_workstation.sales_team_groups(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.businesses_losing_reasons ADD CONSTRAINT businesses_losing_reasons_business_id_foreign FOREIGN KEY (business_id) REFERENCES sales_rep_workstation.businesses(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.businesses_losing_reasons ADD CONSTRAINT businesses_losing_reasons_business_losing_reason_id_foreign FOREIGN KEY (business_losing_reason_id) REFERENCES sales_rep_workstation.business_losing_reasons(id) ON UPDATE CASCADE;