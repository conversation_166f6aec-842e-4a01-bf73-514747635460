-- Completed on: 2023-05-09

-- sales_rep_workstation.sales_teams_business_products definition

-- Drop table

-- DROP TABLE sales_rep_workstation.sales_teams_business_products;

CREATE TABLE sales_rep_workstation.sales_teams_business_products (
	sales_team_id int8 NOT NULL, -- 業務團隊
	business_product_id int8 NOT NULL, -- 商機商品
	CONSTRAINT sales_teams_business_products_pkey PRIMARY KEY (sales_team_id, business_product_id)
);
COMMENT ON TABLE sales_rep_workstation.sales_teams_business_products IS '業務團隊與商機商品關聯';

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN sales_rep_workstation.sales_teams_business_products.sales_team_id IS '業務團隊';
COMMENT ON COLUMN sales_rep_workstation.sales_teams_business_products.business_product_id IS '商機商品';


-- sales_rep_workstation.sales_teams_business_products foreign keys

ALTER TABLE sales_rep_workstation.sales_teams_business_products ADD CONSTRAINT sales_teams_business_products_business_product_id_foreign FOREIGN KEY (business_product_id) REFERENCES sales_rep_workstation.business_products(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.sales_teams_business_products ADD CONSTRAINT sales_teams_business_products_sales_team_id_foreign FOREIGN KEY (sales_team_id) REFERENCES sales_rep_workstation.sales_teams(id) ON UPDATE CASCADE;