-- Completed on: 2023-05-02

-- public.corporations definition

-- Drop table

-- DROP TABLE public.corporations;

CREATE TABLE public.corporations (
	id int8 NOT NULL GENERATED ALWAYS AS IDENTITY,
	region_id int8 NOT NULL,
	disabled bool NOT NULL DEFAULT false,
	created_at timestamptz NOT NULL DEFAULT now(),
	updated_at timestamptz NOT NULL DEFAULT now(),
	code varchar(30) NOT NULL,
	"name" varchar(60) NOT NULL,
	gui_number varchar(60) NULL, -- 統一編號
	yonyou_set_of_book varchar(60) NULL, -- 用友帳套
	phone varchar(100) NULL, -- 電話
	fax  varchar(100) NULL, -- 傳真
	email varchar(100) NULL, -- email
	address varchar(255) NULL, -- 地址
	website varchar(255) NULL, -- 官網
	facebook varchar(255) NULL, -- Facebook
	instagram varchar(255) NULL, -- Instagram
	twitter varchar(255) NULL, -- Twitter
	weibo varchar(255) NULL, -- 微博
	tiktok varchar(255) NULL, -- 抖音
	CONSTRAINT corporations_code_key UNIQUE (code),
	CONSTRAINT corporations_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.corporations IS '公司法人(原Company是品牌)';

-- Column comments
COMMENT ON COLUMN public.corporations.gui_number IS '統一編號';
COMMENT ON COLUMN public.corporations.yonyou_set_of_book IS '用友帳套';
COMMENT ON COLUMN public.corporations.phone IS '電話 @GraphQLScalar=PhoneNumberResolver';
COMMENT ON COLUMN public.corporations.fax IS '傳真 @GraphQLScalar=PhoneNumberResolver';
COMMENT ON COLUMN public.corporations.email IS 'email @GraphQLScalar=EmailAddressResolver';
COMMENT ON COLUMN public.corporations.address IS '地址';
COMMENT ON COLUMN public.corporations.website IS '官網 @GraphQLScalar=URLResolver';
COMMENT ON COLUMN public.corporations.facebook IS 'Facebook @GraphQLScalar=URLResolver';
COMMENT ON COLUMN public.corporations.instagram IS 'Instagram @GraphQLScalar=URLResolver';
COMMENT ON COLUMN public.corporations.twitter IS 'Twitter @GraphQLScalar=URLResolver';
COMMENT ON COLUMN public.corporations.weibo IS '微博 @GraphQLScalar=URLResolver';
COMMENT ON COLUMN public.corporations.tiktok IS '抖音 @GraphQLScalar=URLResolver';

-- public.corporations_depts definition

-- Drop table

-- DROP TABLE public.corporations_depts;

CREATE TABLE public.corporations_depts (
	corporation_id int8 NOT NULL,
	dept_id int8 NOT NULL,
	CONSTRAINT corporations_depts_pkey PRIMARY KEY (corporation_id, dept_id)
);
COMMENT ON TABLE public.corporations_depts IS '公司法人 <> 部門';


-- public.corporations_depts foreign keys

ALTER TABLE public.corporations_depts ADD CONSTRAINT corporations_depts_dept_id_fk FOREIGN KEY (dept_id) REFERENCES public.departments(id);
ALTER TABLE public.corporations_depts ADD CONSTRAINT corporations_depts_corporation_id_fk FOREIGN KEY (corporation_id) REFERENCES public.corporations(id);