-- Completed on: 2023-05-02

-- customer_types
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('醫院', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('診所', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('醫院', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('診所', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('医疗机构', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('经销商', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('军事单位', 3, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('学校', 4, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('社区卫生所', 5, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);
INSERT INTO sales_rep_workstation.customer_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('其他机构', 6, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);

-- customer_property_types
INSERT INTO sales_rep_workstation.customer_property_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('IOL', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 'IOL_Level');
INSERT INTO sales_rep_workstation.customer_property_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('儀器', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 'Instructment_Level');
INSERT INTO sales_rep_workstation.customer_property_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('CAS', 3, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 'CAS_Level');
INSERT INTO sales_rep_workstation.customer_property_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('等級', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 'General_Level');

-- customer_properties
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Aa', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ab', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ac', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ba', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Bb', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Bc', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ca', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Cb', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Cc', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 1);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Aa', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ab', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ac', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ba', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Bb', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Bc', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ca', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Cb', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Cc', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 2);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('A1', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('A2', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('A3', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('A4', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('A5', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('B1', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('B2', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('B3', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('B4', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('B5', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('C1', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('C2', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('C3', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('C4', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('C5', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 3);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Aa', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ab', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ac', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ba', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Bb', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Bc', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Ca', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Cb', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);
INSERT INTO sales_rep_workstation.customer_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('Cc', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2, 4);

-- customer_certificate_types
INSERT INTO sales_rep_workstation.customer_certificate_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('測試用', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1, 'Test');
INSERT INTO sales_rep_workstation.customer_certificate_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('营业执照', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 'Operating_License');
INSERT INTO sales_rep_workstation.customer_certificate_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('经营许可证', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 'Business_License');
INSERT INTO sales_rep_workstation.customer_certificate_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES('其他凭证', 3, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 'Other_License');
