-- Completed on: 2023-05-02

-- visit_actions
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('In field', NULL, true, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Sales call', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Service call', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Training', 3, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Off', 4, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Office', 5, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Join call', 6, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Admin', 7, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('Handover', 8, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('拜訪目的', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('介紹產品', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('候選人諮商', 3, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('跟刀', 4, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('手術器械交貨', 5, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('術前說明會', 6, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('驗貨', 7, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('支援學術研習活動', 8, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('收集信息', 9, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('公司形象', 10, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('指導', 11, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('開發市場', 12, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_actions ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('產品交貨衛教', 13, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);

-- visit_goals
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('溝通產品FAB', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('舉辦產品CME', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('處理客戶產品疑慮', 3, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('洽談產品合約', 4, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('溝通產品方案', 5, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('溝通演講內容', 6, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('OR-使用sample', 7, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('OR-第一次使用科明OVD, 植入IOL', 8, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('OR教用並找尋損片,汙染片原因', 9, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('OR-學習phaco, 談產品及合作', 10, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('業績進度跟催', 11, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('對帳收款', 12, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('回收暫借產品', 13, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('盤點庫存', 14, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('送件 (醫院訂單、急件)', 15, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('醫院行政流程', 16, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('儀器試用demo', 17, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('儀器跟刀', 18, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_goals ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('其他', 19, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);

-- visit_property_types
INSERT INTO sales_rep_workstation.visit_property_types (id, "name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES(1, '銷售類型', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 'Sales_Type');
INSERT INTO sales_rep_workstation.visit_property_types (id, "name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES(2, '服務類型', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 'Service_Type');
INSERT INTO sales_rep_workstation.visit_property_types (id, "name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES(3, '销售类型', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 'Sales_Type');
INSERT INTO sales_rep_workstation.visit_property_types (id, "name", view_order, deleted, created_at, updated_at, sales_team_group_id, code) VALUES(4, '服务类型', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 'Service_Type');

-- visit_properties
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_例行拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_其它工作事项请写备注', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('协助销售', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_交货、发票', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('叫修', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_进度了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_产品介绍', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_陌生拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_合同处理', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_设备科拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('装机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_配置洽谈', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_售前产品介绍', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_收款处理', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_招标事务', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('操作教育', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_意向了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售-展示机情况了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('产品跟机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_价格洽谈', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('展示撤机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_其它事项', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_陪同主管拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('展示装机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_售后临床培训', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售-会议沟通', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('保养', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售-讲师邀请', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('产品检验', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_寒暄、打招呼', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_主管交待的其它事务', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_手术跟刀', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_客诉处理', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_参加展会或会议（请在内容中写出）', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_参加展会或会议', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('主管_新开业及100万以上订单客户装机后回访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 1);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_展示跟机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_专题会议', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 3, 2);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_产品介绍', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_例行拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_进度了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_其它工作事项请写备注', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_陪同主管拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_主管交待的其它事务', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_合同处理', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_其它事项', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_展示跟机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_售后临床培训', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_价格洽谈', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_参加展会或会议（请在内容中写出）', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_陌生拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_设备科拜访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_招标事务', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_配置洽谈', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_收款处理', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_意向了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_售前产品介绍', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售-展示机情况了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_寒暄、打招呼', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售-会议沟通', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_专题会议', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_交货、发票', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_客诉处理', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('叫修', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('产品跟机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('展示装机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('操作教育', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_网络讲课', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售-讲师邀请', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('通用_参加展会或会议', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('临床_手术跟刀', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('协助销售', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('展示撤机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('装机', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('保养', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('主管_新开业及100万以上订单客户装机后回访', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_展示机情况了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('5052cd63f4d14a3ca0142ba97a9967b1', NULL, true, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('产品检验', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 4);
INSERT INTO sales_rep_workstation.visit_properties ("name", view_order, deleted, created_at, updated_at, sales_team_group_id, type_id) VALUES('销售_竟向了解', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4, 3);

-- visit_time_periods
INSERT INTO sales_rep_workstation.visit_time_periods ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('上午', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_time_periods ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('下午', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_time_periods ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('上午', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_time_periods ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('下午', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_time_periods ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('上午', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);
INSERT INTO sales_rep_workstation.visit_time_periods ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('下午', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 4);

-- visit_types
INSERT INTO sales_rep_workstation.visit_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('計劃', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('非計劃', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 1);
INSERT INTO sales_rep_workstation.visit_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('計劃', 1, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);
INSERT INTO sales_rep_workstation.visit_types ("name", view_order, deleted, created_at, updated_at, sales_team_group_id) VALUES('非計劃', 2, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', 2);