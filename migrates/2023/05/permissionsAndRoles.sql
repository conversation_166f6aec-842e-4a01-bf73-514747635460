-- Completed on: 2023-05-02

-- permission_applications
INSERT INTO "permission".permission_applications ("name", code, description) VALUES('後勤系統', 'ERP', NULL);
INSERT INTO "permission".permission_applications ("name", code, description) VALUES('業務系統', 'SALES_REP', NULL);

-- permission_groups
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('客戶', 'customer', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('聯絡人', 'contact_people', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('報價', 'quotation', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('合約', 'warranty', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('服務單', 'service_order', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('派工', 'work_diary', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('商品', 'product', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('優惠', 'promotion', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('商機', 'business', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('拜訪', 'visit', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('固資', 'fixed_asset', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('競爭對手', 'provider', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('權限', 'permission', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('簽核', 'approval', NULL);
INSERT INTO "permission".permission_groups ("name", code, description) VALUES('業務團隊', 'salesTeam', NULL);

-- permissions
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶閱覽', 'customer.read', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶新增', 'customer.create', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶修改', 'customer.update', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人閱覽', 'contact_people.read', NULL, false, 2, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人新增', 'contact_people.create', NULL, false, 2, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人修改', 'contact_people.update', NULL, false, 2, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人刪除', 'contact_people.delete', NULL, false, 2, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價閱覽', 'quotation.read', NULL, false, 3, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價新增', 'quotation.create', NULL, false, 3, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('合約閱覽', 'warranty.read', NULL, false, 4, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('合約新增', 'warranty.create', NULL, false, 4, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('合約修改', 'warranty.update', NULL, false, 4, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('合約刪除', 'warranty.delete', NULL, false, 4, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('服務單閱覽', 'service_order.read', NULL, false, 5, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('服務單新增', 'service_order.create', NULL, false, 5, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('服務單修改', 'service_order.update', NULL, false, 5, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('服務單刪除', 'service_order.delete', NULL, false, 5, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('派工閱覽', 'work_diary.read', NULL, false, 6, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('派工新增', 'work_diary.create', NULL, false, 6, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('派工修改', 'work_diary.update', NULL, false, 6, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('派工刪除', 'work_diary.delete', NULL, false, 6, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商品閱覽', 'product.read', NULL, false, 7, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商品新增', 'product.create', NULL, false, 7, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商品修改', 'product.update', NULL, false, 7, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商品刪除', 'product.delete', NULL, false, 7, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('優惠閱覽', 'promotion.read', NULL, false, 8, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('優惠新增', 'promotion.create', NULL, false, 8, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('優惠修改', 'promotion.update', NULL, false, 8, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('優惠刪除', 'promotion.delete', NULL, false, 8, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機閱覽', 'business.read', NULL, false, 9, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機新增', 'business.create', NULL, false, 9, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機修改', 'business.update', NULL, false, 9, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機刪除', 'business.delete', NULL, false, 9, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪閱覽', 'visit.read', NULL, false, 10, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪新增', 'visit.create', NULL, false, 10, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪修改', 'visit.update', NULL, false, 10, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪刪除', 'visit.delete', NULL, false, 10, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('門市email', 'store.email', NULL, false, 10, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶閱覽', 'customer.read', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶新增', 'customer.create', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶修改', 'customer.update', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶刪除', 'customer.delete', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶刪除', 'customer.delete', NULL, true, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('首營客戶送審', 'customer.submit', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價修改', 'quotation.update', NULL, true, 3, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價刪除', 'quotation.delete', NULL, true, 3, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('固資新增', 'fixed_asset.create', NULL, false, 11, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('固資修改', 'fixed_asset.update', NULL, false, 11, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('固資閱覽', 'fixed_asset.read', NULL, false, 11, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('固資刪除', 'fixed_asset.delete', NULL, true, 11, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭對手閱覽', 'provider.read', NULL, false, 12, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭對手新增', 'provider.create', NULL, false, 12, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭對手更新', 'provider.update', NULL, false, 12, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭對手刪除', 'provider.delete', NULL, false, 12, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('權限閱覽', 'permission.read', NULL, false, 13, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('權限新增', 'permission.create', NULL, false, 13, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('權限更新', 'permission.update', NULL, false, 13, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('權限刪除', 'permission.delete', NULL, false, 13, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶首營資料送審', 'customer.bpm.submit', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價顯示研究費', 'quotation.commission_amount', NULL, false, 3, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('服務單閱覽(核准人)', 'service_order.approver_user', NULL, false, 5, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶刪除', 'customer.delete', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人閱覽', 'contact_people.read', NULL, false, 2, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人新增', 'contact_people.create', NULL, false, 2, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人修改', 'contact_people.update', NULL, false, 2, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('聯絡人刪除', 'contact_people.delete', NULL, false, 2, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪閱覽', 'visit.read', NULL, false, 10, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪新增', 'visit.create', NULL, false, 10, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪修改', 'visit.update', NULL, false, 10, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('拜訪刪除', 'visit.delete', NULL, false, 10, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭商品閱覽', 'provider.read', NULL, false, 12, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商品閱覽', 'product.read', NULL, false, 7, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('優惠閱覽', 'promotion.read', NULL, false, 8, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶憑證附件功能', 'customer.certificate', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價單閱覽', 'quotation.read', NULL, false, 3, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('報價單新增', 'quotation.create', NULL, false, 3, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機閱覽', 'business.read', NULL, false, 9, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機新增', 'business.create', NULL, false, 9, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機修改', 'business.update', NULL, false, 9, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('商機刪除', 'business.delete', NULL, false, 9, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭商品新增', 'provider.create', NULL, false, 12, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭商品更新', 'provider.update', NULL, false, 12, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('競爭商品刪除', 'provider.delete', NULL, false, 12, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('業務團隊閱覽', 'salesTeam.read', NULL, false, 15, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('業務團隊新增', 'salesTeam.create', NULL, false, 15, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('業務團隊更新', 'salesTeam.update', NULL, false, 15, 1);

-- roles
INSERT INTO "permission".roles ("name", code, deleted, is_system_admin, is_allow_all_regions, allow_region_ids, is_allow_all_tw_companies, is_allow_all_cn_companies, allow_company_ids, is_allow_all_sales_team_groups, allow_sales_team_group_ids, is_allow_all_twn_ec_and_mr_sales_team, is_allow_all_twn_cl_sales_team, is_allow_all_twn_eye_sales_team, is_allow_all_chn_eye_sales_team, allow_sales_team_ids, is_only_own_user, allow_user_ids) VALUES('系統管理者', 'sys_admin', false, true, false, '{}', false, false, '{}', false, '{}', false, false, false, false, '{}', false, '{}');

-- roles_users
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 1); -- ken
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 4); -- mike
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 260); -- 花花
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 503); -- parker
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 842); -- maxine
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 1759); -- robby
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 1765); -- jimmy
INSERT INTO "permission".roles_users (role_id, user_id) VALUES(1, 101); -- 小日本
