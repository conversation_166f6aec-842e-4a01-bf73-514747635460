-- Completed on: 2023-05-02

-- material_types
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '0', '軟體類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '1', '儀器類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '2', '器械類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '3', '零件類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '4', '耗材類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '5', '修理類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '6', '修護工具');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '7', '人工水晶體(IOL)');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '8', '人工水晶體(DOMILENS)');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '9', '健康食品');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '10', '隱型眼鏡');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '11', '組配料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '12', '維修器材');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '13', 'CI處理器/CI植入體/CI升級');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '14', '課程');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '15', 'LS CLUB');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '16', '紅利料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '17', '商機料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '18', '聽寶獎金耗材類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '19', '助聽器延保類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, '20', 'CROS');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'A', '助聽器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'B', '維修材料類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'C', '設備類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'D', '書籍類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'E', 'CI處理器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'F', 'CI植入體');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'G', 'CI升級');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'R', '睡眠呼吸中止儀器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'Z', '進貨測試');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 1, 'Z1', 'DEMO');

INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '0', '軟體類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '1', '儀器類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '2', '器械類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '3', '零件類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '4', '耗材類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '5', '修理類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '6', '修護工具');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '7', '人工水晶體(IOL)');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '8', '人工水晶體(DOMILENS)');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '9', '健康食品');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '10', '隱型眼鏡');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '11', '組配料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '12', '維修器材');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '13', 'CI處理器/CI植入體/CI升級');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '14', '課程');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '15', 'LS CLUB');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '16', '紅利料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '17', '商機料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '18', '聽寶獎金耗材類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '19', '助聽器延保類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, '20', 'CROS');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'A', '助聽器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'B', '維修材料類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'C', '設備類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'D', '書籍類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'E', 'CI處理器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'F', 'CI植入體');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'G', 'CI升級');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'R', '睡眠呼吸中止儀器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'Z', '進貨測試');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 2, 'Z1', 'DEMO');

INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '0', '軟體類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '1', '儀器類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '2', '器械類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '3', '零件類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '4', '耗材類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '5', '修理類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '6', '修護工具');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '7', '人工水晶體(IOL)');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '8', '人工水晶體(DOMILENS)');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '9', '健康食品');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '10', '隱型眼鏡');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '11', '組配料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '12', '維修器材');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '13', 'CI處理器/CI植入體/CI升級');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '14', '課程');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '15', 'LS CLUB');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '16', '紅利料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '17', '商機料號');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '18', '聽寶獎金耗材類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '19', '助聽器延保類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, '20', 'CROS');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'A', '助聽器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'B', '維修材料類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'C', '設備類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'D', '書籍類');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'E', 'CI處理器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'F', 'CI植入體');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'G', 'CI升級');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'R', '睡眠呼吸中止儀器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'Z', '進貨測試');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(1, 3, 'Z1', 'DEMO');

INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, '1', '仪器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, '2', '器械');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, '3', '零件');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, '4', '耗材');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, '5', '其他');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, '6', '修理工具');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, 'A', '助听类');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, 'B', '维修材料类');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 30, 'C', '设备');

INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, '1', '仪器');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, '2', '器械');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, '3', '零件');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, '4', '耗材');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, '5', '其他');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, '6', '修理工具');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, 'A', '助听类');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, 'B', '维修材料类');
INSERT INTO inventory.material_types (region_id, company_id, code, "name") VALUES(2, 32, 'C', '设备');

-- material_repair_types
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'HA', '助聽器');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'CO', '電子耳');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'LE', '除溼機');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'RS', '呼吸器');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'AD', '居家輔具');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'VI', '消毒機');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'EA', 'Morear A類');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 1, 'EB', 'Morear B類');

INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'HA', '助聽器');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'CO', '電子耳');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'LE', '除溼機');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'RS', '呼吸器');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'AD', '居家輔具');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'VI', '消毒機');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'EA', 'Morear A類');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 2, 'EB', 'Morear B類');

INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'HA', '助聽器');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'CO', '電子耳');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'LE', '除溼機');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'RS', '呼吸器');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'AD', '居家輔具');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'VI', '消毒機');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'EA', 'Morear A類');
INSERT INTO inventory.material_repair_types (region_id, company_id, code, "name") VALUES(1, 3, 'EB', 'Morear B類');

-- medical_device_classifications
INSERT INTO inventory.medical_device_classifications (region_id, code, "name", description) VALUES(1, 'class I', '第一等級', '低風險性。');
INSERT INTO inventory.medical_device_classifications (region_id, code, "name", description) VALUES(1, 'class II', '第二等級', '中風險性。');
INSERT INTO inventory.medical_device_classifications (region_id, code, "name", description) VALUES(1, 'class III', '第三等級', '高風險性。');
INSERT INTO inventory.medical_device_classifications (region_id, code, "name", description) VALUES(2, 'class I', '一类', '风险程度低，实行常规管理可以保证其安全、有效的医疗器械。');
INSERT INTO inventory.medical_device_classifications (region_id, code, "name", description) VALUES(2, 'class II', '二类', '具有中度风险，需要严格控制管理以保证其安全、有效的医疗器械。');
INSERT INTO inventory.medical_device_classifications (region_id, code, "name", description) VALUES(2, 'class III', '三类', '具有较高风险，需要采取特别措施严格控制管理以保证其安全、有效的医疗器械。');
