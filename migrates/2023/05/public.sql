-- Completed on: 2023-05-02

-- companies
INSERT INTO public.companies ("name", code, phone, deleted, created_at, updated_at, enabled, hr_code, region_id) VALUES('科林助听器(CN-EYE)', 'CN-EYE', NULL, false, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000', true, NULL, 2);

-- codes
INSERT INTO public.codes (region_id, company_id, "type", prefix, current_code, max_code, created_at, updated_at) VALUES(2, 32, 'eyeWarrantyContract', 'CN-EWC', 0, 999999, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000');
INSERT INTO public.codes (region_id, company_id, "type", prefix, current_code, max_code, created_at, updated_at) VALUES(2, 32, 'eyeServiceOrder', 'CN-ESO', 0, 999999, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000');
INSERT INTO public.codes (region_id, company_id, "type", prefix, current_code, max_code, created_at, updated_at) VALUES(2, 32, 'eyeQuotationOrder', 'CN-EQO', 0, 999999, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000');
INSERT INTO public.codes (region_id, company_id, "type", prefix, current_code, max_code, created_at, updated_at) VALUES(2, 32, 'customer', NULL, 10000, 99999, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000');
INSERT INTO public.codes (region_id, company_id, "type", prefix, current_code, max_code, created_at, updated_at) VALUES(2, 32, 'contactPerson', 'CN-CP', 0, 999999, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000');
INSERT INTO public.codes (region_id, company_id, "type", prefix, current_code, max_code, created_at, updated_at) VALUES(2, 32, 'business', 'CN-B', 0, 99999, '2023-05-02 00:00:00.000', '2023-05-02 00:00:00.000');

-- 信用期間 credit_periods
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '0D', 'Due on Receipt', 0);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '7D', 'Net 7 Days', 7);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '14D', 'Net 14 Days', 14);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '15D', 'Net 15 Days', 15);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '30D', 'Net 30 Days', 30);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '40D', 'Net 40 Days', 40);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '45D', 'Net 45 Days', 45);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '60D', 'Net 60 Days', 60);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '70D', 'Net 70 Days', 70);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '90D', 'Net 90 Days', 90);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '120D', 'Net 120 Days', 120);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '150D', 'Net 150 Days', 150);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '180D', 'Net 180 Days', 180);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '210D', 'Net 210 Days', 210);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(1, '240D', 'Net 240 Days', 240);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '0D', 'Due on Receipt', 0);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '7D', 'Net 7 Days', 7);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '14D', 'Net 14 Days', 14);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '15D', 'Net 15 Days', 15);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '30D', 'Net 30 Days', 30);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '40D', 'Net 40 Days', 40);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '45D', 'Net 45 Days', 45);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '60D', 'Net 60 Days', 60);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '70D', 'Net 70 Days', 70);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '90D', 'Net 90 Days', 90);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '120D', 'Net 120 Days', 120);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '150D', 'Net 150 Days', 150);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '180D', 'Net 180 Days', 180);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '210D', 'Net 210 Days', 210);
INSERT INTO public.credit_periods (region_id, "name", description, days) VALUES(2, '240D', 'Net 240 Days', 240);

-- 稅率 tax_rates
INSERT INTO public.tax_rates (rate, region_id) VALUES(0.05, 1);
INSERT INTO public.tax_rates (rate, region_id) VALUES(0.17, 2);

-- 付款方式 payment_methods
INSERT INTO public.payment_methods (region_id, "name") VALUES(1, '現金');
INSERT INTO public.payment_methods (region_id, "name") VALUES(1, '支票');
INSERT INTO public.payment_methods (region_id, "name") VALUES(2, '现金');
INSERT INTO public.payment_methods (region_id, "name") VALUES(2, '支票');

-- 商品type eye_product_types
INSERT INTO public.eye_product_types (region_id, company_id, "name", view_order) VALUES(1, 6, '手術設備', 1);
INSERT INTO public.eye_product_types (region_id, company_id, "name", view_order) VALUES(1, 6, '檢查設備', 2);
INSERT INTO public.eye_product_types (region_id, company_id, "name", view_order) VALUES(2, 32, '手术设备', 1);
INSERT INTO public.eye_product_types (region_id, company_id, "name", view_order) VALUES(2, 32, '检查设备', 2);

-- 商品項目type eye_product_item_types
INSERT INTO public.eye_product_item_types ("name", view_order, category) VALUES('主商品', 1, 1);
INSERT INTO public.eye_product_item_types ("name", view_order, category) VALUES('配件', 1, 1);
INSERT INTO public.eye_product_item_types ("name", view_order, category) VALUES('贈品', 1, 1);
INSERT INTO public.eye_product_item_types ("name", view_order, category) VALUES('其他', 1, 1);

-- 服務項目type eye_service_order_types
INSERT INTO maintenance.eye_service_order_types ("name", view_order) VALUES('維護與維修', 1);
INSERT INTO maintenance.eye_service_order_types ("name", view_order) VALUES('固資租借', 2);

-- 維修週期type eye_warranty_period_types
INSERT INTO maintenance.eye_warranty_period_types (code, "name", value, unit) VALUES('1M', '每月', 1, 'Month');
INSERT INTO maintenance.eye_warranty_period_types (code, "name", value, unit) VALUES('2M', '每2月', 2, 'Month');

-- 派工類別 type
INSERT INTO maintenance.eye_service_order_work_diary_types ("name") VALUES('類別一');
INSERT INTO maintenance.eye_service_order_work_diary_types ("name") VALUES('類別二');