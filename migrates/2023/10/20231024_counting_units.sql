-- START: http://asking.clinico.com.tw/issues/84969

-- Definitions
CREATE TABLE public.counting_units (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY,
    name varchar(100) NOT NULL,
    code varchar(30) NULL,
    view_order int4 NULL,
    deleted bool NOT NULL DEFAULT false,
    created_at timestamptz(0) NOT NULL DEFAULT now(),
    updated_at timestamptz(0) NOT NULL DEFAULT now(),
    region_id int4 NOT NULL,
    CONSTRAINT counting_units_pkey PRIMARY KEY (id)
);

-- Comments
COMMENT ON TABLE public.counting_units IS '計數單位';
COMMENT ON COLUMN public.counting_units.name IS '名稱';
COMMENT ON COLUMN public.counting_units.code IS '編號';
COMMENT ON COLUMN public.counting_units.view_order IS '檢視順序';
COMMENT ON COLUMN public.counting_units.deleted IS '資料刪除';
COMMENT ON COLUMN public.counting_units.created_at IS '資料新增日期';
COMMENT ON COLUMN public.counting_units.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.counting_units.region_id IS '區域';

-- Foreign keys
ALTER TABLE public.counting_units
  ADD CONSTRAINT counting_units_region_id_fkey
  FOREIGN KEY (region_id)
  REFERENCES public.regions (id) ON UPDATE CASCADE;

-- Migration
ALTER TABLE inventory.materials ADD COLUMN counting_unit_id int8;
COMMENT ON COLUMN inventory.materials.counting_unit_id IS '庫存單位';
ALTER TABLE inventory.materials
  ADD CONSTRAINT materials_counting_unit_id_fkey
  FOREIGN KEY (counting_unit_id)
  REFERENCES public.counting_units (id);

-- Comments
COMMENT ON COLUMN inventory.materials.unit IS '庫存單位（棄用；改用 counting_unit_id）';
COMMENT ON COLUMN inventory.materials.region_id IS '區域';
COMMENT ON COLUMN inventory.materials.company_id IS '公司';
COMMENT ON COLUMN inventory.materials.supplier_id IS '供應商';
COMMENT ON COLUMN inventory.materials.deleted IS '資料刪除';
COMMENT ON COLUMN inventory.materials.created_at IS '資料新增日期';
COMMENT ON COLUMN inventory.materials.updated_at IS '資料修改日期';
COMMENT ON COLUMN inventory.materials.is_active IS '是否可用（棄用；改用 status）';

-- END: http://asking.clinico.com.tw/issues/84969