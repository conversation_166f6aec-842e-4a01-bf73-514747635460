-- START: http://asking.clinico.com.tw/issues/84456

-- Definitions
ALTER TABLE public.eye_fixed_assets ADD COLUMN code varchar(30) NOT NULL;

-- Comments
COMMENT ON COLUMN public.eye_fixed_assets.code IS '編號';


-- Definitions
CREATE TABLE public.eye_fixed_asset_images (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    created_user_id int8 NULL,
    updated_user_id int8 NULL,
    deleted bool NOT NULL DEFAULT false,
    eye_fixed_asset_id int8 NOT NULL,
    name varchar(300) NULL,
    "extension" varchar(30) NOT NULL,
    s3_key varchar(300) NOT NULL,
    memo text NULL,
    CONSTRAINT eye_fixed_asset_images_pkey PRIMARY KEY (id)
);

-- Comments
COMMENT ON TABLE public.eye_fixed_asset_images IS '固定資產圖片';
COMMENT ON COLUMN public.eye_fixed_asset_images.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_images.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_images.created_user_id IS '資料建立人員';
COMMENT ON COLUMN public.eye_fixed_asset_images.updated_user_id IS '資料修改人員';
COMMENT ON COLUMN public.eye_fixed_asset_images.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_fixed_asset_images.eye_fixed_asset_id IS '固定資產';
COMMENT ON COLUMN public.eye_fixed_asset_images.name IS '檔案名稱';
COMMENT ON COLUMN public.eye_fixed_asset_images."extension" IS '副檔名';
COMMENT ON COLUMN public.eye_fixed_asset_images.s3_key IS 'S3 key';
COMMENT ON COLUMN public.eye_fixed_asset_images.memo IS '備註';

-- Foreign keys
ALTER TABLE public.eye_fixed_asset_images
    ADD CONSTRAINT eye_fixed_asset_images_eye_fixed_asset_id_fkey
    FOREIGN KEY (eye_fixed_asset_id) REFERENCES public.eye_fixed_assets (id);


-- Definitions
CREATE TABLE public.eye_fixed_asset_documents (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    created_user_id int8 NULL,
    updated_user_id int8 NULL,
    deleted bool NOT NULL DEFAULT false,
    eye_fixed_asset_id int8 NOT NULL,
    name varchar(300) NULL, -- 檔案名稱
    "extension" varchar(30) NOT NULL, -- 副檔名
    s3_key varchar(300) NOT NULL, -- S3 key
    memo text NULL, -- 備註
    CONSTRAINT eye_fixed_asset_documents_pkey PRIMARY KEY (id)
);

-- Comments
COMMENT ON TABLE public.eye_fixed_asset_documents IS '固定資產文件';
COMMENT ON COLUMN public.eye_fixed_asset_documents.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_documents.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_documents.created_user_id IS '資料建立人員';
COMMENT ON COLUMN public.eye_fixed_asset_documents.updated_user_id IS '資料修改人員';
COMMENT ON COLUMN public.eye_fixed_asset_documents.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_fixed_asset_documents.eye_fixed_asset_id IS '固定資產';
COMMENT ON COLUMN public.eye_fixed_asset_documents.name IS '檔案名稱';
COMMENT ON COLUMN public.eye_fixed_asset_documents."extension" IS '副檔名';
COMMENT ON COLUMN public.eye_fixed_asset_documents.s3_key IS 'S3 key';
COMMENT ON COLUMN public.eye_fixed_asset_documents.memo IS '備註';

-- Foreign keys
ALTER TABLE public.eye_fixed_asset_documents
    ADD CONSTRAINT eye_fixed_asset_documents_eye_fixed_asset_id_fkey
    FOREIGN KEY (eye_fixed_asset_id) REFERENCES public.eye_fixed_assets (id);

-- END: http://asking.clinico.com.tw/issues/84456