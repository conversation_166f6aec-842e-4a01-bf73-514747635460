-- START: http://asking.clinico.com.tw/issues/84390

-- Definitions
ALTER TABLE public.eye_products ADD COLUMN standard_delivery_days int8;

-- Comments
COMMENT ON COLUMN public.eye_products.standard_delivery_days IS '標準交期（天數）';


-- Definitions
CREATE TABLE public.eye_products_product_users (
    eye_product_id int8 NOT NULL,
    product_user_id int8 NOT NULL,
    CONSTRAINT eye_products_product_users_pkey PRIMARY KEY (eye_product_id, product_user_id)
);

-- Comments
COMMENT ON TABLE public.eye_products_product_users IS '眼科商品與產品負責人員關聯';
COMMENT ON COLUMN public.eye_products_product_users.eye_product_id IS '眼科商品';
COMMENT ON COLUMN public.eye_products_product_users.product_user_id IS '產品負責人員';

-- Foreign keys
ALTER TABLE public.eye_products_product_users
    ADD CONSTRAINT eye_products_product_users_eye_product_id_fkey
    FOREIGN KEY (eye_product_id) REFERENCES public.eye_products (id);
ALTER TABLE public.eye_products_product_users
    ADD CONSTRAINT eye_products_product_users_product_user_id_fkey
    FOREIGN KEY (product_user_id) REFERENCES public.users (id);

-- END: http://asking.clinico.com.tw/issues/84390