-- START: http://asking.clinico.com.tw/issues/84973

ALTER TABLE public.eye_products ALTER COLUMN eye_product_type_id DROP NOT NULL;

-- END: http://asking.clinico.com.tw/issues/84973


-- START: http://asking.clinico.com.tw/issues/84969

COMMENT ON COLUMN inventory.materials.code IS '編號';
COMMENT ON COLUMN inventory.materials.name2 IS '名稱（中文）';
COMMENT ON COLUMN inventory.materials.spec IS '規格／機型';

ALTER TABLE inventory.materials ADD COLUMN sale_price numeric(10, 2);
COMMENT ON COLUMN inventory.materials.sale_price IS '銷售金額';

ALTER TABLE inventory.materials ADD COLUMN sale_currency_id int4;
COMMENT ON COLUMN inventory.materials.sale_currency_id IS '銷售幣別';
ALTER TABLE inventory.materials
  ADD CONSTRAINT materials_sale_currency_id_fkey
  FOREIGN KEY (sale_currency_id) REFERENCES public.currencies (id);

ALTER TABLE inventory.materials ADD COLUMN cost_price numeric(10, 2);
COMMENT ON COLUMN inventory.materials.cost_price IS '成本金額';

ALTER TABLE inventory.materials ADD COLUMN cost_currency_id int4;
COMMENT ON COLUMN inventory.materials.cost_currency_id IS '成本幣別';
ALTER TABLE inventory.materials
  ADD CONSTRAINT materials_cost_currency_id_fkey
  FOREIGN KEY (cost_currency_id) REFERENCES public.currencies (id);

ALTER TABLE inventory.materials ADD COLUMN status int4 NOT NULL DEFAULT 1;
COMMENT ON COLUMN inventory.materials.status IS '狀態（0 = 停用, 1 = 啟用）';

INSERT INTO "generator"."enums"
  ("schema","table","column","name","key","value","description")
VALUES
  ('inventory','materials','status','EnumMaterialStatus','Disabled','0','停用'),
  ('inventory','materials','status','EnumMaterialStatus','Enabled' ,'1','啟用');

ALTER TABLE inventory.materials ADD COLUMN created_user_id int8;
COMMENT ON COLUMN inventory.materials.created_user_id IS '資料建立人員';
ALTER TABLE inventory.materials
  ADD CONSTRAINT materials_created_user_id_fkey
  FOREIGN KEY (created_user_id) REFERENCES public.users (id);

ALTER TABLE inventory.materials ADD COLUMN updated_user_id int8;
COMMENT ON COLUMN inventory.materials.updated_user_id IS '資料修改人員';
ALTER TABLE inventory.materials
  ADD CONSTRAINT materials_updated_user_id_fkey
  FOREIGN KEY (updated_user_id) REFERENCES public.users (id);

-- END: http://asking.clinico.com.tw/issues/84969