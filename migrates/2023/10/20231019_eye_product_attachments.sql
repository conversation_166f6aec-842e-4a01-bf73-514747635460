-- START: http://asking.clinico.com.tw/issues/84456

-- Definitions
CREATE TABLE public.eye_product_attachments (
	id int8 NOT NULL GENERATED ALWAYS AS IDENTITY,
	deleted bool NOT NULL DEFAULT false, -- 資料刪除
	created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
	updated_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料修改日期
	"name" varchar(300) NOT NULL, -- 名稱
	"extension" varchar(30) NOT NULL, -- 副檔名
	s3_key varchar(300) NOT NULL, -- S3 Key
	memo text NULL, -- 備註
	eye_product_id int8 NOT NULL, -- 眼科商品
	created_user_id int8 NULL, -- 資料建立人員
	updated_user_id int8 NULL, -- 資料修改人員
	CONSTRAINT eye_product_attachments_pkey PRIMARY KEY (id)
);

-- Comments
COMMENT ON TABLE public.eye_product_attachments IS '眼科商品附件';
COMMENT ON COLUMN public.eye_product_attachments.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_product_attachments.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_product_attachments.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_product_attachments."name" IS '名稱';
COMMENT ON COLUMN public.eye_product_attachments."extension" IS '副檔名';
COMMENT ON COLUMN public.eye_product_attachments.s3_key IS 'S3 Key';
COMMENT ON COLUMN public.eye_product_attachments.memo IS '備註';
COMMENT ON COLUMN public.eye_product_attachments.eye_product_id IS '眼科商品';
COMMENT ON COLUMN public.eye_product_attachments.created_user_id IS '資料建立人員';
COMMENT ON COLUMN public.eye_product_attachments.updated_user_id IS '資料修改人員';

-- Foreign keys
ALTER TABLE public.eye_product_attachments
	ADD CONSTRAINT eye_product_attachments_eye_product_id_fkey
	FOREIGN KEY (eye_product_id) REFERENCES public.eye_products (id);
ALTER TABLE public.eye_product_attachments
	ADD CONSTRAINT eye_product_attachments_created_user_id_fkey
	FOREIGN KEY (created_user_id) REFERENCES public.users (id);
ALTER TABLE public.eye_product_attachments
	ADD CONSTRAINT eye_product_attachments_updated_user_id_fkey
	FOREIGN KEY (updated_user_id) REFERENCES public.users (id);

-- END: http://asking.clinico.com.tw/issues/84456