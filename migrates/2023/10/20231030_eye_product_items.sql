-- START: http://asking.clinico.com.tw/issues/85164

ALTER TABLE eye_product_items ADD COLUMN material_name varchar(200);

UPDATE public.eye_product_items
SET material_name = subquery.material_name
FROM (SELECT i.material_id, m."name" AS material_name
      FROM public.eye_product_items i
      JOIN inventory.materials m ON i.material_id = m.id) AS subquery
WHERE eye_product_items.material_id = subquery.material_id;

ALTER TABLE public.eye_product_items ALTER COLUMN material_name SET NOT NULL;

COMMENT ON COLUMN eye_product_items.material_name IS '名稱';

-- END: http://asking.clinico.com.tw/issues/85164