-- Material 加上 sale_price, currency_id
ALTER TABLE public.materials ADD currency_id int8 NULL;
COMMENT ON COLUMN public.materials.currency_id IS '幣別';

ALTER TABLE public.materials ADD sale_price numeric(10, 2) NULL;
COMMENT ON COLUMN public.materials.sale_price IS '銷售定價';


-- Material unique code 改為 code + region_id

-- 第一階段，先要把原先的關聯table 添加 material_id ， 並且migrate

-- pos_sale_order_details (直接刪)
ALTER TABLE public.pos_sale_order_details DROP CONSTRAINT pos_sale_order_details_material_code_fkey;

-- resmed_devices
ALTER TABLE public.resmed_devices ADD material_id int8 NULL;
update public.resmed_devices d
set material_id = m.id 
from public.materials m
where m.code = d.material_code 
  and m.region_id = 1
;
ALTER TABLE public.resmed_devices ALTER COLUMN material_id SET NOT NULL;
ALTER TABLE public.resmed_devices ADD CONSTRAINT resmed_devices_material_id_fk FOREIGN KEY (material_id) REFERENCES public.materials(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- repair_quotation_items
ALTER TABLE public.repair_quotation_items ADD material_id int8 NULL;
update public.repair_quotation_items q
set material_id = m.id 
from public.materials m
where m.code = q.material_code 
  and m.region_id = 1
;
ALTER TABLE public.repair_quotation_items ADD CONSTRAINT repair_quotation_items_material_id_fk FOREIGN KEY (material_id) REFERENCES public.materials(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- repair_items
ALTER TABLE public.repair_items ADD material_id int8 NULL;
update public.repair_items i
set material_id = m.id 
from public.materials m
where m.code = i.material_code 
  and m.region_id = 1
;
ALTER TABLE public.repair_items ADD CONSTRAINT repair_items_material_id_fk FOREIGN KEY (material_id) REFERENCES public.materials(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- audiologist_traces
ALTER TABLE public.audiologist_traces ADD material_id int8 NULL;
update public.audiologist_traces a
set material_id = m.id 
from public.materials m
where m.code = a.item_id 
  and m.region_id = 1
;
ALTER TABLE public.audiologist_traces ADD CONSTRAINT audiologist_traces_material_id_fk FOREIGN KEY (material_id) REFERENCES public.materials(id) ON DELETE SET NULL ON UPDATE CASCADE;
CREATE INDEX audiologist_traces_material_id_idx ON public.audiologist_traces (material_id);

-- 第二階段，將materials的 ix_materials_code 移除，並重新添加 unique: [ code, region_id ]
ALTER TABLE public.resmed_devices DROP CONSTRAINT resmed_devices_material_code_fkey;
ALTER TABLE public.repair_quotation_items DROP CONSTRAINT repair_quotation_items_material_code_fkey;
ALTER TABLE public.repair_items DROP CONSTRAINT repair_items_material_code_fkey;

DROP INDEX public.ix_materials_code;
CREATE UNIQUE INDEX materials_code_region_id_idx ON public.materials (code,region_id);

-- insert
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114201', '直注吸针头', 'OP', '个', '个', 5877.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00A208-B', 'HEADRIGHTCOVER', 'HV', '个', '个', 260.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-7060018', '自动转臂支架', 'LU', '个', '个', 52958.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-45487-006', '主机箱控制板', 'OV', '个', '个', 9661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-47051', '反光镜', 'OV', '个', '个', 1127.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48247-002', 'DICOM模组', 'OV', '个', '个', 16773.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-49494-001', 'iVue测量头', 'OV', '个', '个', 414845.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME200SMAHRXA-D', '自定义激光内窥镜', 'EO', '个', '个', 68661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-VA', '眼科内窥镜', 'EO', '个', '个', 69522.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-4000', '内窥镜摄像系统', 'EO', '台', '台', 700791.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-2000-D', 'E2CompactMicroprobeLaseran', 'EO', '台', '台', 1709544.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME200SMAXA-D', '自定义激光内窥镜', 'EO', '个', '个', 46348.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-PS300E4', 'Lamppowersupplypartof', 'EO', '个', '个', 41447.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-369130', '驱动泵压力传感器板', 'OP', '个', '个', 10915.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-341-80-120-05', '电感线圈', 'OT', '个', '个', 165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-399-50-530-08', '白色编程模块', 'OT', '个', '个', 864.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220609', '升降台', 'HA', '个', '个', 48246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020960', '清洗液', 'HA', '个', '个', 1025.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022387', '裂隙灯桌面套件', 'HA', '个', '个', 1542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022389', '裂隙灯桌面套件', 'HA', '个', '个', 1566.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220462', '前额支架', 'HA', '个', '个', 16428.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020940', '照明控制线缆2米', 'HA', '个', '个', 620.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022328', '晶星桌面套件', 'HA', '个', '个', 4264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007306', '佳能相机线缆', 'HA', '个', '个', 7731.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007306-D', 'CameracableCanon', 'HA', '个', '个', 7731.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HPP1108', '打印机', 'LS', '个', '个', 1354.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-LBP611CN', '打印机', 'LS', '个', '个', 2392.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022328-D', '桌面套件', 'HA', '个', '个', 4264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-P95D', 'P95D打印机', 'LS', '个', '个', 4290.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-REPAIR', '维修人工费', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SMX10SRA', '超微主板', 'LS', '个', '个', 2960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1801309', '刺激光', 'HA', '个', '个', 148.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1801971', '亮度测量板', 'HA', '个', '个', 2570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400323', 'C-mount接口', 'HA', '个', '个', 1652.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3020310S', 'SpecialToolforCameraModule', 'HA', '个', '个', 761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802397', '控制器中继A板', 'HA', '个', '个', 7811.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803108', '蓝黄视野程序', 'HA', '个', '个', 44606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220229', '裂隙灯显微镜', 'HA', '台', '台', 228198.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7200198-D', '玻璃体手术接触镜', 'HA', '个', '个', 12080.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220096', '眼科光学生物测量仪', 'HA', '台', '台', 391058.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SC-MC', '三桌面检查台', 'FR', '个', '个', 224369.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-BCSM', '椅子底座', 'FR', '个', '个', 21931.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63AB', '水平平衡臂', 'FR', '个', '个', 13337.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SC-MC-D', '65SCthreetableswithchair', 'FR', '个', '个', 224369.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65HS-NE-SX', '综合检查台', 'FR', '个', '个', 317357.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65Z3', '写字工作台', 'FR', '个', '个', 28418.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-MMOTORID9900655', '马达电机', 'FR', '个', '个', 3440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-SM01', '下巴架', 'FR', '个', '个', 1429.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-TT220V-D', '220V供电', 'FR', '个', '个', 2667.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8002752', 'SETOFFOAMCUSHIONSFORLID', 'IT', '个', '个', 314.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340975-ZZ', 'SLAforotherthanHSbut', 'MR', '个', '个', 140320.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340986-ZZ', 'UPFpassiveforLeicamicro-', 'MR', '个', '个', 41525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OD-292USB-II', 'EchoportILO292USB-II', 'OD', '台', '台', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-82501001', '头部检测器固定器', 'OK', '个', '个', 3334.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-LO-50', 'YAG光源', 'OK', '个', '个', 110822.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-OPTOYAG&SLT', 'YAG&SLTLasersystemon', 'OK', '台', '台', 756804.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KE-1941-P-1368', '双锂充电器', 'KE', '个', '个', 5110.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-ORANGEBOX-D', 'Orangeboxformarketing', 'IR', '个', '个', 3936.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-REPAIR', 'IR-维修', 'IR', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DKT-18', '电动桌', 'LS', '个', '个', 1094.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-E5-T', '生物显微镜', 'LS', '个', '个', 3800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-EASYCAP', '视频采集卡', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-HOLDER-DLB502-D', '显示器支架', 'LS', '个', '个', 418.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ACK-E6', '外部电源', 'LS', '个', '个', 220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BATTERY-A13', '进口电池A13', 'LS', '个', '个', 2.50, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA5D', '相机', 'LS', '个', '个', 24829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA80D', '照相机', 'LS', '个', '个', 6545.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO980-3X', '裂隙灯显微镜', 'LS', '台', '台', 55470.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO980-5X-ADAPTER', '裂隙灯显微镜', 'LS', '个', '个', 64350.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP-LED900-66', '桌面', 'LS', '个', '个', 363.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-191004-1', 'KawanoIrrigationSurge', 'OP', '个', '个', 157.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GBK-1000VA-D', '稳压电源', 'LS', '个', '个', 539.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IF-4501', '眼用镊', 'LS', '个', '个', 7056.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-KERATOME-2.8MM', '眼科手术刀', 'LS', '个', '个', 90.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-113308-1', '输液管套', 'LS', '个', '个', 103.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-114217', '注吸头', 'LS', '个', '个', 7212.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GTX1070TI', 'GTX1070Ti公版显卡', 'LS', '个', '个', 3861.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ISTAND-WHEEL', '轮子', 'LS', '个', '个', 515.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA00-Y-KL2', '科以康电动桌', 'LS', '个', '个', 3014.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP600X500-66', '桌面', 'LS', '个', '个', 363.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP900X500-66', '桌面', 'LS', '个', '个', 363.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IF-8111', '角膜上皮掀瓣器', 'LS', '个', '个', 1076.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IK-301.760.69', '打印机放置柜', 'LS', '个', '个', 549.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ML1614-HS100', '佳能工具工业镜头', 'LS', '个', '个', 1025.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-NEWLINE-TV', 'NEWLINE-TV', 'LS', '个', '个', 56947.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-SENSOR-3A', '低功率激光功率计探头', 'LS', '个', '个', 19176.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-VEGA', '激光能量表', 'LS', '个', '个', 65016.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SM01B', 'Chinrestfor65-SA', 'LS', '个', '个', 550.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TRANSFORMER', '变压器', 'LS', '个', '个', 1931.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCHOLDER-301840-D', '支架', 'LS', '个', '个', 492.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCHOLDER-HP-D', '电脑支架', 'LS', '个', '个', 353.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCHOLDER-KL4-D', '电脑支架', 'LS', '个', '个', 952.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G4-D', 'HP电脑', 'LS', '个', '个', 6435.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G5-1', 'HP400G5电脑', 'LS', '个', '个', 5769.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP600G2-D', '惠普电脑', 'LS', '个', '个', 6264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HUAWEI-MB13', '电脑', 'LS', '个', '个', 7858.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVOM720S', '电脑', 'LS', '个', '个', 6545.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-REPAIR', 'KO返厂维修', 'KO', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-633-00', '外置受话器', 'OT', '个', '个', 2310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-660-00', '外置受话器', 'OT', '个', '个', 2640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-662-00', '外置受话器', 'OT', '个', '个', 2640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-168628', '电视适配器', 'OT', '个', '个', 1866.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-SILICONE', '软耳模', 'OT', '个', '个', 180.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.207', 'FEMTOLubricationKits', 'ZO', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.501.002', '手柄辅助镜', 'ZO', '个', '个', 18181.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.012', '一次性手术包', 'ZO', '盒', '盒', 37960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.017-D', 'Z8白内障手术包', 'ZO', '盒', '盒', 49990.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.024', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.040', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 44040.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-RH-1', '光脉冲干眼治疗仪', 'RH', '台', '台', 447813.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-9200-1512-1C-ZZ', 'A-ScanCylinders10.0mm', 'SO', '个', '个', 423.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-RH-1-D', '光脉冲干眼治疗仪', 'RH', '台', '台', 447813.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SL-SO-005', '蝴蝶本', 'SL', '个', '个', 3153.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-A101', 'A超探头', 'SO', '个', '个', 24046.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-A120-2', '测厚探头', 'SO', '个', '个', 28984.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200665', '电源', 'TR', '个', '个', 2351.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-COMBO-1', '视觉功能分析仪', 'TR', '台', '台', 1073497.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-QPED', 'VOLK镜子', 'VO', '个', '个', 9554.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-VTE', '接触式激光眼底诊断镜', 'VO', '个', '个', 10055.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.003.004', '飞秒眼科固体激光治疗仪', 'ZO', '台', '台', 3156830.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.410.001', '放切下角膜的架子', 'ZO', '个', '个', 167270.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-MV5600-D', '眼科B超超声诊断仪', 'SO', '台', '台', 192576.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TL-100003-ZZ', 'TearLabOsmolarityTestCards', 'TL', '个', '个', 6012.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-REPAIR', 'iTrace返厂维修', 'TR', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-VIEWER-SOFTWARE', 'Viewer软件套件', 'TR', '个', '个', 74429.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-MV4500', '眼科B超超声诊断仪', 'SO', '个', '个', 79841.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SP-13-MF', '电池', 'SP', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TL-100013-ZZ', 'TearLabOsmolarityHigh', 'TL', '个', '个', 738.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-V20LC', '镜子', 'VO', '个', '个', 2970.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-CLASSCAL-MODULE', '经典扫描模组', 'TR', '个', '个', 249588.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-COMBO-2', '视觉功能分析仪', 'TR', '台', '台', 923655.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-NSPC-ZZ', 'KonanspecalurmicroscopeXV', 'KO', '台', '台', 268009.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-671-00', '外置受话器', 'OT', '个', '个', 2640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.005', 'CableharnessCavityA5', 'ZO', '个', '个', 30065.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-62813', '泡沫耳塞', 'WE', '个', '个', 7.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.014', '一次性手术包（CorneaPP）', 'ZO', '盒', '盒', 37960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.020', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1200MP0J044-B', '旋转刻度标识', 'HV', '个', '个', 238.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M011-C', '打印机外壳', 'HV', '个', '个', 160.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M017-E', '左侧滑动挡板', 'HV', '个', '个', 147.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-NAVILAS-532', '倍频Nd:YVO激光光凝仪', 'DS', '台', '台', 2714969.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-491.14E', 'MANUALPHOROPTERBRACKET', 'DM', '个', '个', 19590.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-NITROGENTTUBE', '氮气管', 'LS', '个', '个', 473.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OD400UHW-D', '摄像系统', 'LS', '个', '个', 6200.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-7Z01550', '能量表', 'LS', '个', '个', 23103.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OVP-RJ45-E1000', '网络交换机', 'LS', '个', '个', 220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-50821-002', '桌子', 'OV', '个', '个', 67094.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-51423-001-ZZ', 'OPTOVUESolixsystem', 'OV', '台', '台', 1653185.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-400-44735', 'OpticFiberInstallTool', 'OV', '个', '个', 2784.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-45612-001', 'SLD模块含光纤', 'OV', '个', '个', 67094.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-530-42735-002', '下巴架模块', 'OV', '个', '个', 134.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48577-001', '光学相干断层扫描仪', 'OV', '台', '台', 651255.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-DBDGO080-3005', 'poweron/offtouch', 'OV', '个', '个', 174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-54344-003', '电脑', 'OV', '个', '个', 113164.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-HRXVA', '自定义激光内窥镜连接19G弯头', 'EO', '个', '个', 73942.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-XA', '眼科内窥镜', 'EO', '个', '个', 37079.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-4000-D', '内窥镜摄像系统', 'EO', '台', '台', 700791.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7560PA27AV1-A', '开关电源(HRK-7AV1)_旧型号', 'HV', '个', '个', 3732.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR3006-AA', 'PI线', 'HV', '个', '个', 22.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-CRT8800-PCB', 'CRT8800MAIINBOARD', 'HV', '个', '个', 3246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-E0MASSY000-AA', '远程控制器', 'HV', '个', '个', 317.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HDR-7000', '验光头', 'HV', '台', '台', 82928.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EV-V000-014A', '防护镜--病人用', 'EV', '个', '个', 695.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HOCT-1F-ZZ', 'Opticalcoherencetomography', 'HV', '个', '个', 617261.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HS-5000(3X)', 'SlitLamp', 'HV', '台', '台', 47811.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HS-7000-D', '裂隙灯显微镜', 'HV', '台', '台', 81013.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J0MASSY009-AA', '卤素灯泡', 'HV', '个', '个', 707.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-JOMASSY009-AA', '卤素灯', 'HV', '个', '个', 1322.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EHAR5002-AB', 'Harness', 'HV', '个', '个', 2076.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY020-AA', '液晶显示器组件', 'HV', '个', '个', 7153.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY041-AB', 'Z轴马达组件', 'HV', '个', '个', 1104.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192625-D-1', '25G双极电凝笔', 'OP', '个', '个', 5926.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192809', '23G广角照明光纤', 'OP', '个', '个', 1449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OG3MSA', '医用放大镜', 'OR', '个', '个', 4321.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-22M', '医用放大镜', 'OR', '个', '个', 2912.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-STDM-LR', '医用镜子', 'OR', '个', '个', 3006.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OLIV-H', '医用放大镜', 'OR', '个', '个', 626.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMRA-PRP-165', '医用放大镜', 'OR', '个', '个', 9938.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OWIV-HM', '医用放大镜', 'OR', '个', '个', 9534.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-102966', '电池仓门', 'OT', '个', '个', 41.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-104-23-200-00', '数字中功率耳背式助听器', 'OT', '个', '个', 20691.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-NPE-1005', '通讯线缆', 'KO', '个', '个', 3882.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-SP-L23', '内皮计照明灯', 'KO', '个', '个', 2580.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111006', 'ECLIPSE-GRAPHICAL', 'OP', '个', '个', 74200.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M018-E', '右侧滑动挡板', 'HV', '个', '个', 147.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000MPRM020-A', '顶盖', 'HV', '个', '个', 570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7200122-D', '下巴架', 'HA', '个', '个', 20536.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220002', '视野计', 'HA', '个', '个', 474736.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400306', '对比度增强滤镜（黄）', 'HA', '个', '个', 19796.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400308', '分光镜', 'HA', '个', '个', 35808.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1801094', '控制器中继C板', 'HA', '个', '个', 2108.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802393', '主板', 'HA', '个', '个', 23937.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020956', '照明控制线缆5米', 'HA', '个', '个', 857.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G5-1-D', '电脑', 'LS', '个', '个', 5769.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVOM710', '电脑', 'LS', '个', '个', 7503.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0205035-018', '儿童BTE生肖挂绳', 'LS', '个', '个', 30.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0409001', '496胶水', 'LS', '个', '个', 44.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103036', '蓝宝石钻头13.5MM', 'LS', '个', '个', 94.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-25G', '25G套管针', 'LS', '个', '个', 968.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-REP008-STC625PWCC', '摄像单元', 'EO', '个', '个', 66503.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-XL300B', '300W氙气灯', 'EO', '个', '个', 23747.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SA-MC-SX', '左手型二位检查台', 'FR', '个', '个', 128224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-88-SM', '水平电动滑片', 'FR', '个', '个', 15708.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-111095', '数字中功率耳背式助听器', 'OT', '个', '个', 9800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114209', '数字中功率耳背式助听器', 'OT', '个', '个', 24800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114216', '数字中功率耳背式助听器', 'OT', '个', '个', 38800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120713', 'WaxfilterproWax100pcs', 'OT', '个', '个', 16.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124586', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124591', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124771', '电视适配器', 'OT', '个', '个', 1800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124970', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125924-1', '开放式耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-131788', '数字大功率耳背式助听器', 'OT', '个', '个', 10800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133794-1', '低音耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-146345', '数字中功率耳背式助听器', 'OT', '个', '个', 40800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150092', '数字中功率耳背式助听器', 'OT', '个', '个', 20600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8105-5728-00', '椅子', 'LU', '个', '个', 1581.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.021', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.103.150', '角膜植入环功能码', 'ZO', '个', '个', 245987.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-25G-TWEEZER', '眼用镊', 'LS', '个', '个', 6435.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BATTERY-A312', '进口电池A312', 'LS', '个', '个', 2.50, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BM500T', '显微镜', 'LS', '个', '个', 3622.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-2', '数码相机', 'LS', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERALINKPCB-OLD', '相机连线', 'LS', '个', '个', 23158.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-M10', '数码相机', 'LS', '个', '个', 71753.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CL-DRAWER', '抽屉', 'LS', '个', '个', 220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-2', '电脑', 'LS', '个', '个', 8000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PENTACAM-70700', '眼前节测量评估系统', 'LS', '个', '个', 466395.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-POWERSTRIP-D', '专用电源插板', 'LS', '个', '个', 220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-1', '打印机配件', 'LS', '个', '个', 3000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CA613-CDW', '佳能613打印机', 'LS', '个', '个', 3740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPL3118', '打印机', 'LS', '个', '个', 1184.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-POWERSTRIP', '电源线', 'LS', '个', '个', 220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CAIP7280', 'IP7280打印机', 'LS', '个', '个', 1089.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPC5790', '打印机', 'LS', '个', '个', 5638.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP178NW', '打印机', 'LS', '个', '个', 3645.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SANTAKUPS', '山特UPS电源', 'LS', '个', '个', 1716.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SLM-KJ5S1', '手持裂隙灯显微镜', 'LS', '个', '个', 10250.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-USB2.0EXTENDER-D', 'USB扩展器', 'LS', '个', '个', 3024.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00A209-B', 'HEADLEFTCOVER', 'HV', '个', '个', 240.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********', '听力测试平台', 'IT', '台', '台', 493351.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-115023', '外置受话器', 'OT', '个', '个', 1107.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-190700', '空气注入管', 'OP', '个', '个', 215.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192304', '前玻切头', 'OP', '个', '个', 2168.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192514', '一次性三通截止阀', 'OP', '个', '个', 97.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111010-D', 'R-EvoSmartS', 'OP', '个', '个', 375315.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113309', '22Gsiliconsleevegreen1.7-2.1mm', 'OP', '个', '个', 209.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113402', '针头扳手', 'OP', '个', '个', 1338.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114218', '直注吸针头', 'OP', '个', '个', 4115.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114219-D', '2.3mmI/ATipangle', 'OP', '个', '个', 3621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-116003', '消毒盒', 'OP', '个', '个', 2421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-121010', '玻切模组', 'OP', '个', '个', 205044.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192816', '23G一次性隔离广角多端口眼内照明光纤', 'OP', '包', '包', 7345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-181004', '台车', 'OP', '台', '台', 80534.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192225', '一步法25G截止阀套管', 'OP', '个', '个', 2787.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-108964', '数字大功率耳背式助听器', 'OT', '个', '个', 6600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-109012-ITE', '耳内式助听器', 'OT', '个', '个', 5800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124965', '外置受话器', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-REPAIR', 'OR维修', 'OR', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114217', '弯注吸针头', 'OP', '个', '个', 4115.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-115301', '电凝镊', 'OP', '个', '个', 2519.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A314-A', 'HEADFRONTCOVER', 'HV', '个', '个', 83.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HRK-9000A-D', '验光仪', 'HV', '台', '台', 147463.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-12.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10775', '控制板', 'IR', '个', '个', 38173.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-15522', '光纤', 'IR', '盒', '盒', 54117.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31174-01', '裂隙灯激光适配器', 'IR', '个', '个', 95394.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31174-01-D', '裂隙灯激光适配器', 'IR', '个', '个', 95394.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31174-02', '裂隙灯适配器', 'IR', '个', '个', 148928.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65593-D', '护目镜,577nmCEmark', 'IR', '个', '个', 5843.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220750', '桌面轨道套件', 'HA', '个', '个', 5148.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220783', '头带', 'HA', '个', '个', 463.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300644', '管道组（BC/OM900)', 'HA', '个', '个', 4949.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220355', '视野计', 'HA', '台', '台', 303040.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220477', '前额支架', 'HA', '个', '个', 14486.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220546', '控制面板RM02', 'HA', '个', '个', 29401.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220568', '裂隙灯显微镜', 'HA', '台', '台', 279036.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3320087S', '升降柱', 'HA', '个', '个', 31700.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220001-D', '视野计', 'HA', '台', '台', 267490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220355-D', '视野计', 'HA', '台', '台', 303040.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220520-ZZ', '25倍目镜', 'HA', '个', '个', 12370.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220538-YY', 'ImagingModuleIM600forBQ900', 'HA', '个', '个', 106621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220545-ZZ', 'Tabletop""UntModel""BI900', 'HA', '个', '个', 15696.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803300', '反光镜组', 'HA', '个', '个', 46855.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803500', '孔隙滤镜模组', 'HA', '个', '个', 11216.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221020', 'CameraCableCanontoBX900', 'HA', '个', '个', 3502.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804005-EX', '主板（EX）', 'HA', '个', '个', 18449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806308S', 'BLGUI线缆', 'HA', '个', '个', 405.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220601', '升降桌', 'HA', '个', '个', 41533.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220463-D', 'Headrestsilverw/lowerpart,', 'HA', '个', '个', 20288.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220518', '目镜', 'HA', '个', '个', 17658.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220743', '亮度控制开关', 'HA', '个', '个', 2794.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220755', '固定基座', 'HA', '个', '个', 2167.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220766', '卤素灯泡', 'HA', '个', '个', 1669.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220552', '裂隙灯显微镜', 'HA', '台', '台', 259949.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220734', '照明控制线缆', 'HA', '个', '个', 1084.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0102003', '中号耳朵解剖模型', 'LS', '个', '个', 282.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.056', 'FarradayInsulatorincl', 'ZO', '个', '个', 90293.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.003.008-ZZ', 'FEMTOLDVZ8', 'ZO', '个', '个', 4273532.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-44427-003', 'ReVue软件', 'OV', '个', '个', 9810.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-23.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-7.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8016806', '客观听觉测试平台', 'IT', '台', '台', 417451.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-REM440MODULE', '软件', 'IT', '个', '个', 51442.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-USB-CVRS9H', 'USB转换器', 'KO', '个', '个', 1712.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-311070', '裂隙灯适配器支架', 'MR', '个', '个', 25719.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A317-A', 'YAXISGUIDEPLATE4', 'HV', '个', '个', 61.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-22.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-26.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1230A000274-A', 'A23LCDwindow;A2', 'HV', '个', '个', 76.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00A212-B', 'HEADBACKBOTTOMCOVER', 'HV', '个', '个', 160.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000MP0A304-B', 'HEADFRONTCOVERXYZ', 'HV', '个', '个', 337.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000MPR0066-A', '顶盖', 'HV', '个', '个', 347.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.400.001', '掀瓣器', 'ZO', '个', '个', 14154.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.024', 'LED显示板', 'ZO', '个', '个', 5836.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.025', '负压系统电路板', 'ZO', '个', '个', 10271.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-115301-D', '电凝镊', 'OP', '个', '个', 2519.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0679-000', '电路板', 'CA', '个', '个', 634.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0680-000', '电路板', 'CA', '个', '个', 607.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1700-000', 'PCBUNIT.EXT.FI', 'CA', '个', '个', 260.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-HS100', '光学相干断层扫描仪', 'CA', '台', '台', 937469.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH9-0487-000', '(NOUSE)USECA-BH9-0487-050', 'CA', '个', '个', 3509.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11175', '手术显微镜保险装置', 'IR', '个', '个', 17426.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14390', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 4240.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14573F', '眼内光纤', 'IR', '个', '个', 4502.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-15980', '眼内光纤', 'IR', '个', '个', 4421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30493', '激光防护滤镜', 'IR', '个', '个', 52243.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31808', '无线脚踏', 'IR', '个', '个', 43564.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-SLX-SYSTEM', '半导体激光治疗仪', 'IR', '组', '组', 398870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-TX-SYSTEM', '激光光凝仪', 'IR', '台', '台', 277076.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-13920-1', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 4586.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220636', '下巴架', 'HA', '个', '个', 29994.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220685', '裂隙灯显微镜', 'HA', '台', '台', 808411.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820455S', '控制板O600', 'HA', '个', '个', 47941.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1821000S', '摄像机校准工具', 'HA', '个', '个', 12676.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220210', '裂隙灯显微镜', 'HA', '台', '台', 123690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220732', 'BX相机连接线', 'HA', '个', '个', 9664.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220738', '口水板', 'HA', '个', '个', 820.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220748', '黄色滤镜', 'HA', '个', '个', 24833.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220751', '桌面轨道套件', 'HA', '个', '个', 2226.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220477-D', '下巴架', 'HA', '个', '个', 14486.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220722', '呼吸防护罩', 'HA', '个', '个', 408.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220753', '桌面套件', 'HA', '个', '个', 2226.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403340', 'LeadScrewAssembly', 'CL', '个', '个', 1966.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0302002-001', '耳窥镜（光纤型）德国HEINE', 'LS', '个', '个', 3080.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0302009', '笔灯德国HEINE', 'LS', '个', '个', 1155.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0604009', '凹口起子', 'LS', '个', '个', 31.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1102023', '圆锥形磨头手柄5S', 'LS', '个', '个', 93.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-MOUNT', '相机接口', 'LS', '个', '个', 19500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192516', '同步联结器', 'OP', '个', '个', 150.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SWITCH', 'switchforliftingtable', 'LS', '个', '个', 386.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0502005', '韩国手钻JC100A+106', 'LS', '个', '个', 2376.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0604013', '大号一字起子', 'LS', '个', '个', 9.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1102004', '软耳模用球形钻3.1mm', 'LS', '个', '个', 44.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103003', '圆柱形砂纸手柄', 'LS', '个', '个', 62.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP-480*800', '桌面', 'LS', '个', '个', 385.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182609', '耳内式助听器', 'OT', '个', '个', 18690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178509', '蓝牙转换器', 'OT', '个', '个', 4783.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('PH-200-50-210-00', 'Hearit模块', 'PH', '个', '个', 1378.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.028', '管路套件', 'ZO', '个', '个', 1926.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.040', '变压器', 'ZO', '个', '个', 20153.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.048-E', '激光转臂（适用于CLASSIC/CRYSTAL)', 'ZO', '个', '个', 241208.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.053', 'RotatorMirrorclassic/Crystal', 'ZO', '个', '个', 26144.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.120', 'ETX卡', 'ZO', '个', '个', 87535.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.142', '选择器控制板', 'ZO', '个', '个', 18966.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.155-E', 'OCT软件系统', 'ZO', '个', '个', 369592.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-ISCAN-D', '眼科光学相干断层扫描仪', 'OV', '台', '台', 447603.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.185-E', '激光腔', 'ZO', '个', '个', 945768.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-16.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-24.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J2MASSY404-AA', '照明组件', 'HV', '个', '个', 23839.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J0MASSY014-AC', '过滤器', 'HV', '个', '个', 438.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-12.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-16.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-28.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-10.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-14.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-6.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-15.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-14.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-7240029', '椅子转动系统', 'LU', '个', '个', 5628.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-7240030', '自动前后移椅', 'LU', '台', '台', 13788.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-112101', '双线性脚踏开关', 'OP', '个', '个', 31793.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MD-2900CE', '20规格玻切头R4', 'MD', '个', '个', 1825.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206617', 'CROS', 'OT', '个', '个', 9450.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-112105-D', '脚踏', 'OP', '个', '个', 34973.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-39380100C', 'Pulsar2汞板', 'OP', '个', '个', 127801.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-149281', '耳塞', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178940', '耳内式助听器', 'OT', '个', '个', 33390.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182722', '耳内式助听器', 'OT', '个', '个', 12390.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182729', '耳内式助听器', 'OT', '个', '个', 13400.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1709-000', 'CAPFORTX-2020P', 'CA', '个', '个', 54.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1157-000', '驱动装置', 'CA', '个', '个', 6662.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3103-000', 'PCB板', 'CA', '个', '个', 2946.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-3402-000', '清洁纸', 'CA', '个', '个', 3753.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-0193-000', 'DedicatedEOS90D', 'CA', '个', '个', 27513.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-EL-1', '外固视灯', 'CA', '个', '个', 8910.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2864-020', '氙灯组件', 'CA', '个', '个', 12525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-3714-000', '电路板', 'CA', '个', '个', 13511.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-15522-D', 'G6微脉冲激光探头', 'IR', '盒', '盒', 54117.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-16200', '眼内光纤', 'IR', '个', '个', 210.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-66200-Y-D', 'IQ577微脉冲模块', 'IR', '个', '个', 48718.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30295', '应急控制开关', 'IR', '个', '个', 2494.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11256', 'G型连接线', 'IR', '个', '个', 5454.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6502009', '幻灯片', 'CL', '个', '个', 1287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401001', '同视机', 'CL', '个', '个', 209863.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6502008', '幻灯片', 'CL', '个', '个', 1287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-5806005', '手持式压平眼压计', 'CL', '台', '台', 49873.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-040.25E', '2位旋转桌面', 'DM', '台', '台', 49217.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-003.26E', '左手位检查台', 'DM', '个', '个', 208161.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-601.20E', '电动仪器', 'DM', '个', '个', 21723.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-530-C0118-002', 'iBase2,Foreheadbelt', 'CR', '个', '个', 805.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191958', '耳内式助听器', 'OT', '个', '个', 7860.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-196672', '数字中功率耳背式助听器', 'OT', '个', '个', 10380.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205534', '耳背式助听器', 'OT', '个', '个', 34440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205566', '耳背式助听器', 'OT', '个', '个', 14070.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113107-D', 'phacohandpiecesix', 'OP', '个', '个', 51760.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113308', '硅胶套', 'OP', '个', '个', 186.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114219', '弯注吸针头', 'OP', '个', '个', 3621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-116002', '集液袋', 'OP', '个', '个', 54.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-163202', '角膜地形图底座', 'OP', '个', '个', 24798.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-116001-D', '测试套', 'OP', '个', '个', 235.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-116003-D', '消毒盒', 'OP', '台', '台', 2421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-117004', '管道组（吸引管）', 'OP', '个', '个', 4979.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192227', '27GTROCARSYSTEM,ONE-STEP', 'OP', '个', '个', 3006.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192304-D', 'AnteriorvitrectomyprobeforPulsar2', 'OP', '个', '个', 2168.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.017', 'Z8白内障手术包', 'ZO', '盒', '盒', 4999.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.730.006', 'FilterPadZ2-Z8', 'ZO', '个', '个', 1284.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.014', '手柄固视调整工具', 'ZO', '个', '个', 1945.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.041', '硅树脂OCT校准工具', 'ZO', '个', '个', 316.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-17.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-20.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-21.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-27.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-191702', '一次性管道组', 'OP', '个', '个', 784.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-AC440-B', '软件', 'IT', '个', '个', 23974.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-5014-000', '前额拖框架', 'CA', '个', '个', 2661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2845-020', '闪光单元', 'CA', '个', '个', 34039.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65593', '护目镜,577nmCEmark', 'IR', '个', '个', 5843.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70292', '扫描激光传输装置', 'IR', '个', '个', 415058.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-13323', '810激光护目镜', 'IR', '个', '个', 12367.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-87287', '激光间接检验镜', 'IR', '个', '个', 26924.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-CYCLO-G6-SYSTEM', '眼科半导体激光治疗仪', 'IR', '台', '台', 728584.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-IQ577-SYSTEM', '眼科半导体激光光凝仪', 'IR', '台', '台', 569271.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30775', '防护滤镜', 'IR', '个', '个', 29043.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6411001', '幻灯片套装', 'CL', '个', '个', 13699.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405083', '下额托万向接头', 'CL', '个', '个', 1280.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6512004', '十字线后像幻灯片', 'CL', '个', '个', 1251.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-14-998.20E', '运输包装', 'DM', '个', '个', 3834.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-001.20E', '810检查台底座', 'DM', '台', '台', 183495.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-322.20E', '扶手固定（2部分）', 'DM', '个', '个', 3283.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-101-10E', '适配器', 'DM', '个', '个', 1778.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-M02E', '装配电缆', 'DM', '个', '个', 652.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-090.25E', '装镜片的旋转抽屉', 'DM', '个', '个', 13709.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-231.20E', '病人椅', 'DM', '台', '台', 49847.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-051.20E', '电动靠背倾斜', 'DM', '个', '个', 24143.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-821.25E', '折叠脚凳', 'DM', '个', '个', 9665.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-900.20E', '左边旋转扶手', 'DM', '个', '个', 2259.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP150A', 'HP打印机', 'LS', '个', '个', 2540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-500-C0643-002', '电脑单元', 'CR', '个', '个', 12345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-560-C0205-002', '笔记本电脑USB线', 'CR', '个', '个', 856.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0801-001', '升降桌', 'CR', '个', '个', 6575.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-560-C0204-001', 'DC线缆', 'CR', '个', '个', 999.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-44491-B', 'CCD组件', 'OV', '个', '个', 8051.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-26.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8108991', '临床型听力计', 'IT', '台', '台', 165000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-CALLISTO', '听力测试平台', 'IT', '个', '个', 45567.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-1813KC-PC19', '控制板', 'KO', '个', '个', 66098.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-8.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-UG-20786', '绿联HUB', 'LS', '个', '个', 143.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-UPS-SANTAK-C2K', 'UPS电源', 'LS', '个', '个', 3299.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-USB3.0-3M', 'USB线', 'LS', '个', '个', 165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-203', 'Padfortheheadrestof', 'LKC', '个', '个', 3908.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-010', 'EOGActiveElectrodes8mmAG-AGCI-redorwhitewire', 'LKC', '个', '个', 1005.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-131673', '数字中功率耳背式助听器', 'OT', '个', '个', 9800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150-00-301-00', '蓝牙伴侣', 'OT', '个', '个', 1980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-127235', '迷你适配器R', 'OT', '袋', '袋', 544.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-190700-D', '空气注入管', 'OP', '个', '个', 215.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192518-D', 'Adaptersyringetubingllf-Aaiassistant', 'OP', '个', '个', 192.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192525', '一次性硅油', 'OP', '个', '个', 309.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192623-D', '23G眼内电凝表', 'OP', '个', '个', 6038.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192807', '23GA照明光纤', 'OP', '个', '个', 1417.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192817', '眼内照明光纤', 'OP', '个', '个', 1329.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200662', '视觉分析仪镜组', 'TR', '个', '个', 487.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-149308', '双孔低音耳塞', 'OT', '袋', '袋', 105.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182764', '耳内式助听器', 'OT', '个', '个', 11340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191908', '耳内式助听器', 'OT', '个', '个', 10480.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191914', '耳内式助听器', 'OT', '个', '个', 9430.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SL-SO-003', '随机点"E"', 'SL', '个', '个', 2281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.109-E', '激光腔', 'ZO', '个', '个', 797542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.110-E', '控制器', 'ZO', '个', '个', 226132.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124966', '外置受话器', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178965', '耳内式助听器', 'OT', '个', '个', 32340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-149282', '耳塞', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191909', '耳内式助听器', 'OT', '个', '个', 9960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191913', '耳内式助听器', 'OT', '个', '个', 9430.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-201869', '数字大功率耳背式助听器', 'OT', '个', '个', 5130.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-149307', '双孔低音耳塞', 'OT', '袋', '袋', 105.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178967', '耳内式助听器', 'OT', '个', '个', 32340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191942', '耳内式助听器', 'OT', '个', '个', 8380.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206574', '耳内式助听器', 'OT', '个', '个', 5760.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206603', '耳内式助听器', 'OT', '个', '个', 4180.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-214925', '耳背式助听器', 'OT', '个', '个', 8910.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191941', '耳内式助听器', 'OT', '个', '个', 8910.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-196651', '助听器', 'OT', '个', '个', 19740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022178', 'USB线', 'HA', '个', '个', 436.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001219', '底座滑板', 'HA', '个', '个', 1484.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022332', '磁性桌面垫板', 'HA', '个', '个', 1903.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020392', '晶星900电源', 'HA', '个', '个', 1728.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022610S', '反射镜外罩', 'HA', '个', '个', 88526.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803300-E', '反光镜组', 'HA', '个', '个', 17424.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6512002', '后像幻灯片', 'CL', '个', '个', 1287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-9000004358', '运输包装', 'DM', '箱', '箱', 24766.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-085.14E', '检眼镜座', 'DM', '个', '个', 7240.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-497.14E', '综合验光仪手动托架', 'DM', '个', '个', 36243.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-12-998.20E', '运输包装', 'DM', '箱', '箱', 5752.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-501.20E', '综合验光仪支架', 'DM', '个', '个', 62217.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-560-C0206-004', '手柄缆线', 'CR', '个', '个', 999.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-TONOVUE', '非接触式眼压计', 'CR', '台', '台', 89404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-41094', 'PCBARTIllumination', 'OV', '个', '个', 1865.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-ANGIOOCT', '光学相干断层扫描仪', 'OV', '台', '台', 897712.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME200HRA', '眼科内窥镜', 'EO', '个', '个', 38400.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-112103', '双线性脚踏开关', 'OP', '个', '个', 31793.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113101-D', '超乳手柄', 'OP', '个', '个', 39218.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113240-D', '喇叭口针头', 'OP', '个', '个', 626.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-7.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113241-D', '21GFlaredphacotip,2.3mmi', 'OP', '个', '个', 1829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114223', '22GAngledI/Acannulaslisleeve0.3forpulsar/pulsar2', 'OP', '个', '个', 3621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-201889', '数字中功率耳背式助听器', 'OT', '个', '个', 4710.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA04', '升降桌', 'LS', '个', '个', 3861.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205568', '耳背式助听器', 'OT', '个', '个', 13020.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205592', '助听器', 'OT', '个', '个', 19740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-201967', '数字中功率耳背式助听器', 'OT', '个', '个', 3450.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206567', '耳内式助听器', 'OT', '个', '个', 5440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221873', 'A-助听器', 'OT', '个', '个', 35490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221876', 'A-助听器', 'OT', '个', '个', 21840.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230272', 'A-助听器', 'OT', '个', '个', 33390.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.730.003', '滤网', 'ZO', '个', '个', 467.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.033', '飞秒工具', 'ZO', '个', '个', 1225.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-130959', '数字中功率耳背式助听器', 'OT', '个', '个', 19800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-20131', 'Silicast注射器', 'WE', '个', '个', 277.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-3', '配件', 'LS', '个', '个', 3000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP600G3-D', '电脑', 'LS', '个', '个', 6264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-IVUENR', '电脑', 'LS', '个', '个', 7973.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-THINKPADE490', '笔记本电脑', 'LS', '个', '个', 4730.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PM100D-HS100', 'HS100激光能量表PM100D', 'LS', '个', '个', 12631.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-6-D', 'LS-PARTS-6-D', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.003', 'LDV冷却器', 'ZO', '台', '台', 99693.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DK20/30', '电动桌', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-NES-1000P', '便携式眼底照相机', 'LS', '个', '个', 35000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.004-R', 'LDV激光腔', 'ZO', '个', '个', 809023.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.012', '线缆组', 'ZO', '个', '个', 6711.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.037', 'USB隔离器', 'ZO', '个', '个', 16048.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.041', '保险管', 'ZO', '个', '个', 7295.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.042', '组件', 'ZO', '个', '个', 4921.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.073', '锁模装置', 'ZO', '个', '个', 20425.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.078', '飞秒手柄摄像头单元', 'ZO', '个', '个', 153673.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206602', '耳内式助听器', 'OT', '个', '个', 4180.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-225678', '充电底座', 'OT', '个', '个', 2940.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230266', 'A-助听器', 'OT', '个', '个', 33390.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230281', 'A-助听器', 'OT', '个', '个', 19740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-390-01-040-00', '适配器', 'OT', '个', '个', 359.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-66200-G-D', 'IQ532微脉冲模组', 'IR', '个', '个', 62712.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-0520058', '反射镜对齐工具', 'HA', '个', '个', 2636.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001307', '电源盒', 'HA', '个', '个', 14466.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007304', '闪光灯线缆', 'HA', '个', '个', 26297.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803800', '摄像头模组', 'HA', '个', '个', 14447.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804005', '主板', 'HA', '个', '个', 41993.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804155', '控制板', 'HA', '个', '个', 1903.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1002788', '盖板', 'HA', '个', '个', 404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020755S', '调节螺丝', 'HA', '个', '个', 334.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022106', '电源供应', 'HA', '个', '个', 1552.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806260S', 'RB控制板', 'HA', '个', '个', 348.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-843.20E', '裂隙灯头枕适配器', 'DM', '个', '个', 3742.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-14-656.51E', '下巴架适配器', 'DM', '个', '个', 2807.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-041.20E', '电子锁开关', 'DM', '个', '个', 5543.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-403.26E', 'Refraktions-/UntersuchungseinheitHS-2010plusLinksausführung', 'DM', '台', '台', 208814.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-M055E', 'LS900预安装件', 'DM', '个', '个', 4390.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0101064', '左耳展示模板（6个一组）', 'LS', '台', '台', 542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0504001', '铸环', 'LS', '个', '个', 11.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103022', '麻花钻2.35', 'LS', '个', '个', 109.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-64634', '灯泡', 'LS', '个', '个', 77.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BX-HSM600-PLATE', 'ReinforcingplateforHSM600', 'LS', '个', '个', 592.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103002', '夹砂纸钻', 'LS', '个', '个', 62.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0301002', 'CIC注射器', 'LS', '个', '个', 75.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0203001', 'PVC吹气球', 'LS', '个', '个', 25.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103005', '麻花钻0.6mm', 'LS', '个', '个', 82.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103014', '麻花钻2.35', 'LS', '个', '个', 34.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP-600500-D', '桌面', 'LS', '个', '个', 330.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0302010', '伟伦耳窥镜', 'LS', '个', '个', 880.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0502001', '韩国手钻', 'LS', '个', '个', 748.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-53969-001', '光谱仪组件', 'OV', '个', '个', 289964.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-580-48250-002', 'Dicom模组', 'OV', '个', '个', 26864.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48100-001', '光学相干断层扫描仪', 'OV', '台', '台', 1257410.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-50940-001-D', 'OCT量化分析软件', 'OV', '个', '个', 83017.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63DC', '垂直马达', 'FR', '个', '个', 27576.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-117001', '管道组', 'OP', '个', '个', 5144.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-162206', '裂隙灯适配套件', 'OP', '个', '个', 16510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-191703', '一次性管道组', 'OP', '个', '个', 709.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192315', '25GDisposabledualbladevitrectomyprobe', 'OP', '个', '个', 3842.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192513-D', '一次性灌注气管+过滤器', 'OP', '个', '个', 152.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8000008', '三抽屉柜', 'LU', '个', '个', 7879.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8105-8029-00', '附加控制模组', 'LU', '个', '个', 29431.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A215-B', 'HEADFRONTCOVER', 'HV', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MD-2940CE', '25G玻切头(一盒6个)', 'MD', '个', '个', 1932.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('NL-20-4001', '供电电源', 'NL', '个', '个', 1266.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178947', '耳内式助听器', 'OT', '个', '个', 34440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-196657', '耳背式助听器', 'OT', '个', '个', 19740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205526', '耳背式助听器', 'OT', '个', '个', 35490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806301', '控制板', 'HA', '个', '个', 714.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205560', '耳背式助听器', 'OT', '个', '个', 14070.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.149-E', '激光腔控制器', 'ZO', '个', '个', 148323.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.193', 'LaserExitWindowLEW', 'ZO', '个', '个', 52521.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65512', '裂隙灯适配器', 'IR', '个', '个', 93213.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-66200-Y', 'IQ577微脉冲模块', 'IR', '个', '个', 48718.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70292-D', '扫描激光传输装置', 'IR', '个', '个', 415058.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70295', '扫描激光传输装置', 'IR', '台', '台', 414569.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-GL-SYSTEM', '倍频Nd:YAG激光光凝仪', 'IR', '组', '组', 345019.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-IQ577-SYSTEM-D', '眼科半导体激光光凝仪', 'IR', '台', '台', 569271.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-HEINE20DLENS-ZZ', 'HEINE20DLENS', 'IR', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820137S', '线缆O900', 'HA', '个', '个', 885.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********', '裂隙灯灯头线缆', 'HA', '个', '个', 992.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********', '齿轮螺母', 'HA', '个', '个', 2487.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********', '刺激光对齐工具', 'HA', '个', '个', 2841.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3320108S', 'WheellockabletoHSM600', 'HA', '个', '个', 820.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001298', '亮度旋钮', 'HA', '个', '个', 1142.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001925', '压力弹簧', 'HA', '个', '个', 59.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1008386', '滑板', 'HA', '个', '个', 328.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020740', '贴纸', 'HA', '袋', '袋', 26.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1008068', '电源盒', 'HA', '个', '个', 2548.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220567-D', 'SlitLampBP90014mm', 'HA', '台', '台', 234186.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220617', '升降台', 'HA', '个', '个', 42456.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020884', 'LED照明灯头', 'HA', '个', '个', 16692.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020885-EX', 'BQ900LED光源', 'HA', '个', '个', 12568.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020961', '清洁贴纸', 'HA', '包', '包', 422.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022103S', 'APS手柄', 'HA', '个', '个', 9752.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001263', '左边旋钮', 'HA', '个', '个', 536.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001590', '长镜片', 'HA', '个', '个', 3338.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220685-D', '裂隙灯显微镜', 'HA', '台', '台', 808411.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220745', '照明控制盒', 'HA', '个', '个', 2782.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220751-D', '桌面轨道', 'HA', '个', '个', 2226.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021610S', '照明头', 'HA', '个', '个', 15872.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1006468', '螺丝', 'HA', '个', '个', 59.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1002366', '弹簧', 'HA', '个', '个', 59.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023036S-ZZ', 'IlluminationheadBILED', 'HA', '个', '个', 23984.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023160S', '垂直驱动单元', 'HA', '个', '个', 10318.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023306S', '线缆固定', 'HA', '个', '个', 436.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802032', '应答器手柄', 'HA', '个', '个', 4627.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220768', '分光镜', 'HA', '个', '个', 46972.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-191704', '集液盒', 'LS', '个', '个', 310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-NFAI-TSEBOX', '眼底图像辅助软件', 'LS', '个', '个', 36000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-50940-001', 'OCT量化分析软件', 'OV', '个', '个', 83017.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205576', '耳背式助听器', 'OT', '个', '个', 20790.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.017-ZZ', 'Z8白内障手术包', 'ZO', '盒', '盒', 49990.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206566', '耳内式助听器', 'OT', '个', '个', 5440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-206610', '耳内式助听器', 'OT', '个', '个', 4500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205574', '耳背式助听器', 'OT', '个', '个', 13020.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221882', 'A-助听器', 'OT', '个', '个', 21840.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221885', 'A-助听器', 'OT', '个', '个', 13650.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221891', 'A-助听器', 'OT', '个', '个', 13650.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-890-01-019-09', '鼓风机', 'OT', '个', '个', 91.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.018', 'PI马达驱动板', 'ZO', '个', '个', 17215.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.032', '供电模块', 'ZO', '个', '个', 6127.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.024', 'CALIBRATIONTOOLTOPVIEW', 'ZO', '个', '个', 25677.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.025', 'HandpiececalibrationtoolZ8', 'ZO', '个', '个', 78782.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.035', '马达驱动器', 'ZO', '个', '个', 29178.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.036', '马达驱动', 'ZO', '个', '个', 26552.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.026', '粗调工具', 'ZO', '个', '个', 13617.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DDR3-8G', '内存', 'LS', '个', '个', 262.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.108-E', '飞秒手柄', 'ZO', '个', '个', 359866.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.140', 'KeyswitchcompleteZ2-Z6PP', 'ZO', '个', '个', 4921.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.179', 'Z8显示屏', 'ZO', '个', '个', 82797.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-VSLT', 'VO-VSLT', 'VO', '个', '个', 6262.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2MASSY225-AA', 'HEADBACKCOVERASSY', 'HV', '个', '个', 544.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192518', '注射适配器', 'OP', '个', '个', 192.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221015', '眼科光学生物测量仪', 'HA', '台', '台', 221514.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023034S', 'IlluminationheadBQLED', 'HA', '个', '个', 23984.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1008201', '滑动板', 'HA', '个', '个', 1523.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020262', '供电电源', 'HA', '个', '个', 16985.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802780S', '线缆O900', 'HA', '个', '个', 1513.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803152S', '前壳O900', 'HA', '个', '个', 6595.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820458S', '亮度控制板', 'HA', '个', '个', 8492.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000010', '螺丝', 'HA', '个', '个', 82.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300031', '9号油脂', 'HA', '个', '个', 2431.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022524S', '水平组件', 'HA', '个', '个', 50281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022612S', 'FlashcableforBXLED', 'HA', '个', '个', 26112.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020885', '照明头', 'HA', '个', '个', 24101.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021118-D', '桌面', 'HA', '个', '个', 12065.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400113', '额托', 'HA', '个', '个', 703.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1800753', '刺激光灯泡', 'HA', '个', '个', 1952.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803170', '操作单元', 'HA', '个', '个', 28259.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803200-EX', '900视野计下巴架', 'HA', '个', '个', 17717.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803250S', 'Swivelunit', 'HA', '个', '个', 33091.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803510', '投射器步进马达', 'HA', '个', '个', 1552.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001047S', '定位螺丝', 'HA', '个', '个', 1816.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1001434', '附件套装', 'HA', '个', '个', 664.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007301', '闪光单元', 'HA', '个', '个', 133916.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020335', '晶星控制杆', 'HA', '个', '个', 12739.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020749', '维修对准工具', 'HA', '个', '个', 8697.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021124', '模型眼', 'HA', '个', '个', 12387.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1008202', '连接板', 'HA', '个', '个', 4393.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803912', '电源', 'HA', '个', '个', 2636.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021085', '盖板', 'HA', '个', '个', 422.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021914', '灰色盖板', 'HA', '个', '个', 246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022052S-EX', '控制器', 'HA', '个', '个', 14496.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803550S', '红外照明单元', 'HA', '个', '个', 5300.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-131.21E', '弧形桌面（订制款）', 'DM', '个', '个', 15476.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-90-013.10E', '安装板', 'DM', '个', '个', 2870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-43793', 'ivue光谱仪', 'OV', '个', '个', 170257.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-48377-004', '描述控制板', 'OV', '个', '个', 20209.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48564-001', '功能程序软件', 'OV', '个', '个', 15655.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-ANGIOOCT-D', '光学相干断层扫描仪', 'OV', '台', '台', 897712.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-ISCAN', '眼科光学相干断层扫描仪', 'OV', '台', '台', 447603.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192540', 'Kitforinj./removalofsilic', 'OP', '个', '个', 1134.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-198825', '玻切套包', 'OP', '包', '包', 31797.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113101', '超乳手柄', 'OP', '个', '个', 39218.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113217', '冷超声超乳针头', 'OP', '个', '个', 2013.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113240-1', '针头', 'OP', '个', '个', 2078.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-127236', '迷你型适配器L', 'OT', '袋', '袋', 544.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113401', '针头扳手', 'OP', '个', '个', 2319.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.003.006', '飞秒眼科固体激光治疗仪', 'ZO', '台', '台', 3250000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-129017', '结膜压盘', 'OP', '个', '个', 8454.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-181006-D', 'IntegratedIVpoletrolley', 'OP', '个', '个', 63572.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-098', '参考电极', 'LKC', '个', '个', 37.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.071', '硬盘', 'ZO', '个', '个', 25054.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.077', 'LDV轮子', 'ZO', '个', '个', 4012.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.101-E', '快速扫描模组', 'ZO', '个', '个', 108933.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.119', 'CB4控制板', 'ZO', '个', '个', 233232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.126', 'POLARIZATIONROTATOR', 'ZO', '个', '个', 65651.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.144', '手柄保护帽', 'ZO', '个', '个', 219.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.014', 'LDV手柄线缆组', 'ZO', '个', '个', 21984.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.159-E', 'OCT关节臂', 'ZO', '个', '个', 217087.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BATTERY-13', '国产电池A13', 'LS', '个', '个', 2.50, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.169', '电源供应模组', 'ZO', '个', '个', 20425.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.020', '模拟I/O信号卡', 'ZO', '个', '个', 15192.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.021', '马达位置板', 'ZO', '个', '个', 70806.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.027', '负压电磁阀', 'ZO', '个', '个', 7295.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.062', 'LDV脚踏板Z型', 'ZO', '个', '个', 29178.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.104', 'Cable,Inside,CameraFORALL', 'ZO', '个', '个', 6419.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.121', '多轴内部连线', 'ZO', '个', '个', 9843.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.038', 'suctiontubeadaptergreenZ8', 'ZO', '个', '个', 350.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.112', '飞秒工具', 'ZO', '个', '个', 1371.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.154-E', '飞秒手柄', 'ZO', '个', '个', 359866.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.022', '数字卡', 'ZO', '个', '个', 5836.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.030', '电源', 'ZO', '个', '个', 6419.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.031', '供电模块', 'ZO', '个', '个', 14297.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.082', 'CameraHandpieceC1', 'ZO', '个', '个', 192091.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.089', '负压管接口', 'ZO', '个', '个', 1809.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.122', '多轴马达驱动', 'ZO', '个', '个', 27817.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.138', '关节臂', 'ZO', '个', '个', 560613.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.150-E', '激光腔', 'ZO', '个', '个', 797542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.040', 'CableDataloggerinterface', 'ZO', '个', '个', 1401.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.151', 'CableHarnessCavitySprint', 'ZO', '个', '个', 34236.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.177', '电源供应', 'ZO', '个', '个', 14881.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.520.001-E', 'LDV手柄', 'ZO', '个', '个', 276222.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022373', 'USB线缆', 'HA', '个', '个', 765.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300105', '插头', 'HA', '个', '个', 2489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023251S', '调整测量仪', 'HA', '个', '个', 644.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806270S', '控制板O600', 'HA', '个', '个', 565.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820462S', '刺激光控制板', 'HA', '个', '个', 5564.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023304S', 'USB集线器', 'HA', '个', '个', 2958.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803221', '步进马达组件', 'HA', '个', '个', 1903.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804500S', '背景光LED光源', 'HA', '个', '个', 8697.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400215', '背景光支架', 'HA', '个', '个', 11948.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220442', 'EyeSuite软件许可码', 'HA', '个', '个', 33591.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300299', '滑动盘组件', 'HA', '个', '个', 1259.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1024102S', '面板', 'HA', '个', '个', 870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0204004', '助听器保养仪-Ⅱ', 'LS', '个', '个', 1760.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CF-1-SYS', 'LS-CF-1-SYS', 'LS', '个', '个', 7480.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-SONYX898', 'SONY热敏打印机', 'LS', '个', '个', 5125.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SW-PACS', 'PACS系统', 'LS', '个', '个', 12870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48098-01', '光学相干断层扫描仪', 'OV', '台', '台', 789020.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.181', '控制板', 'ZO', '个', '个', 100763.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114101', '注吸手柄', 'OP', '个', '个', 9281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48577-D', '光学相干断层扫描仪', 'OV', '台', '台', 356267.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-800-43605-D', '测量头外壳', 'OV', '个', '个', 6387.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VR-EL-SET-COMPLETE-D', 'EyesiSlitLampSimulator', 'VR', '台', '台', 2803749.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-10.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-23.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-28.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-7760017', '检查台桌面', 'LU', '个', '个', 61765.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MD-2400CE', '标准玻切头', 'MD', '个', '个', 1319.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('NL-N005-2112-YY', 'LEAFPowerSupply', 'NL', '个', '个', 3152.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-115302-D', 'Diathermypencileraser', 'OP', '个', '个', 4786.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111001-D', '超声乳化仪', 'OP', '台', '台', 223570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178966', '耳内式助听器', 'OT', '个', '个', 32340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-201957', '数字中功率耳背式助听器', 'OT', '个', '个', 3870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-201977', '数字大功率耳背式助听器', 'OT', '个', '个', 4180.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113206', '20G迷你超乳针头30度', 'OP', '个', '个', 1476.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340835-ZZ', 'SLAtoBMBQ8mmor14mm', 'MR', '个', '个', 115892.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113250', '20Gflaredbentphacotip30degree', 'OP', '个', '个', 1829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114216', '直注吸针头', 'OP', '个', '个', 4115.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-214962', '耳背式助听器', 'OT', '个', '个', 7330.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.111', 'CableHarnessCavityTP200', 'ZO', '个', '个', 57773.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-117006', 'ResuableI/Acassette(2pcs)forR-EvoSmartE,etc.', 'OP', '个', '个', 6821.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-121010-D', '玻切模组', 'OP', '个', '个', 205044.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111001', '超声乳化仪', 'OP', '台', '台', 223570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114101-D', '注吸手柄', 'OP', '个', '个', 9281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-20-S-ZZ', 'Endoprobestraighttip,20G', 'MR', '个', '个', 264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('NL-90-3009-YY', 'LEAFChinaPowerCord', 'NL', '个', '个', 657.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-115101', '双极电凝线', 'OP', '个', '个', 2421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-115302', '电凝刷', 'OP', '个', '个', 4786.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.730.001', '激光冷却液', 'ZO', '个', '个', 700.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.007', '保护镜', 'ZO', '个', '个', 4863.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1801723', '皮带', 'HA', '个', '个', 401.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000827', '佳能相机适配器环', 'HA', '个', '个', 6267.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300648', '前额头带', 'HA', '个', '个', 463.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7021000', 'Mountingplate-for3rd', 'HA', '个', '个', 6999.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802199S', '电源', 'HA', '个', '个', 3661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803104', 'TOP检查策略软件', 'HA', '个', '个', 29577.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806120S', '触摸屏', 'HA', '个', '个', 22111.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220205', '清洁套装', 'HA', '个', '个', 1142.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220741', '照明供电电源', 'HA', '个', '个', 9543.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804702', '线缆', 'HA', '个', '个', 146.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806161S', '额托模组', 'HA', '个', '个', 16331.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000242', '轴承球', 'HA', '个', '个', 26.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7200660', '弥散光片', 'HA', '个', '个', 445.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3320109S', 'WheelfreerunningtoHSM600', 'HA', '个', '个', 761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7200032', '压平眼压计', 'HA', '组', '组', 51868.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220531', '压平眼压计', 'HA', '个', '个', 52009.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220552-ZZ', 'SlitLampBI900completewith', 'HA', '台', '台', 259949.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806150S', '应答器', 'HA', '个', '个', 2694.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806303S', 'LCD诊断线缆', 'HA', '个', '个', 885.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806306S', 'LCDGUI线缆', 'HA', '个', '个', 771.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221008-YY', 'ImagingModuleIM910forBQ900', 'HA', '个', '个', 166135.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7200033', '压平眼压计', 'HA', '组', '组', 50878.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220210-D', 'SlitLamp900BMLED', 'HA', '台', '台', 123690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182761', '耳内式助听器', 'OT', '个', '个', 11340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182763', '耳内式助听器', 'OT', '个', '个', 11340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-199-YY', 'topcover', 'LKC', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-011', 'ERG电极', 'LKC', '盒', '盒', 33195.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220633', '助手镜', 'HA', '个', '个', 49432.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220746', '亮度控制器', 'HA', '个', '个', 3163.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000499', '滑动板', 'HA', '个', '个', 820.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300118', '灯仓', 'HA', '个', '个', 1874.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803157S', '顶盖O900', 'HA', '个', '个', 2741.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803200', 'HeadsetforOctopus900', 'HA', '个', '个', 52829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SOFTBAND', '绝缘板', 'LS', '个', '个', 20.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-WN-TPLINK', 'wirelessnetworkadapter', 'LS', '个', '个', 54.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0604010', '小号一字起子', 'LS', '个', '个', 9.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1101005', '硬耳模用球形钻2.7mm', 'LS', '个', '个', 34.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1101012', '硬耳模用头形钻5.0mm', 'LS', '个', '个', 79.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-D1EPCB7001-AT', 'PCBASSY(ROB-350)', 'HV', '个', '个', 7291.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192704', '一次性管道组', 'OP', '个', '个', 642.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192818', '(5pcs/pkg)27Gstandardsingle-useendoilluminationprobe', 'OP', '个', '个', 1469.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114302', 'ASPIR.PROBEFORTWO-HANDSTECHNIQUE', 'OP', '个', '个', 5582.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192845', '25G一次性隔离广角多端口眼内照明光纤', 'OP', '个', '个', 1576.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-38050300B', 'ConeforKeratronscout', 'OP', '个', '个', 20416.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OSMG', '单面角镜', 'OR', '个', '个', 3260.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-13.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-27.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-8.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J2MASSY408-AA', '下巴架', 'HV', '个', '个', 5325.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-25.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-3022A00B202-A', 'TorqueHingeL', 'HV', '个', '个', 98.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-HS100-OCTA-1', '光学相干断层扫描仪', 'CA', '台', '台', 887653.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0910-000', 'KNOB,STAGELOCK', 'CA', '个', '个', 64.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0588-000', 'LED板', 'CA', '个', '个', 2108.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0129-000', '传感电路板', 'CA', '个', '个', 6710.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020524S', '晶星900外壳', 'HA', '个', '个', 2718.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022052S', '拍摄控制模组', 'HA', '个', '个', 19913.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023139S', '极化工具适配器', 'HA', '个', '个', 4072.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400379', '基座', 'HA', '个', '个', 540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803151S', '外壳O900', 'HA', '个', '个', 20357.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803610', '固定倾斜镜', 'HA', '个', '个', 7936.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803916', '电源模组', 'HA', '个', '个', 1669.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804401S', '背景光亮度测量板', 'HA', '个', '个', 2987.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804445', '开关', 'HA', '个', '个', 703.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820509S', '供电单元O900', 'HA', '个', '个', 8736.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000827-D', 'Adapterring', 'HA', '个', '个', 6267.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220139', 'EyeSuite软件', 'HA', '个', '个', 9840.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220146', '长助手镜', 'HA', '个', '个', 93358.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220316', '压平眼压计测量头', 'HA', '个', '个', 2401.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220374', 'DICOM注册码', 'HA', '个', '个', 18068.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220442-D', 'AutometedImportofBX900', 'HA', '个', '个', 33591.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220463', '前额支架', 'HA', '个', '个', 20288.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220756', '下巴纸', 'HA', '盒', '盒', 310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220765', '背景光支架', 'HA', '个', '个', 25360.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220384', 'T-Cone不含ToricPlanner', 'HA', '个', '个', 119577.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221024', '6m同轴线', 'HA', '个', '个', 3182.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221030', 'LS9005系手动版主机', 'HA', '台', '台', 277174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3320097S', 'BX配HSM600桌面', 'HA', '个', '个', 24101.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220720-D', 'EYESTAR900', 'HA', '个', '个', 1242807.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0201022-006', '新款干燥饼', 'LS', '袋', '袋', 20.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVO-G', '电脑', 'LS', '个', '个', 10249.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CA663CDN', 'PrinterCANONLBP663CDN', 'LS', '个', '个', 4896.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ADAPTER-CSO-1', '转接口', 'LS', '个', '个', 5019.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELL5090', '戴尔电脑', 'LS', '个', '个', 7971.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CA6780', '打印机', 'LS', '个', '个', 2107.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-19.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-266', '眼罩', 'LKC', '个', '个', 250.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-125', '大鼠电极500g(含针电极)', 'LKC', '包', '包', 15458.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-BS-F', '台式电生理BigShot闪光ERG', 'LKC', '包', '包', 589135.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-120', '小鼠电极(含针电极)', 'LKC', '包', '包', 16231.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-126', '大鼠电极550g(含针电极)', 'LKC', '包', '包', 15458.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-BS-FP-UV', '台式电生理BigShot闪光和图形ERG(UV刺激)', 'LKC', '包', '包', 687292.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-560-C0103-001', '手柄线缆', 'CR', '个', '个', 805.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-50195-001', '分光计', 'OV', '个', '个', 123889.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J0MASSY315-AB', '握把', 'HV', '个', '个', 88.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY020-AA', '前面板组件（壳子）', 'HV', '个', '个', 4100.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7501POW0001-A', 'SMPS(RPD-60A);RPD-60A,', 'HV', '个', '个', 1464.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-311.659.501', 'S1摇臂通用BM', 'ZO', '个', '个', 11714.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-174007', '数字大功率耳背式助听器', 'OT', '个', '个', 17800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192627', '27GENDIAT.BIPOLARPROBE5pc/pkg', 'OP', '个', '个', 1521.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-REPAIR', 'OP返厂维修', 'OP', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-20-S-D', 'EndoProbes,20Gauge.10pc/box', 'MR', '个', '个', 264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340835', 'BM/BQ专用裂隙灯适配器', 'MR', '个', '个', 115892.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340847', '用于HS型裂通用隙灯适配器', 'MR', '个', '个', 144859.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-006662', 'DISPLAYASSEMBLY', 'OK', '个', '个', 14289.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-O106', '主板', 'OK', '袋', '袋', 12606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OSJAG', '医用放大镜', 'OR', '个', '个', 6904.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-218368', '耳塞', 'OT', '包', '包', 105.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230276', 'A-助听器', 'OT', '个', '个', 18800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-BA580', 'VuPad(A/B-Scan)PortableA/B-scanTablet', 'SO', '个', '个', 317393.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-VUPAD-ZZ', '眼科A/B型超声诊断仪', 'SO', '台', '台', 535407.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-20334', '海绵障', 'WE', '袋', '袋', 111.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-311.701.201', '传感器帽', 'ZO', '袋', '袋', 8541.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-D1MASSY049-AA', 'OPERATIONPANAL', 'HV', '个', '个', 26837.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J2MASSY145-AA', '显微镜目镜组件', 'HV', '个', '个', 26751.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-EYESLHVHIS052', 'DICOM软件', 'HV', '个', '个', 13419.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-18.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-6.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-19.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-11.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-29.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-13.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-15.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-9.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-REPAIR', 'Ziemer返厂维修', 'ZO', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-640425', 'Merilas532激光安全护目镜', 'MR', '个', '个', 4661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OG3MS-2', '诊断三面镜,', 'OR', '个', '个', 3632.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-EARMOLD-SILICONE', '自制耳膜', 'OT', '个', '个', 180.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-128057', '防耵聍网罩', 'OT', '袋', '袋', 79.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-116008', 'WrenchforresuableI/AcassetteforR-EvoSmartE,', 'OP', '个', '个', 2956.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.041', 'LASIK手术包110um，10mm', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CS-100226502', 'nouse;usefor(LS-CSO980-3X,-5X)', 'CS', '个', '个', 4993.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-17.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-18.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1152-000', '透镜单元', 'CA', '个', '个', 9517.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221008', 'IM910影像模组', 'HA', '个', '个', 170404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221032', 'IM910EyeSuite影像功能', 'HA', '个', '个', 30065.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405112', '测试片推出器', 'CL', '个', '个', 536.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403190', '下巴架高度控制旋钮零组件', 'CL', '个', '个', 286.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401280', '同视机垂直偏差控制旋钮组件', 'CL', '个', '个', 447.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-266.10-3', 'SchreibtischRERAL9006', 'DM', '个', '个', 19412.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-D10-12', '控制电位器', 'DM', '个', '个', 2223.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP-900X500-D', 'DESKTOP900X500×ÀÃæ', 'LS', '个', '个', 0.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL13Column', 'KL1和KL3的升降柱', 'LS', '个', '个', 979.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL2Tabletop', 'KL2的桌面', 'LS', '个', '个', 353.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL5Tabletop', 'KL5的桌面', 'LS', '个', '个', 478.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-LIFTSW', '升降开关', 'LS', '个', '个', 14.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G9', '电脑', 'LS', '个', '个', 5809.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-550-C11IF-004', '软件工具', 'CR', '个', '个', 2684.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-600-C11PM-001-ZZ', '电源模组', 'CR', '个', '个', 2684.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-TONOVUE-ZZ', '非接触式眼压计', 'CR', '台', '台', 89404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-49880-001-ZZ', 'ssyEnclosureShuttle', 'OV', '个', '个', 4745.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-FC2720-D', '设备航空箱', 'EO', '个', '个', 6312.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-200-YY', 'bottomcover', 'LKC', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-80230101', '1/2MICROPHONE', 'IT', '个', '个', 11727.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-22.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-29.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-B101-7', '50MHZUBM探头', 'SO', '个', '个', 45212.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-BUAP-580-ZZ', 'VuPad(Ascan/Bscan/UBM/pachy-meter)', 'SO', '台', '台', 648374.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OASG-H', '医用放大镜', 'OR', '个', '个', 9534.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200669', '(nouse)useTR-200670', 'TR', '个', '个', 2683.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-130835', '防耵聍网罩', 'OT', '袋', '袋', 183.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-39861000B', '温度传感器', 'OP', '个', '个', 8193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-KIT-P', '光源组件', 'RH', '袋', '袋', 399413.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-41201', '绿色熔蜡500g', 'EG', '个', '个', 1609.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-40607', '打磨用砂纸', 'EG', '个', '个', 402.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-51503', '卤素灯泡，75W', 'EG', '个', '个', 1164.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-50104', 'UV线灯管(波长=460nM)', 'EG', '个', '个', 421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-75100', '切刀圆柱5.0mm', 'EG', '个', '个', 101.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-CELLCECK-20-1-D', '角膜内皮显微镜', 'KO', '台', '台', 330422.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-ATM200T-F240', 'OpenFramePowerSupply', 'KO', '个', '个', 3489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-CELLCHEKDPULS', '眼库角膜内皮镜', 'KO', '台', '台', 558218.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-24.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221868', 'A-助听器', 'OT', '个', '个', 33800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-B101-5-ZZ', 'UBM水域探头组件', 'SO', '个', '个', 181224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-B101-6-ZZ', '35MHZUBM探头.', 'SO', '个', '个', 35193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-B101-7-ZZ', '50MHZUBM探头', 'SO', '个', '个', 45212.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-VUMAXHD-U-575', 'VuMax-高清UBM主机（仅支持UBM）', 'SO', '台', '台', 581621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230285', 'A-助听器', 'OT', '个', '个', 9980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-REPAIR', 'DS返厂维修', 'DS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-20700', '硅胶耳模A/B胶', 'EG', '个', '个', 1664.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-50103', 'UV线灯管(波长=370nM)', 'EG', '个', '个', 421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-74200', '软抛光盘', 'EG', '个', '个', 66.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114301', 'IRR.PROBEFOR2-HANDSTECHNIQUCROZAFON', 'OP', '个', '个', 5582.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8004219', 'Microphonerefbase', 'IT', '个', '个', 7309.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-80418001', 'REFERENCEMICROPHONE', 'IT', '个', '个', 5863.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340843', 'BQ900用户保护过滤器', 'MR', '个', '个', 34760.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-REPAIR', 'OK返厂维修', 'OK', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70089', 'IQ577TxCellComputer', 'IR', '个', '个', 42733.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-87302', '检眼镜', 'IR', '个', '个', 227678.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-VOLK60DLENS-ZZ', 'VOLKOPTICAL60DLENS', 'IR', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023660S-D', 'EYESTAR900极化工具', 'HA', '个', '个', 28376.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********', 'BD/BC电源供应器抽屉', 'HA', '个', '个', 13881.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-096.14E', '电动牛眼支架', 'DM', '个', '个', 35247.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL1Tabletop', 'KL1的桌面', 'LS', '个', '个', 364.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-23G', '23G套管针', 'LS', '袋', '袋', 968.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DRAWER-66', '六六公司抽屉组件', 'LS', '袋', '袋', 165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GPU-RTX206012G', 'GraphicmodelRTX206012G', 'LS', '个', '个', 3417.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0401025', 'DREVEA+BIMPRESSION800G*2', 'LS', '个', '个', 528.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DT-LED900-66-D', 'DESKTOP-LED900-66-D', 'LS', '个', '个', 0.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-EG-30605', 'eggerLP/Hlacquerhard,500ml', 'LS', '个', '个', 4602.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-151161-D', '软件', 'DS', '个', '个', 190524.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-262', '充电电池', 'LKC', '个', '个', 1465.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-123', '大鼠电极300g(含针电极)', 'LKC', '包', '包', 15458.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-124', '大鼠电极350g(含针电极)', 'LKC', '包', '包', 15458.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-BS-FP', '台式电生理BigShot闪光和图形ERG', 'LKC', '包', '包', 619497.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-122', '大鼠电极250g(含针电极)', 'LKC', '包', '包', 15458.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-121', '大鼠电极200g(含针电极)', 'LKC', '包', '包', 15458.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-49879-001-ZZ', 'AssyEnclosureShuttleLeft', 'OV', '个', '个', 5045.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-VP6200-D', '灭菌盒', 'EO', '个', '个', 3050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-170ZMG-D', 'EO-OME170ZMG', 'EO', '个', '个', 43176.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SCP1E14A', '三桌面', 'FR', '个', '个', 4979.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-REPAIR', 'FR返厂维修', 'FR', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-TG-FRA-CAP10-R1', '65HS/HS-NE用控制电路板', 'FR', '个', '个', 7939.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-TGFRAMASTER01R1B', '控制电路板;HFMU0420', 'FR', '个', '个', 9129.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-EARMOLD-ACRYLIC', '自制耳膜', 'OT', '个', '个', 120.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-RHDE-1', 'Tearcheckexcl.pad&table', 'RH', '台', '台', 430518.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-9200-1512-1-D', '校准柱', 'SO', '个', '个', 671.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221886', 'A-助听器', 'OT', '个', '个', 13000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-20333', '海绵障', 'WE', '袋', '袋', 111.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-CELLCHEKD', '眼库角膜内皮镜', 'KO', '台', '台', 477295.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-CATA-MERGE', 'CATASOFTWARE(Orderw/Z8)**', 'ZO', '个', '个', 1156382.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-20331', '海绵障', 'WE', '袋', '袋', 111.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-B101-5', 'UBM水域探头组件', 'SO', '个', '个', 181224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-CLEAR-UPGRADE', 'LENTICULARSOFTWARE', 'ZO', '个', '个', 1999619.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.161-E', 'C1激光腔-以旧换新', 'ZO', '个', '个', 256236.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-11.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-25.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-9.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340845', 'BM900用户保护过滤器', 'MR', '个', '个', 34760.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-20-S', '20G眼内光纤', 'MR', '个', '个', 264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-100491', 'Merilas532α激光主机', 'MR', '台', '台', 378360.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-100490-D', 'Merilas532αgreenphotocoa-', 'MR', '个', '个', 378360.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340847-D', 'SLAforHStypeslitlamps,In', 'MR', '台', '台', 144859.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-640425-D', 'Merilas532激光安全护目镜', 'MR', '个', '个', 4661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-40900', '寒天', 'EG', '个', '个', 2147.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1310A00A001-A', '蚀刻传感器', 'HV', '个', '个', 18.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00J009-A', '裂隙灯轨道', 'HV', '个', '个', 30.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-5467-000', '相机线缆', 'CA', '个', '个', 15271.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10153', '光纤的连接螺母', 'IR', '个', '个', 745.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300034', 'Condensorassembled', 'HA', '个', '个', 6033.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803170-EX', 'O900显示操作单元（以旧换新）', 'HA', '个', '个', 11753.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022389-D', 'NOUSE,§ï¥ÎHA-7220753', 'HA', '个', '个', 1566.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023147S', 'Packagingtomeasuring', 'HA', '袋', '袋', 2636.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3320119S', 'BX配HSM600桌面板，无附件', 'HA', '个', '个', 16106.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220568-D', 'BQ90014mm', 'HA', '台', '台', 279036.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820504S', 'O600SSD存储磁盘，适用于第二代主板', 'HA', '个', '个', 6088.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220538-D', 'ImagingModuleIM600forBQ900', 'HA', '个', '个', 109361.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220567', '裂隙灯显微镜', 'HA', '台', '台', 234186.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220703-D', 'HSM600升降桌', 'HA', '台', '台', 38805.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221008-D', 'ImagingModuleIM910forBQ900', 'HA', '个', '个', 170404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221023', '3m同轴线', 'HA', '个', '个', 2147.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220460-D', '眼科光学生物测量仪', 'HA', '台', '台', 277174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300686S', 'Solenoidpre-assembled', 'HA', '个', '个', 6198.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-281.10', '桌面按键控制板', 'DM', '个', '个', 10161.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-M13E', '230V电源接线', 'DM', '个', '个', 984.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-12-001.44', 'HS-810控制板', 'DM', '个', '个', 10034.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-205.20E', '置物盒', 'DM', '个', '个', 19783.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-681.10', 'HS-2010桌面（左手台用）', 'DM', '个', '个', 9844.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CAG680', 'PRINTER-CAG680printer', 'LS', '个', '个', 2278.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL24Column', 'KL2和KL4的升降柱', 'LS', '个', '个', 1093.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.203', 'CavityupgradeC1toC2', 'ZO', '个', '个', 107065.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.208', 'Lasermonitoringsystemto', 'ZO', '个', '个', 71973.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-91-193', '传感器', 'LKC', '个', '个', 3449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-051', '信号放大器', 'LKC', '个', '个', 90174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-052', '连接器', 'LKC', '个', '个', 10821.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-048', '老鼠/猫通道操纵器', 'LKC', '个', '个', 51399.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-PT2200DW', 'PRINTERPTCP2200DW', 'LS', '个', '个', 4328.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-PowerSW', '电源开关', 'LS', '个', '个', 18.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL5Column', 'KL5的升降柱', 'LS', '个', '个', 1093.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-46551-001', 'XR/Assy¡ARefArmMNTRTVue', 'OV', '个', '个', 25160.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-MLINAK31.6B', '前后移动电机', 'FR', '个', '个', 7145.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7560IAPT653-A', '开关电源', 'HV', '个', '个', 3025.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY013-AA', 'A8Zmotorassy', 'HV', '个', '个', 1191.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-CRT8800SMPS', 'CRT8800SMPS', 'HV', '个', '个', 5467.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-DSLR-ADAPTER', 'DSLR接口', 'HV', '台', '台', 45087.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HDR-7000-D', '验光头', 'HV', '台', '台', 82928.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8002875', 'FELTSETFORLID015', 'IT', '个', '个', 1048.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-BAA10', 'BAA10BATTERYADAPTER', 'IT', '个', '个', 2733.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-NSPC', '角膜内皮显微镜', 'KO', '台', '台', 268009.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1200MP0A301-B', '轴承套', 'HV', '个', '个', 351.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1220A00J016-C', '固定轴', 'HV', '个', '个', 73.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HL-303-20P', 'Burian-AllenERGelectrodes', 'HL', '个', '个', 22543.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A206-A', '传感器', 'HV', '个', '个', 26.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1200MP0J079-A', '旋转固定旋钮', 'HV', '个', '个', 95.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M013-A', '外壳', 'HV', '个', '个', 391.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M015-A', 'X移动左边盖板', 'HV', '个', '个', 84.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HNT-1', '非接触式眼压计', 'HV', '台', '台', 88358.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-000864', '存储电容', 'OK', '个', '个', 3230.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-005593', 'PROTEKPM100-14A工具', 'OK', '个', '个', 2763.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-O101', '程序板', 'OK', '个', '个', 2858.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-191704', '集液盒', 'OP', '个', '个', 225.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-128905', '氙灯光源适配器', 'OP', '个', '个', 2348.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192518-1-D', '注射适配器', 'OP', '个', '个', 193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192620', '20G电凝笔', 'OP', '个', '个', 1208.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113205', '20G迷你超乳针头15度', 'OP', '个', '个', 1476.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114203', '弯注吸针头', 'OP', '个', '个', 5877.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-393130H', '电源板', 'OP', '个', '个', 4213.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14995-D', 'IQ532SLA光纤射频识别', 'IR', '个', '个', 23724.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30172-D', 'GL激光护目镜', 'IR', '个', '个', 4165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31202', '电源供应Gen2.(RevH)', 'IR', '个', '个', 55061.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31586', 'GLX控制印制电路板', 'IR', '个', '个', 32540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-REPAIR', '维修收费', 'HA', '次', '次', 20.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220468', 'Headrestlightgrey(RAL7035)', 'HA', '个', '个', 14693.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220669-ZZ', 'AdapterKitFunusModule300', 'HA', '个', '个', 2636.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0203001-004', 'PVC吹气球', 'LS', '个', '个', 25.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DELL-LAT5501', '电脑', 'LS', '个', '个', 10811.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DELL-T630-KIT', '戴尔电脑', 'LS', '个', '个', 45045.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-EPIX-CAMERA-HS100', 'HS100校准用工业相机', 'LS', '个', '个', 4955.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-LD-HL6312G', '发光二级管', 'LS', '个', '个', 421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA01-Y-KL3-D', '电动桌', 'LS', '个', '个', 4345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-APPLE-MYD82', '电脑', 'LS', '个', '个', 11105.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-INJECTOR-5ML', '5ML注射器', 'LS', '个', '个', 2.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-JZLP-TISSUE', '镜头湿巾纸', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-KNIFE-15', '眼科手术刀', 'LS', '个', '个', 90.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-ASUSVB15S', '电脑', 'LS', '个', '个', 5501.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELL3670', '电脑', 'LS', '个', '个', 7722.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELL7040', '电脑', 'LS', '个', '个', 5792.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCHOLDER-KL4', '电动桌配的计算机架', 'LS', '个', '个', 952.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G6', '电脑', 'LS', '个', '个', 5786.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G7', '惠普电脑', 'LS', '个', '个', 5588.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVOM720S-D', '联想电脑', 'LS', '个', '个', 6545.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-400-44868-A', 'ToolOCTSignal', 'OV', '个', '个', 4026.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-41117-003', '电源供应板', 'OV', '个', '个', 5385.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-44748', '前节模组', 'OV', '个', '个', 54793.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-530-42734-A', '下巴架模块', 'OV', '个', '个', 134.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OPR-120', '医用放大镜', 'OR', '个', '个', 8015.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192527-D', '5000CST硅油注射器', 'OP', '个', '个', 962.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-097', '图形监视器', 'LKC', '个', '个', 31489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-005', '参考电极', 'LKC', '个', '个', 20.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-017', '皮肤清洁纸', 'LKC', '盒', '盒', 879.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-018', 'VEP电极', 'LKC', '个', '个', 534.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-394936000', '触摸屏', 'OP', '个', '个', 3489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OAIY', '医用放大镜', 'OR', '个', '个', 6246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-120M', '医用放大镜', 'OR', '个', '个', 4321.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-101068', '接收器', 'OT', '个', '个', 488.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-169201', '手提箱', 'OP', '个', '个', 4487.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-108107', '受话器', 'OT', '个', '个', 224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114210', '数字中功率耳背式助听器', 'OT', '个', '个', 38800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-115017', '外置受话器', 'OT', '个', '个', 1107.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120322', '开放声管', 'OT', '个', '个', 25.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120510', '接收器', 'OT', '个', '个', 239.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124582', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125723', '方向性麦克风', 'OT', '个', '个', 278.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-126164', '耳内式助听器', 'OT', '个', '个', 24800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-100713', '数字大功率耳背式助听器', 'OT', '个', '个', 14800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103-23-300-00', '数字大功率耳背式助听器', 'OT', '个', '个', 37521.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114088', '数字大功率耳背式助听器', 'OT', '个', '个', 38800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1100MA0A301-B', 'AFHeadrestbody', 'HV', '个', '个', 1345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7202LA065VB-A', '旧款LCD屏', 'HV', '个', '个', 6136.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7202LGRG065-D', '液晶显示屏', 'HV', '个', '个', 4406.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-79130010001-A', 'TPH(热敏打印头)', 'HV', '个', '个', 820.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2EPCB3002-AA', '电路板', 'HV', '个', '个', 292.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2EPCB3002-AB', '传感器电路板', 'HV', '个', '个', 274.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113241', '喇叭口针头', 'OP', '个', '个', 1829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR6003-AB', 'HarnessHRKAZmotor', 'HV', '个', '个', 848.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EPCB7001-AC', '印制电路板组件(HRKAM-310)_V5.01.00A', 'HV', '个', '个', 7756.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EPCB7001-BC', '电路板HRKAM-310', 'HV', '个', '个', 6511.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HCP-7000', '视力表投影仪', 'HV', '个', '个', 19927.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HNT-1-D', '非接触式眼压计', 'HV', '台', '台', 88358.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J4MASSY196-AB', '开关电源组件', 'HV', '个', '个', 3447.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-MOMASSY023-AB', '马达组件', 'HV', '个', '个', 896.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-P-HZ-HDR-MATE', 'HDRmate软件', 'HV', '套', '套', 11182.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8001796', 'NOEPRENESEALINGPARTS015FRONT', 'IT', '个', '个', 393.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8002712', 'SIDEINSULATIONBOTTOM015L', 'IT', '个', '个', 2541.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8501746', 'B&K4231Calibrator94+110dB', 'IT', '个', '个', 37066.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-NSP9900-II-D', '角膜内皮显微镜', 'KO', '台', '台', 281705.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-NSPC-D', '角膜内皮显微镜', 'KO', '台', '台', 268009.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30058', '电源模块', 'IR', '个', '个', 9393.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30699', 'SLA光纤', 'IR', '个', '个', 23724.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31722', 'TX电源', 'IR', '个', '个', 17993.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA00-Y-KL1', '科以康电动桌', 'LS', '个', '个', 3014.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA01-Y-KL3', '科以康电动桌', 'LS', '个', '个', 4345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA01-Y-KL5', '电动升降桌', 'LS', '个', '个', 2232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MICROSOFT-PRO6', '电脑', 'LS', '个', '个', 11068.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-THINKPAD-E', '电脑', 'LS', '个', '个', 5277.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-2110NDA', '打印机', 'LS', '个', '个', 5125.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-ASK300', '打印机', 'LS', '个', '个', 4211.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CA623-CDW', '打印机', 'LS', '个', '个', 4620.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SLM-7E', '眼科裂隙灯显微镜检查仪', 'LS', '个', '个', 28314.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0201023-002', '新款干燥饼专用干燥罐', 'LS', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0701019', '西门子规格大号软耳塞', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0703035', '进口3.5*2声管', 'LS', '个', '个', 15.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-47599-002', 'iVue主机控制板', 'OV', '个', '个', 6888.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-50353-002', 'XY扫描控制板', 'OV', '个', '个', 13419.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-40531', '光耦', 'OV', '个', '个', 64.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-620-47782-001', '左右眼识别传感器（单）', 'OV', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-47051-EPROM', 'XYScanboardEPROM', 'OV', '个', '个', 671.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-DBDGO080-3008', 'powersupply', 'OV', '个', '个', 1858.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-MDS-300APB12A', 'iScan电源', 'OV', '个', '个', 1196.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0847-000', 'GearIdleForFocusUnit', 'CA', '个', '个', 12.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0886-000', '物镜镜头套', 'CA', '个', '个', 390.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2516-000', '弹簧', 'CA', '个', '个', 116.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0571-000', '马达轴承', 'CA', '个', '个', 49.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA3-0103-030', '下巴纸固定钉', 'CA', '个', '个', 69.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2517-000', '弹簧', 'CA', '个', '个', 139.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0536-000', 'LinkQRM', 'CA', '个', '个', 60.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0943-000', '按键', 'CA', '个', '个', 36.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1512-030', 'FrameDrivingpart', 'CA', '个', '个', 1054.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-4147-000', '主机左边外壳', 'CA', '个', '个', 1008.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0098-000', '红外滤片', 'CA', '个', '个', 563.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0920-000', '弹簧', 'CA', '个', '个', 46.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-2398-060', '光电传感器', 'CA', '个', '个', 1552.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1486-000', '按钮', 'CA', '个', '个', 49.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-2785-080', '遮挡片', 'CA', '个', '个', 156.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG0-0425-000', '成像镜', 'CA', '个', '个', 2996.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH5-4238-020', '热敏打印机', 'CA', '个', '个', 3602.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0402-000', '遮挡盖片', 'CA', '个', '个', 1085.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG0-0430-000', 'LENSUNITRELAYB', 'CA', '个', '个', 8413.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH2-0771-000', '马达', 'CA', '个', '个', 463.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH5-3356-060', '供电单元', 'CA', '个', '个', 16459.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0703041', '进口3*2防潮声管', 'LS', '个', '个', 64.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-900X500-66-D', '桌面', 'LS', '个', '个', 366.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA90D', '佳能单反相机', 'LS', '个', '个', 8778.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-IVUE100', '电脑', 'LS', '个', '个', 6834.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COUPLANT', '耦合剂', 'LS', '个', '个', 45.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-FG-EU307A-1', 'PCIE接口卡', 'LS', '个', '个', 138.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-INJECTOR-10ML', '10ML注射器', 'LS', '个', '个', 2.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IR-11242', '裂隙灯接头座', 'LS', '个', '个', 110.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ISTAND-NEW-D', 'iVue支架', 'LS', '个', '个', 150151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-LENOVO-MX250', '电脑', 'LS', '个', '个', 8366.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA01-Y-KL4', '科以康电动桌', 'LS', '个', '个', 4224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA01-Y-KL4-D', '升降台', 'LS', '个', '个', 4224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-METALPLATE', '铁板', 'LS', '个', '个', 110.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-NITROGENTTUBE-D', '氮气管', 'LS', '个', '个', 473.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-7Z02692', '能量表', 'LS', '个', '个', 14789.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-APPLE-IMAC', '电脑', 'LS', '个', '个', 10250.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HUAWEI-PAD', 'PC-HUAWEI-matepadpro12.68+256GB', 'LS', '个', '个', 6264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVO-V', '联想电脑', 'LS', '个', '个', 4442.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-SCREEN', '显示器', 'LS', '个', '个', 1199.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-THINKCENTRE', '联想IdeaPad15s', 'LS', '个', '个', 5296.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-THINKPAD-D', '电脑', 'LS', '个', '个', 6435.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-4', '打印机配件', 'LS', '个', '个', 500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CA621CW', '打印机', 'LS', '个', '个', 11686.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CP305D', 'CP305D打印机', 'LS', '个', '个', 6578.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP2029', 'HP2029打印机', 'LS', '个', '个', 770.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP4729', '打印机', 'LS', '个', '个', 1544.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP6230', '打印机', 'LS', '个', '个', 875.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HPDJ1112', 'HP打印机', 'LS', '个', '个', 220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SM01', 'Chinrestfor65-SCand65-SB', 'LS', '个', '个', 550.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SOFA', '沙发', 'LS', '个', '个', 2188.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-THINKPADX13', '电脑', 'LS', '个', '个', 10991.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-USB-3M-UGGREEN', '绿联数据线', 'LS', '个', '个', 39.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-X01', '抽屉盖', 'LS', '个', '个', 11.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-XD-BG380T', '膜瓣镊', 'LS', '个', '个', 11959.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-XD-BG472T', '眼内膜铲', 'LS', '个', '个', 4784.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YT2GA', '电动桌', 'LS', '个', '个', 3986.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH6-3055-000', '扁平线缆', 'CA', '个', '个', 124.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9105-000', '电容', 'CA', '个', '个', 4440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9495-000000', '连接线', 'CA', '个', '个', 135.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-CLEAR-MERGE', 'clear软件', 'ZO', '个', '个', 1133296.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9009-000', '氙灯闪光灯', 'CA', '个', '个', 9193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9102-000', 'powersupplyunitswitching', 'CA', '个', '个', 31838.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9181-000', 'USBcable', 'CA', '个', '个', 1104.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0085-000', '主板', 'CA', '个', '个', 15489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0279-000', '左右眼传感器', 'CA', '个', '个', 664.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0295-060', 'DrivingunitFO', 'CA', '个', '个', 9410.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0340-000', '曝光控制板', 'CA', '个', '个', 475.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0289-040', '观察镜单元', 'CA', '个', '个', 10432.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0351-000', '基座USB接口板', 'CA', '个', '个', 614.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0585-000', '传感器组件', 'CA', '个', '个', 347.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2585-000', '凸轮', 'CA', '个', '个', 122.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0291-000', '固定器', 'CA', '个', '个', 514.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0561-000', '齿轮', 'CA', '个', '个', 55.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0810-000', '二号齿轮', 'CA', '个', '个', 24.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2666-000', '手柄皮带轮固定针', 'CA', '个', '个', 436.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0334-000', '弹簧挡片', 'CA', '个', '个', 394.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0889-000', '镜头盖', 'CA', '个', '个', 151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0919-000', '弹簧', 'CA', '个', '个', 39.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-1273-000', '外壳', 'CA', '个', '个', 212.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0811-000', '基座齿轮', 'CA', '个', '个', 26.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0813-000', '长基座', 'CA', '个', '个', 42.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0923-000', '按钮', 'CA', '个', '个', 35.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0543-000', 'QRM弹簧', 'CA', '个', '个', 193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-KL-LIFTC', '升降柱', 'LS', '个', '个', 1367.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0809-000', '齿轮', 'CA', '个', '个', 23.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9584-000', 'PrinterforTX-20/20P', 'CA', '个', '个', 3533.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9639-000', '开关电源', 'CA', '个', '个', 6336.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH8-2584-020', 'CCD单元', 'CA', '个', '个', 5142.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH9-0254-000', '旋转编码器', 'CA', '个', '个', 1799.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1011-000', 'PCBassymainTX-20forIOPonlyCCD', 'CA', '个', '个', 12089.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1200-000', 'PCBASSYmainTX20P', 'CA', '个', '个', 17512.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0818-000', '齿轮', 'CA', '个', '个', 30.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0863-000', '弹簧线圈', 'CA', '个', '个', 95.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0916-000', '头托', 'CA', '个', '个', 938.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1041-000', '调焦马达传动轴', 'CA', '个', '个', 15.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1386-000', '电容', 'CA', '个', '个', 3873.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1848-000', '主板', 'CA', '个', '个', 6919.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-2786-060', '遮挡片', 'CA', '个', '个', 197.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0009-000', '手柄外壳组件', 'CA', '个', '个', 467.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-2557-000', '控制板', 'CA', '个', '个', 5752.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3011-000', '马达', 'CA', '个', '个', 792.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH5-4253-000', 'CRT显示器', 'CA', '个', '个', 18305.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9704-000', 'EXTERNALSYNCHROCABLE', 'CA', '个', '个', 3641.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9943-000', 'cameraassyCCDB/W', 'CA', '个', '个', 13950.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0128-000', '主板', 'CA', '个', '个', 15819.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0944-000', '弹簧', 'CA', '个', '个', 46.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0945-000', '阻断编码器', 'CA', '个', '个', 47.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1050-000', '基座', 'CA', '个', '个', 155.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1457-000', 'LCD屏幕外壳背板', 'CA', '个', '个', 338.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-4642-000', '轴承', 'CA', '个', '个', 1291.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0123-000', '劈裂线棱镜单元', 'CA', '个', '个', 1757.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0212-000', 'LCD屏幕外壳前面板', 'CA', '个', '个', 1552.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BT2-9114-000', '轴联耦合器', 'CA', '个', '个', 761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-0057-000', '80D医用相机', 'CA', '个', '个', 22313.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-5020-000', 'Prismunitspli', 'CA', '个', '个', 7012.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6343-000', '橡皮眼', 'CA', '个', '个', 19685.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-CR-2-PLUS-AF', '数字眼底照相机', 'CA', '个', '个', 308880.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-CX-1-D', '数字眼底照相机', 'CA', '个', '个', 425344.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0131-000', '电位计', 'CA', '个', '个', 1008.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB3006-AA', '下巴架信号板', 'HV', '个', '个', 200.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY011-AA', 'M0PDLedassy', 'HV', '个', '个', 2270.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A330-B', '传感器', 'HV', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1200MP0A303-B', '轴承支持器', 'HV', '个', '个', 181.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1220A00A301-A', '额托支架升降挡片', 'HV', '个', '个', 401.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY036-AA', '电机组件', 'HV', '个', '个', 1923.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-30.00', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M2EPCB0005-AE', 'PCBANTM-1P', 'HV', '个', '个', 9845.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M2MASSY049-AA', '打印机模组', 'HV', '个', '个', 1141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A001224-C', '下巴架', 'HV', '个', '个', 98.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00A531-A', 'A8000盖帽', 'HV', '个', '个', 20.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M004-G', '下颌托板', 'HV', '个', '个', 79.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M016-A', 'X移动右边盖板', 'HV', '个', '个', 84.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2020MP0B001-A', '打印纸固定', 'HV', '个', '个', 9.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-3012A000011-C', 'XZ_SR_ASSY', 'HV', '个', '个', 1312.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A328-A', 'AF电机外壳', 'HV', '个', '个', 17.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR3006-AC', 'HarnessHRKACRPIassy', 'HV', '个', '个', 22.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHARS005-AA', 'HarnessHRKACRmotor', 'HV', '个', '个', 715.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-CRT8800-CABLE', 'communicationline', 'HV', '个', '个', 1762.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HRK-7000A-D', '自动电脑验光仪', 'HV', '台', '台', 81156.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HRK-9000A', '验光仪', 'HV', '台', '台', 147463.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HS-5000', '裂隙灯显微镜', 'HV', '台', '台', 56144.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HS-7000', '裂隙灯显微镜', 'HV', '台', '台', 81013.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB4002-AB', '用户界面印制电路板组件', 'HV', '个', '个', 1613.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M2MASSY005-AA', 'M2XYZ-ASSY', 'HV', '个', '个', 9565.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M008-A', '中间左的盖板', 'HV', '个', '个', 496.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2040A00J009-A', '固定旋钮橡胶套', 'HV', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7501POW0010-B', 'SMPSDelta100W', 'HV', '个', '个', 1220.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2EPCB4002-AD', '电路板', 'HV', '个', '个', 594.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1200MP0A302-A', '下吧架接头', 'HV', '个', '个', 213.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00A219-B', '外壳', 'HV', '个', '个', 34.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00M009-A', '中间右的盖板', 'HV', '个', '个', 496.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000MPRM023-B', 'M0head-restbutton', 'HV', '个', '个', 104.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2MASSY211-AD', '新打印机组件', 'HV', '个', '个', 1201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0174-000', 'LENSUNITFIELDCX-1', 'CA', '个', '个', 6649.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0171-000', '孔隙镜组', 'CA', '个', '个', 15788.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0177-000', 'LENUNITREPLAYU', 'CA', '个', '个', 8846.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0295-000', '聚焦驱动单元', 'CA', '个', '个', 16938.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0226-000', 'ARM', 'CA', '个', '个', 498.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3018-020', '相机连接线缆', 'CA', '个', '个', 3479.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH3-3288-000', '氙灯闪光灯', 'CA', '个', '个', 16205.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-REPAIR', '佳能维修', 'CA', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XB7-2300-302', 'UnithexagonM3NO', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XD2-1100-132', '垫圈', 'CA', '个', '个', 14.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XD2-1100-242', 'E-RingSUSD=2.35NO', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9331-000', 'LCD液晶屏', 'CA', '个', '个', 14131.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9333-000', '供电单元', 'CA', '个', '个', 11961.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0319-000', '操作手柄', 'CA', '个', '个', 11359.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000516', '裂隙灯手柄皮套', 'HA', '个', '个', 189.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220096-D', '眼科光学生物测量仪', 'HA', '台', '台', 391058.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7221017', '近视防控软件', 'HA', '个', '个', 59056.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401203', '玻璃镜子', 'CL', '个', '个', 456.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0204002', '超声波清洗机', 'LS', '个', '个', 308.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0703005', '硬耳膜挂钩', 'LS', '个', '个', 2.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0703011', '瑞声达硬耳模耳勾', 'LS', '个', '个', 2.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0703024', 'R形成型声管3.5*2塑料锁扣', 'LS', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-HP600G3', '电脑', 'LS', '个', '个', 6380.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-RTVUE', 'RTVue电脑', 'LS', '个', '个', 10250.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-T4900', '电脑', 'LS', '个', '个', 5720.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CORD-404A-501', '三相电源线', 'LS', '个', '个', 36.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO980-3X-ADAPTER', '裂隙灯显微镜', 'LS', '个', '个', 58655.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO980-5X', '裂隙灯显微镜', 'LS', '台', '台', 59225.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A5EPCB3004-AA', '左右眼传感器板', 'HV', '个', '个', 155.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY002-AB', 'A8HRcoreassy', 'HV', '个', '个', 3039.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-CRT8800CARTONBOX', 'CRT8800CARTONBOX', 'HV', '个', '个', 2947.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-CRT-8800-D', '综合验光台', 'HV', '台', '台', 63122.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000MPRM019-E', '底盖', 'HV', '个', '个', 342.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2010MPRM001-B', '开盖支架', 'HV', '个', '个', 89.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7202LGRG065', '液晶显示器', 'HV', '个', '个', 4406.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-75600HVA130-A', '开关电源', 'HV', '个', '个', 6851.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-21.50', '预装式非球面后房人工晶状体盒', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EPCB0005-AC', '主板', 'HV', '个', '个', 7829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-3002A000016-A', '轴承', 'HV', '个', '个', 40.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-720500A0002-A', '照明', 'HV', '个', '个', 2236.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SL-FVA', '视觉功能分析仪', 'SL', '台', '台', 225586.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-COMBO-1-D', '视觉功能分析仪', 'TR', '台', '台', 1073497.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-Z1031-A', '主板', 'TR', '个', '个', 90474.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-Z1121', '适配套件', 'TR', '个', '个', 21470.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-147223', '耳内式助听器', 'OT', '个', '个', 6780.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150-20-101-00', '电话适配器', 'OT', '个', '个', 1782.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150259', '数字中功率耳背式助听器', 'OT', '个', '个', 6780.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150914', '数字大功率耳背式助听器', 'OT', '个', '个', 6780.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120127', '开关旋钮', 'OT', '个', '个', 22.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120214', '模具弯曲3L喇叭线', 'OT', '个', '个', 201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124579', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.410.003', '移植架接口', 'ZO', '个', '个', 19365.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.008', '线缆', 'ZO', '个', '个', 10249.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192301', '一次性后节玻切手柄', 'OP', '个', '个', 2300.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192303', '23G玻切头', 'OP', '个', '个', 2675.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-166511', '机顶盒', 'OT', '个', '个', 78.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.124', 'CableSetHandpieceZAxisforZ4Z6Z6ppZ8', 'ZO', '个', '个', 35141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.146', '过滤棉', 'ZO', '个', '个', 6589.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124587', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125374', '外壳', 'OT', '个', '个', 76.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125922-1', '大功率耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-129859', '蓝牙伴侣', 'OT', '个', '个', 3600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-129901', '数字大功率耳背式助听器', 'OT', '个', '个', 7514.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-131206', '数字中功率耳背式助听器', 'OT', '个', '个', 15600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-131669', '数字中功率耳背式助听器', 'OT', '个', '个', 19800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133791-1', '低音耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-147215', '耳内式助听器', 'OT', '个', '个', 8680.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-147559', 'Nera2放大器', 'OT', '个', '个', 7533.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192803', '3通标准一次性光学组件', 'OP', '个', '个', 885.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192807-X', '23G玻切套包', 'OP', '个', '个', 5490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192808-X', '玻切光纤', 'OP', '个', '个', 5490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192809-D', '23G广角照明光纤', 'OP', '个', '个', 1449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.001', '负压环', 'ZO', '个', '个', 3634.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.010', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.014', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.030', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.730.002', '电池', 'ZO', '个', '个', 293.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MODULE-SYNC', '电脑模块系统', 'LS', '个', '个', 4556.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OD400UHW', '摄像系统', 'LS', '个', '个', 6200.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-192540', '一次性硅油注射气推管路', 'LS', '个', '个', 547.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-JUNO', 'USB适配器', 'LS', '个', '个', 43048.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELLP5820X', '电脑', 'LS', '个', '个', 21753.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP880G5', 'HP电脑', 'LS', '个', '个', 8542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-THINKPAD-E14', '电脑', 'LS', '个', '个', 5353.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-2', '打印机', 'LS', '个', '个', 2000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP179FNW', '打印机', 'LS', '个', '个', 4328.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-IP8780', '打印机', 'LS', '个', '个', 2620.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SLITLAMP-S350', '裂隙灯显微镜', 'LS', '个', '个', 20651.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SURGICAL-SYS', '高频手术系统', 'LS', '个', '个', 424710.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TONOMETER', '眼压计', 'LS', '个', '个', 62548.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-UT120C', '万用表', 'LS', '个', '个', 171.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-CELLCHEKC', '角膜全景扫描显微镜', 'KO', '台', '台', 2129102.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-B101-6', '35MHZUBM探头', 'SO', '个', '个', 35193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OJKY-21', '医用放大镜', 'OR', '个', '个', 5511.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-100555-ZZ', 'Merilas577shortpulseyellow', 'MR', '台', '台', 497323.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340967-ZZ', 'UPFpassiveforBQ900', 'MR', '个', '个', 41525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-23-S-ZZ', 'Endoprobes,23GSMAconnector', 'MR', '个', '个', 2489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340983-ZZ', 'UPFactiveforLeica', 'MR', '个', '个', 114208.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-20-A-ZZ', 'Endoprobeanglestip,20G', 'MR', '个', '个', 1825.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-031026', '主板', 'OK', '个', '个', 7716.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-L411', '升举机构', 'OK', '个', '个', 18745.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113301', '硅胶套', 'OP', '个', '个', 233.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192808-1', '25G标准照明光纤', 'OP', '个', '个', 1449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-39352800B', '超乳接口带线', 'OP', '个', '个', 2035.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-394830000', '延长线', 'OP', '个', '个', 1235.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-39650300B', '数据接口线', 'OP', '个', '个', 4829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-396534000', '前盖板', 'OP', '个', '个', 2898.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OG3MA', '医用镜子', 'OR', '个', '个', 4537.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-STD-LR', '医用放大镜', 'OR', '个', '个', 2442.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMRA-PRP-165-2', '医用镜子', 'OR', '个', '个', 9074.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMRA-S', '医用放大镜', 'OR', '个', '个', 8015.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMRA-S-2', '医用镜子', 'OR', '个', '个', 8416.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221877', 'A-助听器', 'OT', '个', '个', 20800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-115022', '外置受话器', 'OT', '个', '个', 1107.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-117468', '编程线', 'OT', '个', '个', 463.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-118025', '编程仪器', 'OT', '台', '台', 2540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124124', '声管', 'OT', '个', '个', 25.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124583', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124588', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124964', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125921-1', '大功率耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-128003', '麦克风网罩', 'OT', '个', '个', 264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-131424', '电池仓门', 'OT', '个', '个', 41.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133792-1', '低音耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-143836', '数字中功率耳背式助听器', 'OT', '个', '个', 25600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-143843', '数字中功率耳背式助听器', 'OT', '个', '个', 25600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-146183', '数字大功率耳背式助听器', 'OT', '个', '个', 25600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150050', '数字中功率耳背式助听器', 'OT', '个', '个', 7656.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-164237', 'OPNCIC/IICPROGADAPterMINI', 'OT', '个', '个', 758.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-174005', '数字大功率耳背式助听器', 'OT', '个', '个', 17800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-384-20-023-01', '编程线', 'OT', '个', '个', 281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-390-01-151-00', '适配器', 'OT', '个', '个', 325.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-549-86-420-00', '外壳', 'OT', '个', '个', 55.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-570-07-320-00', '耳塞', 'OT', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-622-00', '外置受话器', 'OT', '个', '个', 2310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SP-312-MF', '电池', 'SP', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-COMBO-2-D', '视觉功能分析仪', 'TR', '台', '台', 923655.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-Z1130-M', '视标驱动马达', 'TR', '个', '个', 21176.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-SQUAD160', 'VOLK镜SQUAD160', 'VO', '个', '个', 10950.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-VHRWF', '镜子', 'VO', '个', '个', 11129.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.003.002', '飞秒眼科固体激光治疗仪', 'ZO', '台', '台', 4099780.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.163', 'OCTfiberforZ8', 'ZO', '个', '个', 6052.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0579-000', '模拟板', 'CA', '个', '个', 5170.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1061-000', '拍摄按键控制板', 'CA', '个', '个', 898.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3134-000', 'PCBASSYBASE1/F', 'CA', '个', '个', 12278.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH2-0767-030', '步进马达组件', 'CA', '个', '个', 826.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9707-000', '马达', 'CA', '个', '个', 1069.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-3476146900', 'brakehook', 'OV', '个', '个', 201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-52701-001', '模制左盖', 'OV', '个', '个', 989.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-45721-002-ZZ', 'ImageSensorboard(use', 'OV', '个', '个', 2281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-44373-D', '脚踏', 'OV', '个', '个', 4294.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-46338', '3D/GCC升级包', 'OV', '个', '个', 51438.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2040A00M007-A', '喷嘴盖', 'HV', '个', '个', 34.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-9050ALLE201-B', '贴膜', 'HV', '个', '个', 85.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0932-000', '手柄盖', 'CA', '个', '个', 59.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0580-000', '马达控制板', 'CA', '个', '个', 1015.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0609-000', 'CBM8Cableunitwithconnector', 'CA', '个', '个', 636.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1840-000', '基板', 'CA', '个', '个', 907.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1847-000', 'PRISMUNIT,SPLITLINE', 'CA', '个', '个', 8050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6713-000', 'pin工具', 'CA', '个', '个', 232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-WT2-5124-000', '线缆夹座', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF0-5162-020', '滤片', 'CA', '个', '个', 1699.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0031-000', '劈裂线棱镜单元', 'CA', '个', '个', 5309.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3164-000', 'SwitchunitExposure', 'CA', '个', '个', 1656.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3178-000', '操作手柄单元', 'CA', '个', '个', 14290.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH1-3370-000', '电容', 'CA', '个', '个', 2683.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1846-000', 'DRIVEUNIT,FOCUSCR2', 'CA', '个', '个', 9302.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-5011-000', '相机罩', 'CA', '个', '个', 3367.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2450-020', '控制面板(左侧)', 'CA', '个', '个', 1394.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2760-000', '主板', 'CA', '个', '个', 21591.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3019-000', '操作单元面板', 'CA', '个', '个', 2834.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6658-000', '工具C3', 'CA', '个', '个', 83649.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-CY9-4023-003', '擦镜纸', 'CA', '个', '个', 436.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-2307-020', '氙灯组件', 'CA', '个', '个', 20958.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-4189-000', 'PCBAVariableresistor', 'CA', '个', '个', 2183.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6633-000', '工具D', 'CA', '个', '个', 105471.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-RK-F2', '全自动验光角膜曲率计', 'CA', '个', '个', 85388.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-WG8-5723-000', '光电传感器', 'CA', '个', '个', 89.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-WT2-5112-000', '线缆绷带', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0183-000', '驱动单元', 'CA', '个', '个', 4042.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0285-000', '物镜', 'CA', '个', '个', 44548.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0584-000000', 'Potenion板', 'CA', '个', '个', 272.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-5074-000', '线', 'CA', '个', '个', 2002.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH9-0487-050', '轨迹球', 'CA', '个', '个', 4932.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0178-000', 'blackdotunit', 'CA', '个', '个', 7788.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1174-000', 'joystickunit', 'CA', '个', '个', 22899.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1719-000', '内外眼切换单元', 'CA', '个', '个', 9939.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-2348-050', '基座控制板', 'CA', '个', '个', 4764.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6637-000', '工具Q1', 'CA', '个', '个', 157606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6712-000', '工具S', 'CA', '个', '个', 91154.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-CR-2-AF', '数字眼底照相机', 'CA', '个', '个', 275569.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-HS100-OCTA-D', '光学相干断层扫描仪', 'CA', '台', '台', 899837.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6535-000', 'BNC线缆', 'CA', '个', '个', 511.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-VD7-3652-001', '主板保险丝', 'CA', '个', '个', 62.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XB6-2300-302', 'SETSCREWDOUBLEpoint', 'CA', '个', '个', 19.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XG3-5010-403', '轴承', 'CA', '个', '个', 1714.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XZ9-0451-000', '旋钮', 'CA', '个', '个', 1282.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-TX-20-D', '非接触式眼压计', 'CA', '个', '个', 127680.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403481', '齿轮传动装置', 'CL', '个', '个', 572.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405031', '后侧导板', 'CL', '个', '个', 606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405066', '四星型旋钮', 'CL', '个', '个', 179.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405106', '镜片架', 'CL', '个', '个', 54.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405117', '下巴架滑轨', 'CL', '个', '个', 188.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405126', '呼吸挡板', 'CL', '个', '个', 679.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401040', 'DustCover', 'CL', '个', '个', 858.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6402009', 'chart', 'CL', '个', '个', 357.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405077', '旋钮', 'CL', '个', '个', 232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6502007', '幻灯片', 'CL', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-1146016', '灯泡', 'CL', '个', '个', 429.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-686.10AE', '转化器', 'DM', '个', '个', 6795.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-405.20E', '附加的负载的高度', 'DM', '个', '个', 58269.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-492.14E', '手动牛眼支架', 'DM', '个', '个', 20989.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-905.20E', '手动移动椅子扶手', 'DM', '个', '个', 9082.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-600-C0202-001', '电源适配器', 'CR', '个', '个', 2684.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0611-001-ZZ', '(NOUSE)UseCR-TONOVUE-ZZ', 'CR', '台', '台', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-ASMITWI013', '马达线缆', 'CV', '个', '个', 686.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OG3MIA', '医用放大镜', 'OR', '个', '个', 4321.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OJKPY-25', '医用放大镜', 'OR', '个', '个', 6199.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2EPCB7002-AB', 'PCBASSYHRKM-310MAINB/DVersion5', 'HV', '个', '个', 6185.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY008-AA', 'A8Ymotorassy', 'HV', '个', '个', 1198.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY042-AA', 'A8backcasemiddleassy', 'HV', '个', '个', 635.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-CRT-8800', '综合验光台', 'HV', '台', '台', 63122.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HNT-7000-D', '眼压计', 'HV', '台', '台', 86277.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1300MP0E001-B', 'ChartPlate', 'HV', '个', '个', 753.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000A00J403-B', '盖板', 'HV', '个', '个', 78.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-2000MPRM021-A', 'M0startbutton', 'HV', '个', '个', 104.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR6001-AB', '电机', 'HV', '个', '个', 844.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR6004-AA', '下巴架电机', 'HV', '个', '个', 715.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY021-AA', 'A8_AUTO-PRINTER-ASSY', 'HV', '个', '个', 2796.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HCP-7000-D', '视力表投影仪', 'HV', '个', '个', 19927.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HRK-7000A', '自动电脑验光仪', 'HV', '台', '台', 81156.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EHAR5006-AC', '外部照明LED', 'HV', '个', '个', 199.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB3004-AC', 'PI传感器组件', 'HV', '个', '个', 76.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8EHAR0033-AA', 'HarnessMBCN28toPRTpower', 'HV', '个', '个', 117.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A8MASSY014-AA', 'A8Xmotorassy', 'HV', '个', '个', 1191.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-E2EHAR5001-AB', 'HARNESS(LAMPLED)', 'HV', '个', '个', 183.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HRT-7000', '综合检查台', 'HV', '个', '个', 77829.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HS-5000-LED', '裂隙灯显微镜', 'HV', '台', '台', 72640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HS-5500', '裂隙灯显微镜', 'HV', '台', '台', 56421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-D1EPCB4001-AD', '按钮板', 'HV', '个', '个', 2086.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HOCT-1-ZZ', 'Opticalcoherencetomography', 'HV', '个', '个', 496492.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB7001-AD', '主板', 'HV', '个', '个', 6272.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB8001-AC', '界面印制电路板(NTIF-100)', 'HV', '个', '个', 1894.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY039-AA', 'X轴马达组件', 'HV', '个', '个', 1045.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-MO-XL', '传感器支架', 'HV', '个', '个', 41.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-Z1EPCB0001-AD', 'USB线', 'HV', '个', '个', 677.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB7001-AA', '主板', 'HV', '个', '个', 5827.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY001-AA', '光学组件', 'HV', '个', '个', 48607.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY002-AA', '喷气嘴组件', 'HV', '个', '个', 1460.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M2EHAR0024-AE', 'HarnessM2Ymotor', 'HV', '个', '个', 1165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-P-HZ-CLS02', '验光仪模型眼', 'HV', '个', '个', 15029.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-Z1EHAR0011-AB', 'USB连接线', 'HV', '个', '个', 278.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-Z1EPCB0001-AB', '操纵按键板', 'HV', '个', '个', 677.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-J0MASSY070-AA', '操纵杆组件', 'HV', '个', '个', 1819.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0MASSY028-AB', '马达组件', 'HV', '个', '个', 1372.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M0EPCB4002-AC', '用户面板', 'HV', '个', '个', 1728.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-MOMASSY023-AA', '马达组件', 'HV', '个', '个', 896.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-REPAIR', '海威驰返厂维修', 'HV', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-Z1EHAR0012-AB', '连接线', 'HV', '个', '个', 146.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2476-020', '调节旋钮', 'CA', '个', '个', 1525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2933-000', '右侧控制面板', 'CA', '个', '个', 2606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-5018-000', '物镜', 'CA', '个', '个', 44158.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-HS100-OCTA', '光学相干断层扫描仪', 'CA', '台', '台', 899837.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-TX-20P', '非接触式眼压计', 'CA', '个', '个', 148005.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XB1-3300-606', '螺丝钉', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XD1-2100-309', '固定垫片', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3125-000000', '顶板', 'CA', '个', '个', 11108.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3171-000', '相机线缆', 'CA', '个', '个', 4151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11081', 'SLX能量供应', 'IR', '个', '个', 13978.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11244', '光纤螺母盖', 'IR', '个', '个', 1067.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14030', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 4332.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30791', 'GL加热板印制加热电路板', 'IR', '个', '个', 5185.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31441', 'GL激光探头连接器', 'IR', '个', '个', 1905.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65206', 'ASSY,CABLE,KEYSWITCH', 'IR', '个', '个', 6226.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14997', '间接检眼镜光纤组件', 'IR', '个', '个', 31539.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-2150023', '摩擦垫圈', 'CL', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-5606332', '16倍物镜', 'CL', '个', '个', 13223.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401206', 'BlueFilter', 'CL', '个', '个', 282.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401751', '电位器', 'CL', '个', '个', 391.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2665-000', '底座滑轮', 'CA', '个', '个', 448.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-400-C0622-001', 'TonoVueCTEjig', 'CR', '个', '个', 23617.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-031027', '主板', 'OK', '个', '个', 7553.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113228-D', '19G双锥体超乳针头15度', 'OP', '个', '个', 2174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113230', '20G超乳针头', 'OP', '个', '个', 1965.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8105-5701-00', 'Combi7000主板', 'LU', '个', '个', 38590.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MF-ART', 'PERFECTDRYLUX工具', 'MF', '个', '个', 512.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-384-20-015-01', '3号编程线', 'OT', '个', '个', 446.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('NL-N005-9010-D', '激光光凝仪', 'NL', '台', '台', 340827.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('NL-N006-9200-D', '间接检眼镜', 'NL', '台', '台', 419153.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMVGL', '医用放大镜', 'OR', '个', '个', 5949.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OPY-18', '医用放大镜', 'OR', '个', '个', 5786.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103090', '数字大功率耳背式助听器', 'OT', '个', '个', 21800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103091', '数字大功率耳背式助听器', 'OT', '个', '个', 11800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192808-D-1', '25G标准照明光纤', 'OP', '个', '个', 7242.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-39313000I', '电源板', 'OP', '个', '个', 4224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-396170000', '蠕动泵负压感应板', 'OP', '个', '个', 3735.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150907', '数字大功率耳背式助听器', 'OT', '个', '个', 8680.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103-23-100-00', '数字中功率耳背式助听器', 'OT', '个', '个', 19305.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103-42-200-00', '数字大功率耳背式助听器', 'OT', '个', '个', 19602.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-108402', '麦克风', 'OT', '个', '个', 2280.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-115018', '外置受话器', 'OT', '个', '个', 1107.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OL1PLTF', '医用镜子', 'OR', '个', '个', 12822.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMRA-WF', '医用放大镜', 'OR', '个', '个', 8015.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-570-07-350-00', '耳塞', 'OT', '个', '个', 18.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-571-05-370-00', '耳钩', 'OT', '个', '个', 50.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-580-10-370-08', '程序按钮', 'OT', '个', '个', 165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-623-00', '外置受话器', 'OT', '个', '个', 2310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-360-10-870-00', '音量按钮', 'OT', '个', '个', 198.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-390-01-180-05', '编程线', 'OT', '个', '个', 643.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-538-10-120-07', '电池仓门', 'OT', '个', '个', 23.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-543-20-560-01', '防耵聍网罩', 'OT', '个', '个', 222.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-570-07-322-00', '耳塞', 'OT', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-570-07-352-00', '耳塞', 'OT', '个', '个', 18.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-670-00', '外置受话器', 'OT', '个', '个', 2640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-639-01-010-04', '开关', 'OT', '个', '个', 495.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120509', '受话器', 'OT', '个', '个', 490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-120511', '麦克风', 'OT', '个', '个', 338.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-123328', '麦克风网罩', 'OT', '个', '个', 264.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125751', '方向性麦克风', 'OT', '个', '个', 219.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125923-1', '大功率耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125925-1', '开放式耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-128225', '大功率耳模工具', 'OT', '个', '个', 761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-100640', '数字中功率耳背式助听器', 'OT', '个', '个', 9801.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103-42-300-00', '数字大功率耳背式助听器', 'OT', '个', '个', 10692.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103652', '放大器', 'OT', '个', '个', 9301.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-104-23-100-00', '数字中功率耳背式助听器', 'OT', '个', '个', 37521.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-ACRYLIC', '硬耳模', 'OT', '个', '个', 120.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.510.002', '负压环', 'ZO', '个', '个', 48807.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.039', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10784', '灯泡', 'IR', '个', '个', 1744.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10842', '裂隙灯接头座', 'IR', '个', '个', 135542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11076', '前面板', 'IR', '个', '个', 45415.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11228', '转换开关', 'IR', '个', '个', 3075.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11242', '眼压计接头座', 'IR', '个', '个', 6256.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11242-D', '眼压计R型接头座', 'IR', '个', '个', 6256.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-G-30146', '气液交换管', 'LS', '个', '个', 342.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IK-502.130.75', '电脑桌', 'LS', '个', '个', 769.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IR-10511', '智能钥匙', 'LS', '个', '个', 18.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-WH-V-1', '超生雾化器', 'LS', '个', '个', 7272.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-398100', '主板', 'OP', '个', '个', 64202.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-X02', '抽屉', 'LS', '个', '个', 44.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-400-42686', '校准工具', 'OV', '个', '个', 4383.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('BF-890-60-803-00', 'NOEPRENESEALINGPARTS015R', 'BF', '个', '个', 12146.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-50696-003', '电脑', 'OV', '个', '个', 45087.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-43756', '电机模组', 'OV', '个', '个', 5099.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-47512-001', 'XR光谱仪组件', 'OV', '个', '个', 201281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-49125-001', '前节模组', 'OV', '个', '个', 13419.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-510-40281-A', '滑动底板', 'OV', '个', '个', 1062.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-530-43352-002', '镜头盖', 'OV', '个', '个', 201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-535-45676-001', '聚焦马达模组', 'OV', '个', '个', 4683.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-560-44011-A', '传感器', 'OV', '个', '个', 792.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-560-45672-001', '线缆', 'OV', '个', '个', 403.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-40509', '电源', 'OV', '个', '个', 4710.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101624-D', 'LaserModuleinkl.EndoKey', 'DS', '个', '个', 484646.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101730-1.6-E', '手柄（Exchange）', 'DS', '个', '个', 42339.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0523-000', '喷气气缸单元', 'CA', '个', '个', 6807.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0581-000', '按键板', 'CA', '个', '个', 1050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1042-000', '主板', 'CA', '个', '个', 10873.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1636-000', '下巴架升降单元', 'CA', '个', '个', 6174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY3-5003-000', 'DrivingunitF', 'CA', '个', '个', 8811.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-6635-000', '工具R', 'CA', '个', '个', 9888.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-CX-1', '数字眼底照相机', 'CA', '台', '台', 425344.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-TX-20', '非接触式眼压计', 'CA', '个', '个', 127680.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-ANGIOGRAPHY', '软件', 'CA', '个', '个', 166443.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA0-4718-000', '镜头盖', 'CA', '个', '个', 151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2660-000', '手柄锁紧器', 'CA', '个', '个', 214.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2664-000', '手柄半球基座', 'CA', '个', '个', 297.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XA1-1140-256000', 'SCREWPANHEADM14X2NC', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2452-030', 'CCD相机单元', 'CA', '个', '个', 4937.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405006', '旋钮', 'CL', '个', '个', 232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405028', '旋钮', 'CL', '个', '个', 268.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405032', 'FilterHolder', 'CL', '个', '个', 83.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405043', '后侧导板', 'CL', '个', '个', 606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-1146018', 'Bulb6VLAMP', 'CL', '个', '个', 42.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-1287808', '卡簧', 'CL', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6402690', '闪光器控制板', 'CL', '个', '个', 713.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403214', '下巴支架', 'CL', '个', '个', 107.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401190', '锥环', 'CL', '个', '个', 3169.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405064', '锥形垫片', 'CL', '个', '个', 268.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405071', '向心锁', 'CL', '个', '个', 141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-REPAIR', '同视机维修', 'CL', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-5806000', '手持式压平眼压计', 'CL', '台', '台', 47982.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403128', '腮托螺杆', 'CL', '个', '个', 250.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405065', '半径垫圈', 'CL', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405105', '操作环', 'CL', '个', '个', 83.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-D05-14', '桌面', 'DM', '个', '个', 32067.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-M053E', '裂隙灯预安装件', 'DM', '个', '个', 2826.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-M054E', '裂隙灯预安装件', 'DM', '个', '个', 2572.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-103.10E', 'CPACP-6E型投影仪适配器', 'DM', '个', '个', 1365.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-13-684.11', '轴承', 'DM', '个', '个', 4297.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-14-656.10E', 'HaagStreit下巴架适配器', 'DM', '个', '个', 2604.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-392.14E', '左手支架', 'DM', '个', '个', 21116.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-18-446.14E', 'LED台灯', 'DM', '个', '个', 2540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-595.40', 'PLC模块', 'DM', '个', '个', 4271.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-340.25E', '2-仪器旋转桌子', 'DM', '台', '台', 47449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-101.10E', 'CP690，CP7型投影仪适配器', 'DM', '个', '个', 1778.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-413.27E', '左手位检查台', 'DM', '台', '台', 310808.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-596.40', 'PLC模块', 'DM', '个', '个', 7494.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-390.25E', '装镜片的旋转抽屉（左手位）', 'DM', '个', '个', 13218.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-092.14E', '综合验光仪手动托架', 'DM', '个', '个', 20989.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-324.14E', '综合验光仪连接适配器', 'DM', '个', '个', 1873.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-530-C0631-004', '底座盖', 'CR', '个', '个', 1342.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-REPAIR', '方舟维修', 'CR', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C1101-011', '免散瞳眼底照相机', 'CR', '台', '台', 196182.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0102-001', '方舟下巴支架', 'CR', '个', '个', 22141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0102-001-D', '方舟下巴支架', 'CR', '个', '个', 22141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0102-001-ZZ', '方舟下巴支架', 'CR', '个', '个', 22141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-HNT-7000', '眼压计', 'HV', '台', '台', 86277.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C1101-011-ZZ', '免散瞳眼底相机', 'CR', '台', '台', 196182.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-600-N00PA-001-D', '电源适配器', 'CR', '个', '个', 1861.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-MAIA-SYSTEM', '黄斑完整性评估仪', 'CV', '台', '台', 1058467.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-MAIA2', 'AXMARTL001P&Tcalibrationtool', 'CV', '个', '个', 19500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-REPAIR', 'MAIA返厂维修', 'CV', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-ATMITEL008', '限位控制板', 'CV', '个', '个', 4149.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-151161', '软件注册码', 'DS', '个', '个', 190524.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-33281', '工具箱', 'DS', '个', '个', 92616.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-CELLCHEK-20-D', 'Cellchek-20-D', 'KO', '台', '台', 330422.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-CELLCHEKC-D', '角膜内皮显微镜', 'KO', '台', '台', 2129102.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8011526', 'FELTSETFORLID015', 'IT', '个', '个', 1048.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8001801', 'NOEPRENESEALINGPARTS015L', 'IT', '个', '个', 393.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-1813KC-1033-D', 'USB密钥', 'KO', '个', '个', 11068.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8001802', 'SIDEINSULATIONBOTTOM015R', 'IT', '个', '个', 472.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8002713', 'SIDEINSULATIONBOTTOM015FRON', 'IT', '个', '个', 1886.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-003905', '能量测量板', 'OK', '个', '个', 7986.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-1912EKA0011', '瓶子', 'KO', '个', '个', 2254.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-128815', '电话转接器', 'OT', '个', '个', 1800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133254', '数字大功率耳背式助听器', 'OT', '个', '个', 8980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133793-1', '低音耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-146341', '数字中功率耳背式助听器', 'OT', '个', '个', 40800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150900', '数字中功率耳背式助听器', 'OT', '个', '个', 6080.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-107774', '数字中功率耳背式助听器', 'OT', '个', '个', 32800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114049', '数字中功率耳背式助听器', 'OT', '个', '个', 23800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114056', '数字大功率耳背式助听器', 'OT', '个', '个', 25800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-114773', '电池仓门', 'OT', '个', '个', 41.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124590', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124595', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-HRXA', '眼科内窥镜', 'EO', '个', '个', 40192.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192301-D', '20G玻切头', 'OP', '个', '个', 2300.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192302-1', '25G玻切头', 'OP', '个', '个', 3156.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192315-D', '25G一次性双玻切', 'OP', '个', '个', 3621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192623-1', '23G眼内电凝表', 'OP', '个', '个', 1111.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192625-1', '25G双极电凝笔', 'OP', '个', '个', 1134.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192816-1', '一次性隔离广角多端口眼内照明光纤', 'OP', '个', '个', 1469.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-39350700A', '输出连接器', 'OP', '个', '个', 4562.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-KIT', '光源组件', 'RH', '个', '个', 9985.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-9200-1512-3C-ZZ', 'A-ScanCylinders20.0mm', 'SO', '个', '个', 3355.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10484', '810nm激光护目镜', 'IR', '个', '个', 3200.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14989', '大光斑光纤', 'IR', '个', '个', 33792.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31200', 'GL主板印制电路板', 'IR', '个', '个', 12882.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-77022-D', '显示面板', 'IR', '个', '个', 39578.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-87300', '激光间接检眼镜', 'IR', '个', '个', 220223.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-12757', '70292M3.5紧固螺丝', 'IR', '个', '个', 1342.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65451', '光纤检查板', 'IR', '个', '个', 4935.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70300', '扫描激光传输装置', 'IR', '台', '台', 445054.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-12167', '脚踏', 'IR', '个', '个', 10735.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-446.14E', 'LED照明灯', 'DM', '个', '个', 5038.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-NETVUEPRO-NAS', 'NAS存储服务器', 'LS', '个', '个', 16830.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-113301', '3.0-3.2切口的套袖', 'LS', '个', '个', 77.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-113308', '输液套管', 'LS', '盒', '盒', 103.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELL7090MT', '电脑', 'LS', '个', '个', 6036.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELLT5820', '电脑', 'LS', '个', '个', 20501.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-KEYBOARDM', '键盘', 'LS', '个', '个', 154.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVOM415', '电脑', 'LS', '个', '个', 7260.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-THINKPADNEWS2', '电脑', 'LS', '个', '个', 7859.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PLATE-66', '通用型铁板', 'LS', '个', '个', 55.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CAG3810', '打印机', 'LS', '个', '个', 1540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPL805', 'EPL805打印机', 'LS', '个', '个', 2365.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP254NW', '打印机', 'LS', '个', '个', 3075.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HPM154A', '打印机', 'LS', '个', '个', 1936.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TWOWAY', '三通管道', 'LS', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR3012-AA', '限位马达', 'HV', '个', '个', 170.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3MASSY190-AD', '红外照明模组', 'HV', '个', '个', 2542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-USB-CABLE', 'USB线', 'LS', '个', '个', 50.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-Y67-2476-020', '亮度调节板', 'LS', '个', '个', 412.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-42030-A', '下巴架升降马达', 'OV', '个', '个', 3019.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-50829-ZZ', 'IsolationBox100-240V150VA', 'OV', '个', '个', 9393.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-44373', '脚踏', 'OV', '个', '个', 4294.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200583-2', '橡皮套', 'TR', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200585', '下颌托盖板', 'TR', '个', '个', 403.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-398521000', '外壳', 'OP', '个', '个', 3301.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-511013', '气液交换泵', 'OP', '个', '个', 7201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OG3MHD-10', '医用镜子', 'OR', '个', '个', 5129.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200595', 'i-TraceDICOMSoftwareUPGRADE', 'TR', '个', '个', 29521.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-Z1135', '地形图校准装置', 'TR', '个', '个', 10198.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-147145', '数字耳内式助听器', 'OT', '个', '个', 20600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-170347', '数字中功率耳背式助听器', 'OT', '个', '个', 9880.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-38070000B', '线缆', 'OP', '个', '个', 8244.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OAYA', '医用放大镜', 'OR', '个', '个', 6246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-20M', '医用放大镜', 'OR', '个', '个', 2912.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-222', '医用放大镜', 'OR', '个', '个', 2326.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OG3MSA-2-IR', '医用镜子', 'OR', '个', '个', 6509.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-20', '医用镜片', 'OR', '个', '个', 2326.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-HM', '医用放大镜', 'OR', '个', '个', 2326.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-STDM', '医用镜子', 'OR', '个', '个', 3006.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-300AP', 'A超角膜测厚扫描仪', 'SO', '台', '台', 103257.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-28', '医用放大镜', 'OR', '个', '个', 2326.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-28M', '医用放大镜', 'OR', '个', '个', 2912.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-STD', '医用放大镜', 'OR', '个', '个', 2326.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220760', '连接线', 'HA', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103-40-100-00', '数字中功率耳背式助听器', 'OT', '个', '个', 12474.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124596', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124597', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-VG3NF', 'VOLK镜VG3NF', 'VO', '个', '个', 5457.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.003.008-D', '眼科飞秒激光治疗仪', 'ZO', '台', '台', 4273532.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.103.130', '板层角膜移植功能码', 'ZO', '个', '个', 175705.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.410.002', '角膜移植架', 'ZO', '个', '个', 120065.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.099', 'CardSelfAdjustingSlowScan', 'ZO', '个', '个', 14174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125755', '方向性麦克风', 'OT', '个', '个', 219.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125926-1', '开放式耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-130962', '数字大功率耳背式助听器', 'OT', '个', '个', 20800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-166072', '电池仓', 'OT', '个', '个', 40.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-170355', '数字中功率耳背式助听器', 'OT', '个', '个', 9880.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133191', '数字大功率耳背式助听器', 'OT', '个', '个', 8380.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-143830', '数字中功率耳背式助听器', 'OT', '个', '个', 18502.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-144287', '数字中功率耳背式助听器', 'OT', '个', '个', 29800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-190461', '数字中功率耳背式助听器', 'OT', '个', '个', 8480.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150048', '数字中功率耳背式助听器', 'OT', '个', '个', 20600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-190467', '数字中功率耳背式助听器', 'OT', '个', '个', 6980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101624', '激光模组', 'DS', '个', '个', 484646.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-151252', '连接线', 'DS', '个', '个', 2977.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-150259', 'USB扩展坞', 'DS', '个', '个', 1070.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-150951', '移动底座基板', 'DS', '个', '个', 18417.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EV-V000-012A', '眼罩', 'EV', '个', '个', 619.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-45200', '曲头镊子', 'EG', '个', '个', 197.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-471.40', 'PLC模块电源', 'DM', '个', '个', 3207.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-C30.14E', 'HS2010控制面板', 'DM', '个', '个', 5980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0201-C12-D', '免散瞳眼底照相机', 'CR', '台', '台', 154986.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0802-001-D', '升降桌', 'CR', '个', '个', 1677.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-LG001', '二极管激光防护眼镜', 'EO', '个', '个', 4318.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200HRA', '内窥镜(照明and影像)20G-直头', 'EO', '个', '个', 49655.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-546010000', '电磁阀', 'OP', '个', '个', 1990.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-EA', '眼科内窥镜', 'EO', '个', '个', 63927.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-HRVA', '眼科内窥镜', 'EO', '个', '个', 73685.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME200SMAHRXA', 'straight,19Glaserendoscope,', 'EO', '个', '个', 68661.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-230SMA-XA', '眼科内窥镜', 'EO', '个', '个', 85151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63SA2', 'SUPPORTFOR63DC', 'FR', '个', '个', 4128.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65HSP1E20A-C', '控制面板', 'FR', '个', '个', 11717.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65Z2', '写字工作台', 'FR', '个', '个', 28541.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-BAA312', 'EARMOLD-ACRYLIC', 'IT', '个', '个', 2733.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-1813KC-1033', 'USB加密狗', 'KO', '个', '个', 11068.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-30170', 'SL控制器840650000-a', 'OK', '个', '个', 2611.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113203', '19G超乳针头30度', 'OP', '个', '个', 2013.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8109210', '声阻抗仪', 'IT', '台', '台', 163351.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-NSP9900-II', '角膜内皮显微镜', 'KO', '台', '台', 281705.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14985', '激光间接检眼镜光纤', 'IR', '个', '个', 23724.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14994-D', '532SLAJumper', 'IR', '个', '个', 28382.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30172', '532激光护目镜', 'IR', '个', '个', 4165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021446-EX', '相机', 'HA', '个', '个', 11128.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007123', '电源供应模组', 'HA', '个', '个', 12563.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007129', '呼吸挡板', 'HA', '个', '个', 644.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007722', '主线缆', 'HA', '个', '个', 178.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022449', '固定基座', 'HA', '个', '个', 1820.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802398', '控制器中继B板', 'HA', '个', '个', 6253.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803112', '动态视野程序包', 'HA', '个', '个', 83362.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-450.41', 'HS2010的主板', 'DM', '个', '个', 9543.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-092.14E', '右手支架', 'DM', '个', '个', 21116.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-233.20E', '附件设备', 'DM', '个', '个', 3349.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-301.20E', '折射/检查单元', 'DM', '台', '台', 176936.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-105.10E', 'ACP8拓普康投影仪适配器', 'DM', '个', '个', 1905.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-594.40', 'PLC模块', 'DM', '个', '个', 7875.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-081.14E', '检眼镜座', 'DM', '个', '个', 6383.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-H03.14E', '自动托架', 'DM', '个', '个', 8193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-M05E', '裂隙灯预安装件', 'DM', '个', '个', 3290.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-31-019.14E', 'HS1010的升高柱', 'DM', '个', '个', 15972.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-9000006306', '桌面包装', 'DM', '个', '个', 4860.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0205009-002', '挂绳', 'LS', '个', '个', 9.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0303002-002', '双耳听筒', 'LS', '个', '个', 60.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0701020', '西门子规格中号软耳塞', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0701021', '西门子规格小号软耳塞', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0801008-001', '真耳分析仪', 'LS', '个', '个', 33284.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ADAPTER-CSO', '转接口', 'LS', '个', '个', 4993.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BATTERY-A675', '进口电池A675', 'LS', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMEAR-EOSR', '相机', 'LS', '个', '个', 20978.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA6DII', '相机', 'LS', '个', '个', 13098.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA7D-D', '相机', 'LS', '个', '个', 9567.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-HP400G4', '惠普电脑', 'LS', '个', '个', 6435.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-HP400G5', '电脑', 'LS', '个', '个', 5547.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CORD-401-503', '二项电源线', 'LS', '个', '个', 36.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0301016', '扩管钳', 'LS', '个', '个', 581.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-300ZMG', '10K视频适配器', 'EO', '个', '个', 43176.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-2000', '眼科半导体激光治疗仪', 'EO', '台', '台', 1709544.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-BCSM-D', '全方位椅子', 'FR', '个', '个', 21931.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-SM01B', '下颌托支架', 'FR', '个', '个', 1334.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DA-I2K-RETINA', '拼图软件', 'DA', '个', '个', 30990.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0882-000', '屈光补偿旋钮', 'CA', '个', '个', 44.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-SBMF', '眼电生理仪', 'LKC', '台', '台', 902998.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-078-FLICKER', '视觉电生理诊断装置', 'LKC', '个', '个', 128050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-004', '接地电极耳夹', 'LKC', '个', '个', 1454.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-014', '参考电极', 'LKC', '个', '个', 421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-078-FLICKER-ZZ', 'RETEVAL闪烁', 'LKC', '个', '个', 128050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-001', '1拖2电极分解器', 'LKC', '个', '个', 526.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63CZ-D', '垂直电动臂支架', 'FR', '个', '个', 5038.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63SA1-90', '平衡支架', 'FR', '个', '个', 5038.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SA-MC', '综合检查台', 'FR', '个', '个', 128224.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-ASMITWI022', '马达线缆', 'CV', '个', '个', 657.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-CV08EL0091', '供电电源', 'CV', '个', '个', 11410.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-REPAIR', 'OT-维修', 'OT', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OMRA-PRP-165-2-D', '医用镜子', 'OR', '个', '个', 9074.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OSMGA', '医用放大镜', 'OR', '个', '个', 4383.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-100967', '数字大功率耳背式助听器', 'OT', '个', '个', 33800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-103-42-100-00', '数字大功率耳背式助听器', 'OT', '个', '个', 29502.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.199-E', '手柄', 'ZO', '个', '个', 388503.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.510.001', '负压环', 'ZO', '个', '个', 48807.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.009', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-538-10-110-05', '电池仓门', 'OT', '个', '个', 23.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-613-00', '外置受话器', 'OT', '个', '个', 1980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-117854', '3R扬声器线', 'OT', '个', '个', 201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-123459', '耵聍挡板', 'OT', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124581', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124592', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.021', '手柄校正工具', 'ZO', '个', '个', 81996.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124963', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124969', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125389', '接收器', 'OT', '个', '个', 176.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125920-1', '大功率耳塞', 'OT', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SP-10-MF', '电池', 'SP', '个', '个', 5.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TL-100012-D', 'NormalOsmolarityControl', 'TL', '瓶', '瓶', 738.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-COMBO', '视觉功能分析仪', 'TR', '台', '台', 559560.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-Z1031', '主板组合单元', 'TR', '个', '个', 82151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-137879', '蓝牙伴侣', 'OT', '个', '个', 2777.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150-20-000-00', '电视适配器', 'OT', '个', '个', 1782.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-150260', '数字中功率耳背式助听器', 'OT', '个', '个', 4583.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-Z1129-M', 'CT锥形物', 'TR', '个', '个', 82143.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-020-LC', '20D大口前置镜(50MM)', 'VO', '个', '个', 3092.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30433', '主板', 'IR', '个', '个', 2667.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65094', 'IQ577powersupply', 'IR', '个', '个', 25442.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10511', '智能钥匙', 'IR', '个', '个', 1668.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-12313', '前面面板上旋钮开关', 'IR', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-13887', '电缆', 'IR', '个', '个', 3757.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0801005-002', '真耳分析仪', 'LS', '个', '个', 33284.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0903002-025', '液晶测电器', 'LS', '个', '个', 20.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0205029', 'BTEclipadaptor', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DELL3060D1515CN', 'Dell电脑', 'LS', '个', '个', 6461.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP-LED900-D', '桌面', 'LS', '个', '个', 450.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ALIENWARE-M15', '笔记本电脑', 'LS', '个', '个', 16731.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BASE-PLATE-D', '自制固定铁板', 'LS', '个', '个', 237.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1103039', '蓝宝石钻头13MM', 'LS', '个', '个', 94.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-23G-TIP', '眼用注液针', 'LS', '个', '个', 232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-25G-SCISSOR', '眼用剪', 'LS', '个', '个', 6435.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-41860', '背景光灯泡', 'LS', '个', '个', 33.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-46860', '背景光灯泡', 'LS', '个', '个', 76.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-900BQMOUNT', '接头座', 'LS', '个', '个', 165.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BATTERY-A10', '电池A10', 'LS', '个', '个', 2.50, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BITFLOWCARD-3TH', 'bitflowcard3thforrtvue', 'LS', '个', '个', 10023.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CABLE-CAMER-HS100', 'HS100校准用工业相机连接线', 'LS', '个', '个', 438.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-1', '数码相机', 'LS', '个', '个', 10000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-EOSR5', '相机', 'LS', '个', '个', 39293.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-7630S000S36-A', '轨迹球', 'HV', '个', '个', 1701.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EHAR3011-AA', '传感器', 'HV', '个', '个', 134.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-RETEVALCOMPLETE', '视觉电生理诊断装置', 'LKC', '台', '台', 260860.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-007', '电极膏', 'LKC', '瓶', '瓶', 271.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-068', '感应电极', 'LKC', '盒', '盒', 6761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CONTRACT', '保修合同专用编号', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GBK-1000VA', '稳压电源', 'LS', '个', '个', 539.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0406005', '1kg琼脂', 'LS', '个', '个', 335.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BATTERY-10', '国产电池A10', 'LS', '个', '个', 2.50, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BITFLOW-NEON', '采集卡', 'LS', '个', '个', 10193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA7DMARKII', '7D数码相机', 'LS', '个', '个', 9460.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-400-44732', 'CableJacketExpanderTool', 'OV', '个', '个', 3225.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-44989-002', '参考臂组件', 'OV', '个', '个', 24601.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-45539-001', 'SLD', 'OV', '个', '个', 44729.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.103.140', '穿透性角膜移植功能码', 'ZO', '个', '个', 175705.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.007', 'CableCommunicationCavityA5ForLDVZ2Z4Z6', 'ZO', '个', '个', 937.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-632-00', '外置受话器', 'OT', '个', '个', 2310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-661-00', '外置受话器', 'OT', '个', '个', 2640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-153673', '数字中功率耳背式助听器', 'OT', '个', '个', 30800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-157418', '数字大功率耳背式助听器', 'OT', '个', '个', 34800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-157441', '数字大功率耳背式助听器', 'OT', '个', '个', 7800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-157498', '数字大功率耳背式助听器', 'OT', '个', '个', 19600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.039', 'SuctiontubeadapterblueZ8', 'ZO', '个', '个', 351.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.043', '飞秒工具', 'ZO', '个', '个', 32213.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-524-56-380-00', '麦克风管', 'OT', '个', '个', 13.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-589-25-150-00', '细声管', 'OT', '个', '个', 40.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-020-00', '受话器', 'OT', '个', '个', 725.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SONOMETER-229B', '诊断型听力计', 'LS', '台', '台', 45558.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SPROOM', '听力隔声室', 'LS', '个', '个', 62642.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TVCARD-1', '视频采集卡', 'LS', '个', '个', 683.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-VIDEOSYS-KIT', '影像系统', 'LS', '个', '个', 22000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-SURFACE-PRO', '电脑', 'LS', '个', '个', 17426.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-3', '打印机', 'LS', '个', '个', 1000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CA2900', '打印机', 'LS', '个', '个', 1815.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPL1800', '打印机', 'LS', '个', '个', 4954.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPL4168', '打印机', 'LS', '个', '个', 2188.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP454DW', '打印机', 'LS', '个', '个', 4499.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-MF643CDW', '打印机', 'LS', '个', '个', 4157.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TY2A-D', '电动桌', 'LS', '个', '个', 1254.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-USB-LIVE2', '视频采集卡', 'LS', '个', '个', 901.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-VAIO-SX12', '电脑', 'LS', '个', '个', 15330.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-VS', '粘弹剂', 'LS', '个', '个', 90.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-WH-V', '超生雾化器', 'LS', '个', '个', 42471.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-400-44811', 'HeatShrinkTool', 'OV', '个', '个', 4346.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-510-43696', '马达球', 'OV', '个', '个', 268.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-530-47565-002', 'XR下巴架白色', 'OV', '个', '个', 268.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.067', '玻璃面板固定器', 'ZO', '个', '个', 879.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.003.008', '眼科飞秒激光治疗仪', 'ZO', '台', '台', 4273532.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.400.003', '掀瓣器', 'ZO', '个', '个', 13910.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.017', '控制板', 'ZO', '个', '个', 374837.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-KIT-D', '光源组件', 'RH', '个', '个', 9985.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-REPAIR', 'RH返厂维修', 'RH', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('SO-9200-1512-2C-ZZ', 'A-ScanCylinders16.5mm', 'SO', '个', '个', 3355.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-200670', 'COLOR1/3"SONYCCDHIGH-RES', 'TR', '个', '个', 2683.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-028-LC', '28D前置镜(41MM)', 'VO', '个', '个', 2970.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-VHRC', 'HRCentralis', 'VO', '个', '个', 9894.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-62814', '泡沫耳塞', 'WE', '个', '个', 7.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.006', 'CablecontrollercavityA5forZ2Z4Z6', 'ZO', '个', '个', 29284.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.015', '角膜手术包', 'ZO', '盒', '盒', 37960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.043', 'Displayinclcablesetw/o', 'ZO', '个', '个', 42169.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.044', '监视器线', 'ZO', '个', '个', 1845.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.161-ZZ', 'powerunitC1Z8', 'ZO', '个', '个', 153742.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.167', '电源传感器', 'ZO', '个', '个', 112256.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.055', 'PolarizationRotator', 'ZO', '个', '个', 68720.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.005', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-MOMASSY032-AA', '打印组建', 'HV', '个', '个', 1376.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-MOMASSY032-AE', '打印机组件', 'HV', '个', '个', 1376.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-1030MP0A210-B', 'PDSENSORPLATE', 'HV', '个', '个', 7.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MD-4625CE', '管道组，玻切头', 'MD', '个', '个', 546.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-311123-ZZ', 'Coolingsystem(fan)to', 'MR', '个', '个', 6101.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340977-ZZ', 'SLAfor577CSO', 'MR', '个', '个', 140320.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-640703-ZZ', 'Safetygogglesfor', 'MR', '个', '个', 5491.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OH-Z8-L30C-SH', 'PN#773434OEMSHHeadCWupto', 'OH', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-151237', '蓝牙伴侣', 'OT', '个', '个', 3600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('II-DOC100', '耳镜', 'II', '个', '个', 7239.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-163884', '数字中功率耳背式助听器', 'OT', '个', '个', 29800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-170339', '数字中功率耳背式助听器', 'OT', '个', '个', 16800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-170345', '数字中功率耳背式助听器', 'OT', '个', '个', 16800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0544-000', 'QRM弹簧', 'CA', '个', '个', 189.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0872-000', '凸轮', 'CA', '个', '个', 10.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804540', '固视光LED板', 'HA', '个', '个', 3309.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022545S', '摄像头', 'HA', '个', '个', 33677.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1023140S', 'LS900测量头', 'HA', '个', '个', 150423.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1300068', '卤素灯泡', 'HA', '个', '个', 1638.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020940-D', '照明控制线缆2米', 'HA', '个', '个', 620.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021022-D', '亮度控制器', 'HA', '个', '个', 2526.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400232', '冷光源', 'HA', '个', '个', 23061.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1400306-D', 'Contrastenhancingfilter', 'HA', '个', '个', 19796.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220502', '升级包', 'HA', '个', '个', 139588.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1200602', '长镜片', 'HA', '个', '个', 1757.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1200713', '下巴纸固定钉', 'HA', '个', '个', 132.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220550', 'IM900影像模组', 'HA', '个', '个', 165082.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803107', '闪烁视野程序', 'HA', '个', '个', 44606.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-Z8-JUNO', 'USBINTERFACE', 'LS', '个', '个', 12949.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-S120-HS100', 'HS100激光能量表探头S120', 'LS', '个', '个', 3595.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-41097', '控制面板', 'OV', '个', '个', 2684.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-45721-002', '图像传感器控制板', 'OV', '个', '个', 2281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48004', '角膜上皮软件套件', 'OV', '套', '套', 28600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-49227-002', 'TCP软件套件', 'OV', '个', '个', 59847.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-54543-001-ZZ', 'Assyscannerivue80', 'OV', '台', '台', 438345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-REPAIR', '光视返厂维修', 'OV', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113228', '19G双锥体超乳针头15度', 'OP', '个', '个', 2174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113233', '19G高负压超乳针头', 'OP', '个', '个', 2174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340968-ZZ', 'UPFpassiveforZeiss/HS', 'MR', '个', '个', 41525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340984-ZZ', 'UPFactiveforZeiss/HS', 'MR', '个', '个', 114208.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340988-ZZ', 'UPFpassiveforTopcon', 'MR', '个', '个', 41525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-25-A-ZZ', 'Endoprobes,25GSMAconnector', 'MR', '个', '个', 2489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-25-S-ZZ', 'Endoprobes,25GSMAconnector', 'MR', '个', '个', 2489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-27-S-ZZ', 'Endoprobes,27GSMAconnector', 'MR', '个', '个', 2489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-538-10-140-01', '电池仓门', 'OT', '个', '个', 23.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OH-Z8-JUNO', 'PN#7Z01250CompactUSBInterface', 'OH', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-1135517', '系统编程软件', 'OK', '个', '个', 2540.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-OPTOYAG-M', '眼科激光系统', 'OK', '台', '台', 274196.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340985-ZZ', 'UPFactiveforBQ900.', 'MR', '个', '个', 114208.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-340987-ZZ', 'UPFpassiveforBM900.Clear', 'MR', '个', '个', 41525.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MR-SMA-23-A-ZZ', 'Endoprobes,23GSAMconnector', 'MR', '个', '个', 2489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-570-07-351-00', '耳塞', 'OT', '个', '个', 18.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-870-98-851-00', '编程仪器', 'OT', '个', '个', 12316.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-EARMOLD-ACRYLIN', '自制耳膜', 'OT', '个', '个', 120.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.700.013', '一次性手术包', 'ZO', '盒', '盒', 37960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.029', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.034', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.401', 'SERVICE卡片', 'ZO', '个', '个', 4006.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1487-000', '基座', 'CA', '个', '个', 39.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-3403-000', 'lightproofpaper', 'CA', '个', '个', 61.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF0-4808-000', '螺杆驱动单元', 'CA', '个', '个', 1612.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF0-5491-000', '屈光补偿镜', 'CA', '个', '个', 1039.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3135-000', 'DrivingUnitbuffle', 'CA', '个', '个', 2008.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BG7-3155-000', '电容', 'CA', '个', '个', 1309.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0306-060', '劈裂线组件', 'CA', '个', '个', 6771.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0337-000', '主板', 'CA', '个', '个', 10629.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0346-000', 'PCBAssyFoucsPotentio', 'CA', '个', '个', 822.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0859-050', 'TX20Interpaceboard', 'CA', '个', '个', 8483.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1024-000', '主板', 'CA', '个', '个', 8749.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1821-000', 'CABLEUNITSTAGE', 'CA', '个', '个', 1618.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BY9-3402-001', '清洁纸', 'CA', '个', '个', 196.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.023', 'POWERMETERADAPTERC1', 'ZO', '个', '个', 3046.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-91-201', 'SensorstriptoDINcable', 'LKC', '个', '个', 3908.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XB6-2300-408', 'SET.SCREW', 'CA', '个', '个', 39.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SANTAKUPS-C3K', '不间断电源', 'LS', '个', '个', 4129.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TROLLEY', '台车', 'LS', '个', '个', 3300.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TROLLEY-D', '台车', 'LS', '个', '个', 3300.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-XD-BG476T', '眼内膜铲', 'LS', '个', '个', 5979.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0903003', '指针式测电器', 'LS', '个', '个', 50.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-BASE-PLATE', '基座板', 'LS', '个', '个', 237.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA40D', '相机', 'LS', '个', '个', 1287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-LENS', '相机镜头', 'LS', '个', '个', 12870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-1', '电脑', 'LS', '个', '个', 10000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-4', '电脑', 'LS', '个', '个', 2500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-HP400G3', '惠普电脑', 'LS', '个', '个', 5258.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO980-3X-LED', '光源模块', 'LS', '个', '个', 9339.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTERHOLDER', '电脑支架', 'LS', '个', '个', 440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTERHOLDER-HP', '电脑支架-HP', 'LS', '个', '个', 352.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-HP600G2', '惠普电脑', 'LS', '个', '个', 6160.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CORD-401-503-D', '二项电源线', 'LS', '个', '个', 36.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-JT', '脚踏开关', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PAPER', '打印纸', 'LS', '个', '个', 8.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MASSA01-Y-KL6', 'KL6电动桌', 'LS', '个', '个', 2175.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-218364', '耳塞', 'OT', '包', '包', 105.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230267', 'A-助听器', 'OT', '个', '个', 31800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSD-202001', '台车', 'LS', '个', '个', 770.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO990-MOUNT', 'themountforCSO990', 'LS', '个', '个', 1367.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-510S', '电脑', 'LS', '个', '个', 3417.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-DELL560S', 'Dell电脑', 'LS', '个', '个', 2574.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCHOLDER-12X40X40', '电脑支架', 'LS', '个', '个', 523.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-7Z02410', '能量表', 'LS', '个', '个', 10803.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OPHIR-SENSOR-50A', '中功率激光功率计探头', 'LS', '个', '个', 17761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OV-EPROM', 'EPROM', 'LS', '个', '个', 3.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-5', '配件', 'LS', '个', '个', 1000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVOM710-D', '电脑', 'LS', '个', '个', 7503.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPL810', '打印机', 'LS', '个', '个', 2790.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-HP108A', '打印机', 'LS', '个', '个', 1030.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OCTAI-PABOX2', 'softwarePABOX2forOCTAI', 'LS', '个', '个', 50000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-POWER', '源插头线', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SOFA-3', '干眼仪专用沙发', 'LS', '个', '个', 7721.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192313', '23G玻切', 'OP', '个', '个', 3710.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-178968', '耳内式助听器', 'OT', '个', '个', 32340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220001', '视野计', 'HA', '个', '个', 267490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GLOVE', '手套', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-HOLDER', '支架', 'LS', '个', '个', 440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220696', 'IM900', 'HA', '个', '个', 146966.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-VP6200', '灭菌盒', 'EO', '个', '个', 3050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-FC2720', '设备航空箱', 'EO', '个', '个', 6312.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021022', '亮度控制器', 'HA', '个', '个', 2526.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021024-D', '亮度控制器', 'HA', '个', '个', 1955.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********-D', '桌面套件', 'HA', '个', '个', 1779.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DIGEST-OCTOPUS', '视野计文摘', 'LS', '个', '个', 257.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-HOLDER-DLB502', '支架DLB502型', 'LS', '个', '个', 440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IF-7006', '开睑器', 'LS', '个', '个', 957.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-IF-8505', '剥离器', 'LS', '个', '个', 1076.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-INJECTOR-1ML', '1ML注射器', 'LS', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ISTAND-NEW', '支架台车', 'LS', '个', '个', 150151.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('**********', '照明光纤', 'HA', '个', '个', 4715.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1802349', '目镜遮光罩', 'HA', '组', '组', 287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803113', '科学开发程序包', 'HA', '个', '个', 26758.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000513', '齿孔滚轮', 'HA', '个', '个', 305.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220460', '眼科光学生物测量仪', 'HA', '台', '台', 277174.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220544', 'ABS基座', 'HA', '个', '个', 85900.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220661-ZZ', 'FundusModul300withoutRM02', 'HA', '个', '个', 184490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401204', '镜子支架', 'CL', '个', '个', 71.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6401713', 'Potentiometer5K', 'CL', '个', '个', 356.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405057', '同视机闪光装置', 'CL', '个', '个', 2144.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6512003', '黑点幻灯片', 'CL', '片', '片', 1287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-05-527.10E', '转换器', 'DM', '个', '个', 11625.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-14-127.53', '右手位桌面', 'DM', '个', '个', 4890.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-14-327.53', '左手位桌面', 'DM', '个', '个', 4890.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-921.20E', '电压整合模组', 'DM', '个', '个', 1905.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-601.21E', '电动仪器', 'DM', '个', '个', 35310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-656.10E', '附加的HAAG适配器', 'DM', '个', '个', 1524.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-500-C0611-002', 'Chinrestmodule', 'CR', '个', '个', 8767.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-550-C0201-005', '主控制板', 'CR', '个', '个', 5010.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-550-C0203-003', 'LED板', 'CR', '个', '个', 1342.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-550-C060P-006', '电源板', 'CR', '个', '个', 2684.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0201-C01-D', '方舟光学头组件', 'CR', '台', '台', 189077.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0201-C12', '免散瞳眼底照相机', 'CR', '台', '台', 154986.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C1101-011-D', '免散瞳眼底照相机', 'CR', '台', '台', 196182.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200SMA-XVA', '定制激光连接器19G曲面激光内窥镜', 'EO', '个', '个', 69310.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-600ZMG', '6KPixelVideoAdapterwith', 'EO', '个', '个', 66879.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-VP6250', '消毒盘', 'EO', '个', '个', 3634.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63DB', '电动牛眼悬臂支架', 'FR', '个', '个', 28922.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63DB-D', '垂直机动臂', 'FR', '个', '个', 28922.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65HS-NE', '右手型四位检查台', 'FR', '个', '个', 317357.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65HS-NE-D', '综合检查台', 'FR', '个', '个', 317357.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-ADMITEL002', 'DC控制板', 'CV', '个', '个', 5335.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-ASMITWI014', '传感器连线', 'CV', '个', '个', 1672.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0067-000', '聚焦镜组', 'CA', '个', '个', 6627.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1152-030', 'lensunitobserving9707', 'CA', '个', '个', 9832.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1495-000', '喷气嘴盖', 'CA', '个', '个', 100.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101625-1.6', '工作站', 'DS', '个', '个', 184570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101730', '手柄', 'DS', '个', '个', 81502.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-150259-D', 'USBHUB', 'DS', '个', '个', 1070.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-097-ZZ', 'LCDPATTERNMONITOR', 'LKC', '个', '个', 31489.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-MF-UPGRADE', '电脑', 'LKC', '台', '台', 460888.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-N30', '电极', 'LKC', '个', '个', 21041.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1157-030', 'driveunitsplit9707', 'CA', '个', '个', 7755.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-HS100-1', '光学相干断层扫描仪', 'CA', '台', '台', 722041.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0755-000', 'CABLEUNITCAMERAPOWERFOR', 'CA', '个', '个', 2382.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0496-000', 'PCBASSY,LCDRELAYtx20', 'CA', '个', '个', 2201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9384-000', 'CABLEUNIT,CAMERACONTROL', 'CA', '个', '个', 2344.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-5316-000', '相机线缆', 'CA', '个', '个', 1112.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0135-000', 'PCBASSY,FocusIP', 'CA', '个', '个', 359.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2511-000', '齿轮', 'CA', '个', '个', 62.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0812-000', '短基座', 'CA', '个', '个', 31.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-4146-000', '主机右边外壳', 'CA', '个', '个', 718.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH3-0716-050', '面板开关', 'CA', '个', '个', 4285.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0078-000', '闪光灯激发板', 'CA', '个', '个', 2328.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA3-1444-020', '滚轮', 'CA', '个', '个', 75.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0354-110', '聚焦环传感器板', 'CA', '个', '个', 452.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0857-000', '主板', 'CA', '个', '个', 12421.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1626-000', 'E-RingSUS(D=2.35NO)', 'CA', '个', '个', 919.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0149-000', '电容', 'CA', '个', '个', 1456.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0188-000', '劈裂线单元', 'CA', '个', '个', 16579.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BF1-0206-030', '光吸收遮挡片', 'CA', '个', '个', 228.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0356-130', '传感器板', 'CA', '个', '个', 351.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0494-000', '基座电路板', 'CA', '个', '个', 6965.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0181-000', 'CX1快反镜单元', 'CA', '个', '个', 27386.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0930-000', '手柄胶皮套', 'CA', '个', '个', 197.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1809-000', '光源基座', 'CA', '个', '个', 2819.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0306-000', '劈裂线棱镜', 'CA', '个', '个', 6286.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XA1-1260-406', '螺丝钉', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-WT2-6638-000', '线缆夹', 'CA', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0683-000', '连接器', 'CA', '个', '个', 768.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-1849-000', '物镜镜头', 'CA', '个', '个', 7846.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0365-000', '劈裂线光源', 'CA', '个', '个', 73.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-0542-000', '操作组件', 'CA', '个', '个', 13216.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA4-2857-000', '控制杆倾斜锁', 'CA', '个', '个', 212.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0864-000', '凸轮', 'CA', '个', '个', 36.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-0934-000', '手柄结构支架', 'CA', '个', '个', 126.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH7-9269-000', 'CBM7Cablecard40pin54mm', 'CA', '个', '个', 202.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2451-020', '控制面板((右侧）', 'CA', '个', '个', 1031.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10562-1', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 4589.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70038', '后侧线缆', 'IR', '个', '个', 5159.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31172-01', '裂隙灯激光适配器', 'IR', '个', '个', 135258.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-33122', 'MLT镜', 'IR', '个', '个', 29043.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-TX-SYSTEM-D', '激光光凝仪', 'IR', '个', '个', 301669.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30173', '护目镜', 'IR', '个', '个', 5843.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65504-D', '裂隙灯适配器', 'IR', '个', '个', 123356.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-31000', '裂隙灯镜组', 'IR', '个', '个', 37756.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-IQ532-SYSTEM-D', 'YAG倍频激光光凝仪', 'IR', '台', '台', 417671.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-SLX-SYSTEM-D', '半导体激光治疗仪', 'IR', '台', '台', 398870.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-12677-01', 'IR-12677-01', 'IR', '个', '个', 13419.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-HEATSENSITIVECARD', '营销用热敏卡', 'IR', '个', '个', 30.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-65362-D', 'mirrorframeassemblyHS577nm', 'IR', '个', '个', 7455.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-009', '电极垫圈', 'LKC', '袋', '袋', 507.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-90-087', '电生理放大器', 'LKC', '个', '个', 10981.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-057', '放大器充电组', 'LKC', '个', '个', 5691.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-006', '电极膏(3.5盎司一管)', 'LKC', '瓶', '瓶', 427.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-298', '支架', 'LKC', '个', '个', 11451.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-672-00', '外置受话器', 'OT', '个', '个', 2640.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-26-053', '电池', 'LKC', '个', '个', 2845.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-SB', '眼电生理仪', 'LKC', '台', '台', 654386.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-075', '金杯电极', 'LKC', '个', '个', 879.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-REPAIR', '维修', 'LKC', '次', '次', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-29-038', '手提箱', 'LKC', '个', '个', 6287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-81-248', 'RETEVALFRONTBEZEL', 'LKC', '个', '个', 761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-068-ZZ', '50个一盒，传感器电极', 'LKC', '个', '个', 6761.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-91-173', '光纤线缆', 'LKC', '个', '个', 3401.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14991', '810适配器光纤', 'IR', '个', '个', 16773.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14994', '裂隙灯激光适配器光纤', 'IR', '个', '个', 28382.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300338', '防护眼镜', 'HA', '个', '个', 5300.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220733', '亮度控制线缆', 'HA', '个', '个', 776.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220723', '闪光灯泡', 'HA', '个', '个', 28406.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1820287S', '电源传输组件', 'HA', '个', '个', 1228.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1008409S', '滚珠轴套', 'HA', '个', '个', 1054.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7020034-ZZ', '25倍目镜', 'HA', '个', '个', 8826.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220770', '6V灯泡', 'HA', '个', '个', 1406.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1300127S', '压平眼压计悬臂', 'HA', '个', '个', 6501.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803900', '电源供应单元', 'HA', '个', '个', 15286.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022313S', '滚轴', 'HA', '个', '个', 2226.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022308', 'CableUSB3.0Camera', 'HA', '个', '个', 518.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1800956', '应答器手柄', 'HA', '个', '个', 3602.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220566', '以太网交换器', 'HA', '个', '个', 2196.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220747', '亮度控制单元', 'HA', '个', '个', 2476.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1806210S', '摄像头组件', 'HA', '个', '个', 1770.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220462-D', 'Headrestwithlowepart', 'HA', '个', '个', 16428.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020884-EX', 'LEDIllumationHead(woBack-groundIllumination)', 'HA', '个', '个', 8541.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220396', 'TORIC晶体规划软件', 'HA', '个', '个', 19523.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000353', '裂隙灯灯泡', 'HA', '个', '个', 1092.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021024', '亮度控制器', 'HA', '个', '个', 1955.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022387-D', '裂隙灯桌面套件', 'HA', '个', '个', 1542.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220229-D', '裂隙灯显微镜', 'HA', '台', '台', 228198.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804205', '亮度控制板', 'HA', '个', '个', 10163.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403162', '齿轮传动装置', 'CL', '个', '个', 1161.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6403450', '下额托万向接头', 'CL', '个', '个', 1429.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-17-920.20E', '附件的控制模块', 'DM', '个', '个', 5962.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-906.20E', '电动座椅移动', 'DM', '个', '个', 20964.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-HOLDER-FE110G', '长臂猿显示器支架', 'LS', '个', '个', 121.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OVP-RJ45-E100', '网络交换机', 'LS', '个', '个', 149.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CATS708', '打印机', 'LS', '个', '个', 1650.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-6', '配件', 'LS', '个', '个', 500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-LENOVOS41-E', '联想笔记本电脑S410', 'LS', '个', '个', 11583.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-25G-TIP', '眼用注液针', 'LS', '个', '个', 335.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-3', '相机', 'LS', '个', '个', 2500.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-CS1831W', '打印机', 'LS', '个', '个', 2134.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-COMPUTER-3', '电脑', 'LS', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-TONOVUE-D', '非接触式眼压计', 'CR', '台', '台', 89404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-620-47803-001', 'ivueiBase2,PCBA,Sensor,Dual', 'OV', '个', '个', 476.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000483', '灯泡', 'HA', '个', '个', 988.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-54345-001', '光学相干断层扫描仪', 'OV', '台', '台', 758605.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48098-02', '光学相干断层扫描仪', 'OV', '台', '台', 789020.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48004-D', '角膜上皮软件套件', 'OV', '个', '个', 28600.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-40515', 'Z马达', 'OV', '个', '个', 16013.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-560-45672-002', '左右眼识别线', 'OV', '个', '个', 470.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-48377-005', '控制板', 'OV', '个', '个', 21246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-REPAIR', 'EO返厂维修', 'EO', '个', '个', 1.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-SW002', '激光脚踏', 'EO', '个', '个', 29237.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63CZ', '牛眼悬臂支架固定装置', 'FR', '个', '个', 5038.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SC', '检查台', 'FR', '台', '台', 211741.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-65SC-MC-SX', '椅子', 'FR', '个', '个', 224369.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-TG-FRA-TRONIC-R2', '电路控制板', 'FR', '个', '个', 7939.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-TT220V', '220伏供电模组', 'FR', '个', '个', 2667.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022545S-EX', 'IM900camerareworkedw/flange', 'HA', '个', '个', 8531.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-233.20E', '附加的负载的宽度', 'DM', '个', '个', 1746.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-992.20E', 'LED工作位置灯', 'DM', '个', '个', 8097.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-H03.14', '支架开关', 'DM', '个', '个', 7240.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-013.27E', '折射/检查单元', 'DM', '台', '台', 310808.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-051.14E', '靠背', 'DM', '个', '个', 13527.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DA-I2K-RETINA-D', '拼图软件', 'DA', '个', '个', 30990.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101862', '手柄外壳', 'DS', '个', '个', 1937.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1803102', '视野计软件', 'HA', '个', '个', 26758.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022140S', '螺丝', 'HA', '个', '个', 95.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1007432S', '弥散镜片', 'HA', '个', '个', 334.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020881', 'LED电源盒', 'HA', '个', '个', 7766.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804520S', '刺激光LED板', 'HA', '个', '个', 2050.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3000191', '气动开关', 'HA', '个', '个', 2108.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020887', '背景光支架', 'HA', '个', '个', 20206.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1804465', '开关', 'HA', '个', '个', 577.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-ASUS-D700SA', '华硕电脑', 'LS', '个', '个', 7061.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-1', '配件', 'LS', '个', '个', 10000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-2', '配件', 'LS', '个', '个', 6000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.028', '飞秒工具', 'ZO', '个', '个', 6150.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.032', '聚甲基丙烯酸甲酯校准柱体', 'ZO', '个', '个', 2123.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-100885', '光学头', 'DS', '个', '个', 1409879.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-101973', '轮子固定器', 'DS', '个', '个', 9526.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-150788', '气动弹簧', 'DS', '个', '个', 2977.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3013-000', '操作面板单元', 'CA', '个', '个', 1637.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3066-030', '下颚托支架', 'CA', '个', '个', 9232.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-3121-000', '相机单元', 'CA', '个', '个', 14464.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021181', 'LED照明灯头', 'HA', '个', '个', 19042.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-OP-114101', '注吸手柄', 'LS', '个', '个', 4345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-TY2A', '电动桌', 'LS', '个', '个', 1254.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.033', '升降控制器', 'ZO', '个', '个', 24510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.054', '快门组件', 'ZO', '个', '个', 54466.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124973', '外置受话器', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192317', '27G玻切头', 'OP', '个', '个', 5137.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-186583', '数字中功率耳背式助听器', 'OT', '个', '个', 32340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191959', '耳内式助听器', 'OT', '个', '个', 7860.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.147', '转臂锁', 'ZO', '个', '个', 18236.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.153', '电机', 'ZO', '个', '个', 24315.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-7220067', 'COMBI7000型检查台', 'LU', '台', '台', 346852.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-7720034', '自动前后移动系统', 'LU', '个', '个', 31893.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8105-8003-00', '试镜片托盘', 'LU', '个', '个', 5375.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('MD-4630CE', '管道组，玻切头', 'MD', '个', '个', 837.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192550', 'Fluidsexchsetwithautomaticvalve', 'OP', '个', '个', 193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-196665', '数字中功率耳背式助听器', 'OT', '个', '个', 18690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-201899', '数字大功率耳背式助听器', 'OT', '个', '个', 5440.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.019', '顶视校正工具', 'ZO', '个', '个', 46685.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.034', '飞秒校准工具', 'ZO', '个', '个', 30345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.035', 'OCTcalibrationPMMA', 'ZO', '个', '个', 3210.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('NL-20-4001-YY', 'LIONpowersupplies', 'NL', '个', '个', 1266.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111004', '超声乳化仪', 'OP', '台', '台', 228772.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-112101-D', '双线性脚踏开关', 'OP', '个', '个', 31793.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-112103-D', '双线性脚踏开关', 'OP', '个', '个', 31793.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113107', 'Phacohandpiece"SIX"', 'OP', '个', '个', 51760.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113251', '21GFLAREDBENTPHACOTIP', 'OP', '个', '个', 2494.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113402-D', '针头扳手', 'OP', '个', '个', 1338.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-121013-D', 'R-EvoSmartCR', 'OP', '个', '个', 513914.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230290', 'A-助听器', 'OT', '个', '个', 10480.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OLVS-3', '医用放大镜', 'OR', '个', '个', 32062.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113307', '硅胶套', 'OP', '个', '个', 233.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-131664', '数字中功率耳背式助听器', 'OT', '个', '个', 16923.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.148', '手柄摄像头', 'ZO', '个', '个', 192091.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8105-8900-00', '自动前后移动系统', 'LU', '个', '个', 48483.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.030', '螺纹螺栓', 'ZO', '个', '个', 379.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.081', '焦深测试套件', 'ZO', '个', '个', 2626.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.026', '负压泵', 'ZO', '个', '个', 11914.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.002-E', 'LDV控制器', 'ZO', '个', '个', 170207.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48577', '光学相干断层扫描仪', 'OV', '台', '台', 356267.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-116001', '测试套', 'OP', '个', '个', 235.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7220354', '视野计', 'HA', '台', '台', 187086.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CORD-404A-501-D', 'THREEWIREPOWERCORD', 'LS', '个', '个', 36.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HY-XY-1-20.50', '预装式非球面后房人工晶状体', 'HY', '个', '个', 5000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-218373', '耳塞', 'OT', '包', '包', 105.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48098', '光学相干断层扫描仪', 'OV', '台', '台', 1115095.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-41117-003-ZZ', 'AssyPowerSupply(DCPwr', 'OV', '个', '个', 5385.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL3Tabletop', 'KL3的桌面', 'LS', '个', '个', 513.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('RH-D029AZ008B08A', '干眼治疗头前盖组件', 'RH', '个', '个', 10023.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BA5-1053-100', 'LED固定挡片', 'CA', '个', '个', 324.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BH6-3054-000', '视频线缆', 'CA', '个', '个', 153.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-M2EPCB0003-AA', 'PCBANTSCG-100', 'HV', '个', '个', 210.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A2MASSY211-AE', '打印机组件', 'HV', '个', '个', 1141.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3EPCB0002-AG', 'MainboardPCBASSY(HRKAM422)', 'HV', '个', '个', 9262.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OI-HM-78M', '医用放大镜', 'OR', '个', '个', 3006.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-49954-001', 'OCT血管成像软件', 'OV', '个', '个', 351570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-AXGSDME010', '按动开关缠绕线缆', 'CV', '个', '个', 6520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-E5-T-D', '生物显微镜', 'LS', '个', '个', 3800.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-48155-002', '手柄', 'OV', '个', '个', 10064.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CR-700-C0801-001-D', '升降桌', 'CR', '个', '个', 6575.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CV-ARMARME012', '按钮', 'CV', '个', '个', 6287.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-USB2.0EXTENDER', 'USB隔离器', 'LS', '个', '个', 3024.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-170ZMG', '17K视频适配器', 'EO', '个', '个', 43176.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EO-OME-200LA', '眼科内窥镜', 'EO', '个', '个', 45345.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63ABM', '垂直臂的控制装置', 'FR', '个', '个', 7303.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('FR-63DD', '垂直移动臂', 'FR', '个', '个', 29044.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-83500002', '脚踏开关', 'OK', '个', '个', 8891.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-CR-2-AF-D', '数字眼底照相机', 'CA', '个', '个', 275569.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-XA1-1140-256', '螺丝', 'CA', '个', '个', 13.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-BM7-3715-000', '电路板', 'CA', '个', '个', 13511.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-30824-02', 'LIO光纤', 'IR', '个', '个', 10189.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-87301', '激光间接检眼镜', 'IR', '个', '个', 217910.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-061.14E', '倾斜靠背-左手位', 'DM', '个', '个', 12625.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CAMERA-CA800D', '单反相机', 'LS', '个', '个', 7802.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-400-44871', 'ConnectorSimulator', 'OV', '个', '个', 1324.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-48798-001', '升降柱', 'OV', '个', '个', 26837.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-500-50530-002', '一体机电脑', 'OV', '个', '个', 43208.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-510-40077-B', '手柄底盘', 'OV', '个', '个', 242.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-550-45067-004', '测量头控制板', 'OV', '个', '个', 20128.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-44211', '振镜模组', 'OV', '个', '个', 24449.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-620-50602-001-ZZ', 'XR3Column,Riser', 'OV', '个', '个', 21246.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-45856', '服务器企业版', 'OV', '个', '个', 36767.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-46994', 'ReVue联网许可套件', 'OV', '个', '个', 9912.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-700-48182-002', '光学相干断层扫描仪', 'OV', '台', '台', 1006403.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('VO-V3MIR', 'VOLK镜V3MIR', 'VO', '个', '个', 4884.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.002.001', '飞秒外科激光治疗仪', 'ZO', '台', '台', 6308292.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.004-E', 'LDV激光腔', 'ZO', '个', '个', 601301.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-91-194', '电源充电器', 'LKC', '个', '个', 1954.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-SB-ZZ', 'UTASSUNBRUST', 'LKC', '台', '台', 654386.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-92-058', '电源盒', 'LKC', '个', '个', 30509.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-UTAS-BS-F-UV', '台式电生理BigShot闪光ERG(UV刺激)', 'LKC', '包', '包', 655477.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LK-95-011A', 'ERG电极', 'LKC', '个', '个', 671.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PARTS-4', '配件', 'LS', '个', '个', 2000.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-144720', '验配仪器', 'OT', '个', '个', 5292.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-3300716', '孔径腔', 'HA', '个', '个', 5603.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ED-BOX', '电子干燥盒', 'LS', '个', '个', 178.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-FG-EU207A', '扩展卡', 'LS', '个', '个', 149.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('TR-HOYA-SOFTWARE', 'HOYA软件套件', 'TR', '个', '个', 78276.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-384-20-014-00', '3号编程线', 'OT', '个', '个', 446.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-384-20-022-00', '编程线', 'OT', '个', '个', 281.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-632-02-612-00', '外置受话器', 'OT', '个', '个', 1980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-113238', '微喇叭口针头', 'OP', '个', '个', 1923.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-7200012-D', '照明装置', 'HA', '个', '个', 102787.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111004-D', '超声乳化仪', 'OP', '台', '台', 228772.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-111009-D', 'R-EvoSmartE', 'OP', '个', '个', 322972.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-161204', '角膜地形图仪', 'OP', '台', '台', 176807.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-162202', '角膜地形图充电座', 'OP', '个', '个', 24959.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ELECTRICITYMEASUR', '测电器', 'LS', '个', '个', 5.15, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OV-600-45959-002', '振镜', 'OV', '个', '个', 28428.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182610', '耳内式助听器', 'OT', '个', '个', 18690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182611', '耳内式助听器', 'OT', '个', '个', 18690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182612', '耳内式助听器', 'OT', '个', '个', 18690.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182765', '耳内式助听器', 'OT', '个', '个', 11340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192225-D', '25G截止阀套管', 'OP', '个', '个', 2787.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192513', '一次性灌注气管+过滤器', 'OP', '个', '个', 152.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124971', '外置受话器', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-133261', '数字大功率耳背式助听器', 'OT', '个', '个', 6980.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-149291', '耳塞', 'OT', '个', '个', 510.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182570', '耳内式助听器', 'OT', '个', '个', 19740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-182577', '耳内式助听器', 'OT', '个', '个', 20790.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191912', '耳内式助听器', 'OT', '个', '个', 9960.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-191945', '耳内式助听器', 'OT', '个', '个', 8380.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-196658', '耳背式助听器', 'OT', '个', '个', 11970.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-214930', '耳背式助听器', 'OT', '个', '个', 7330.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-221867', 'A-助听器', 'OT', '个', '个', 35490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230275', 'A-助听器', 'OT', '个', '个', 19740.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-230284', 'A-助听器', 'OT', '个', '个', 10480.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-10547', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 4586.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14400', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 3872.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-CYCLO-G6-SYSTEM-Z', 'CycloG6Laserconsole', 'IR', '台', '台', 417108.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-66200-G', 'IQ532微脉冲模组', 'IR', '个', '个', 62712.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-70295-D', '扫描激光传输装置', 'IR', '台', '台', 414569.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-IQ532-SYSTEM', '眼科Nd：YAG倍频激光光凝仪', 'IR', '台', '台', 417671.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-13880-01', '810nm5mm裂隙灯适配器', 'IR', '个', '个', 144265.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1008041S', 'Mirrorassembled', 'HA', '个', '个', 9254.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020263', '电源盒', 'HA', '个', '个', 16985.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1021888S', '滚轴', 'HA', '个', '个', 5623.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022165', '释放控制模组', 'HA', '个', '个', 19925.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1022524S-EX', '短距离装置', 'HA', '个', '个', 20499.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.160-E', '激光腔', 'ZO', '个', '个', 1074736.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.160-ZZ', 'Z8激光腔', 'ZO', '个', '个', 903070.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('EG-30005', '透明釉', 'EG', '个', '个', 1404.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-527059000', '超乳接口', 'OP', '个', '个', 1700.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-613004000', 'HoneywellVacuumTransducer', 'OP', '个', '个', 7201.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CSO-100257320', '目镜', 'LS', '个', '个', 13018.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-FG-EU207A-D', '扩展卡', 'LS', '个', '个', 149.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-124585', '外置受话器', 'OT', '个', '个', 520.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-62812', '泡沫耳塞', 'WE', '个', '个', 7.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-1285314', '垫圈', 'CL', '个', '个', 27.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-125719', '方向性麦克风', 'OT', '个', '个', 221.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192223-1', '一步法23G截止阀套管', 'OP', '个', '个', 3588.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-OLIV-WF', '医用放大镜', 'OR', '个', '个', 9080.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14996', '光纤', 'IR', '个', '个', 23724.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.400.004', '开睑器', 'ZO', '个', '个', 8053.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-150488', '测量头线缆组', 'DS', '个', '个', 184570.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1005701S', '螺丝', 'HA', '个', '个', 29.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-114223-D', '22GAngledI/ACANNULA', 'OP', '个', '个', 3621.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.800.042', '联轴器', 'ZO', '个', '个', 663.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LU-8105-8023-00', 'Haag-Streit裂隙灯下巴架', 'LU', '个', '个', 10332.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HA-1020881-D', 'LED电源盒', 'HA', '个', '个', 7766.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-14120', '一次性使用眼内激光光纤探针', 'IR', '个', '个', 3885.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192703', '管道组', 'OP', '个', '个', 590.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OR-O2M', 'DIAGNOSTICLENSES(CONTINUED)', 'OR', '个', '个', 3256.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('WE-24106', 'LED耳灯', 'WE', '个', '个', 357.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IT-8002714', 'NOEPRENESEALINGPARTS015', 'IT', '个', '个', 1755.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-11454-1', 'D型连接线', 'IR', '个', '个', 55832.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OT-205544', '耳背式助听器', 'OT', '个', '个', 21840.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCHOLDER-124040-D', '支架', 'LS', '个', '个', 533.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G7-1', '电脑', 'LS', '个', '个', 6241.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DS-151174-D', '轮子', 'DS', '袋', '袋', 2858.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2018-000', '灯泡单元', 'CA', '个', '个', 2531.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CA-Y67-2474-000', '轴联耦合器', 'CA', '个', '个', 568.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.135', '显示屏', 'ZO', '个', '个', 46367.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KE-1128-P-1014', '直接检眼镜', 'KE', '台', '台', 7590.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('KO-60/80XE', '内皮计闪光灯', 'KO', '个', '个', 9885.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('II-DSC100', '耳镜', 'II', '个', '个', 10387.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.011', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.701.031', '飞秒激光治疗仪一次性手术包', 'ZO', '盒', '盒', 36340.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OP-192313-D', '23G玻切', 'OP', '个', '个', 3493.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-Z8-L30C-SH', 'OEMSHHEAD', 'LS', '个', '个', 17256.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-ZO-TEAMVIEWER', '软件', 'LS', '个', '个', 5727.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PC-HP400G3-D', '电脑', 'LS', '个', '个', 5115.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0702010', '小号BTE', 'LS', '个', '个', 4.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-CALIBRATOR', 'calibratorforrefractormeter', 'LS', '个', '个', 115830.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('OK-000040', '电脑', 'OK', '个', '个', 2445.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.194', 'Z8卡', 'ZO', '个', '个', 71947.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-A3MASSY020-AB', '手柄', 'HV', '个', '个', 1818.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('HV-D1MASSY169-AA', '连接盒', 'HV', '个', '个', 1818.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.019', 'ETX电脑卡', 'ZO', '个', '个', 42950.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('ZO-510.500.051', '手柄支架', 'ZO', '个', '个', 27817.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-Wheel', '轮子', 'LS', '个', '个', 30.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('CL-6405044', '海丁格刷', 'CL', '个', '个', 5360.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('IR-77022', '显示面板', 'IR', '个', '个', 39578.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GTX1050TI', '显卡', 'LS', '个', '个', 2620.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-811.25E', '脚踏板，折叠的', 'DM', '个', '个', 8976.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-31-019.21E', '座椅升降柱', 'DM', '个', '个', 25371.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-901.20E', '旋转臂附属充电', 'DM', '个', '个', 2193.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-320.14E', '拓普康CV5000适配器', 'DM', '个', '个', 2858.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-19-904.20E', '座椅把手', 'DM', '个', '个', 9082.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('DM-01-322.14E', '投射式视力表', 'DM', '个', '个', 2128.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-1102005', '软耳模用椭圆形钻7.0mm', 'LS', '个', '个', 108.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0505005', '枪状镊', 'LS', '个', '个', 39.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-0402011', '透明硬耳模材料500ml', 'LS', '个', '个', 935.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-YG-KL4Tabletop', 'KL4的桌面', 'LS', '个', '个', 490.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-LAMP-OCTOPUS', '刺激光灯泡', 'LS', '个', '个', 242.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-MACBOOK-PRO', '苹果笔记本电脑', 'LS', '个', '个', 17084.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PRINTER-EPL3108', '打印机', 'LS', '个', '个', 1071.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SHADING', '遮光布', 'LS', '个', '个', 121.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-GLASSES-HS100', 'HS100激光防护镜', 'LS', '个', '个', 2350.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-METALPLATE-D', '铁板', 'LS', '个', '个', 110.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCIE-COM', 'PCIE转接卡', 'LS', '个', '个', 209.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-DESKTOP700X520-66', '桌面', 'LS', '个', '个', 363.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-PCB-CAMERA-HS100', 'HS100校准用工业相机控制板', 'LS', '个', '个', 4350.00, 2, true);
INSERT INTO public.materials (code, "name", brand, unit, sale_unit, sale_price, region_id, is_active) VALUES('LS-SHADING-HOLDER', '遮光支架', 'LS', '个', '个', 198.00, 2, true);
