CREATE TABLE public.warehouse
(
  id         bigint                 GENERATED ALWAYS AS IDENTITY,
  created_at timestamptz            NOT NULL DEFAULT now(),
  updated_at timestamptz            NOT NULL DEFAULT now(),
  deleted    boolean                NOT NULL DEFAULT false,
  is_active  boolean                NOT NULL DEFAULT true,
  name       character varying(100) NOT NULL,
  code       character varying(100),
  region_id  bigint                ,
  PRIMARY KEY (id)
);
COMMENT ON TABLE public.warehouse IS '倉儲';
COMMENT ON COLUMN public.warehouse.created_at IS '資料新增日期';
COMMENT ON COLUMN public.warehouse.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.warehouse.deleted IS '資料刪除';
COMMENT ON COLUMN public.warehouse.is_active IS '是否啟用';
COMMENT ON COLUMN public.warehouse.name IS '名稱';
COMMENT ON COLUMN public.warehouse.code IS '編號';
COMMENT ON COLUMN public.warehouse.region_id IS '區域';

ALTER TABLE public.eye_fixed_assets ADD warehouse_id int8 NULL;
COMMENT ON COLUMN public.eye_fixed_assets.warehouse_id IS '倉儲';

ALTER TABLE public.eye_fixed_assets
  ADD CONSTRAINT eye_fixed_assets_warehouse_id_fkey
    FOREIGN KEY (warehouse_id)
    REFERENCES public.warehouse (id);

-- 資料匯入
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('A-E-SH2', '正品倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('A-E-SH3', '正品倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('A-E-SH', '眼科倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('B-E-LEND', '暫借倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('D-E-SH', '展示倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('S-E-LEND', '維修暫借倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('S-E-SH', '維修', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('S-REPAIR', '返修倉', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('T-OUTSIDE', '外部貨運行', 2);
INSERT INTO public.warehouse ("code", "name", "region_id") VALUES('T-TEST', '檢測倉', 2);