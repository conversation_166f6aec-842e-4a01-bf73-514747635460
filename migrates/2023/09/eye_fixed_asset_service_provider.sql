CREATE TABLE public.eye_fixed_asset_service_providers
(
  id         bigint                 GENERATED ALWAYS AS IDENTITY,
  created_at timestamptz            NOT NULL DEFAULT now(),
  updated_at timestamptz            NOT NULL DEFAULT now(),
  deleted    boolean                NOT NULL DEFAULT false,
  is_active  boolean                NOT NULL DEFAULT true,
  name       character varying(100) NOT NULL,
  code       character varying(100),
  region_id  bigint                ,
  PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_fixed_asset_service_providers IS '眼科固定資產服務單位';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.is_active IS '是否啟用';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.name IS '名稱';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.code IS '編號';
COMMENT ON COLUMN public.eye_fixed_asset_service_providers.region_id IS '區域';

CREATE TABLE public.eye_fixed_assets_service_providers
(
  eye_fixed_asset_id                  bigint NOT NULL,
  eye_fixed_asset_service_provider_id bigint NOT NULL,
  PRIMARY KEY (eye_fixed_asset_id, eye_fixed_asset_service_provider_id)
);
COMMENT ON TABLE public.eye_fixed_assets_service_providers IS '眼科固定資產服務單位關聯';
COMMENT ON COLUMN public.eye_fixed_assets_service_providers.eye_fixed_asset_id IS '眼科固定資產';
COMMENT ON COLUMN public.eye_fixed_assets_service_providers.eye_fixed_asset_service_provider_id IS '眼科固定資產服務單位';

ALTER TABLE public.eye_fixed_assets_service_providers
  ADD CONSTRAINT eye_fixed_assets_service_providers_efa_id_fkey
    FOREIGN KEY (eye_fixed_asset_id)
    REFERENCES public.eye_fixed_assets (id);

ALTER TABLE public.eye_fixed_assets_service_providers
  ADD CONSTRAINT eye_fixed_assets_service_providers_efasp_id_fkey
    FOREIGN KEY (eye_fixed_asset_service_provider_id)
    REFERENCES public.eye_fixed_asset_service_providers (id);

-- 資料匯入
INSERT INTO public.eye_fixed_asset_service_providers ("name", "code", "region_id") VALUES('业务', 'Sales', 2);
INSERT INTO public.eye_fixed_asset_service_providers ("name", "code", "region_id") VALUES('工程师', 'Engineers', 2);