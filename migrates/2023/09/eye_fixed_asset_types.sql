CREATE TABLE public.eye_fixed_asset_types
(
  id         bigint                 GENERATED ALWAYS AS IDENTITY,
  created_at timestamptz            NOT NULL DEFAULT now(),
  updated_at timestamptz            NOT NULL DEFAULT now(),
  deleted    boolean                NOT NULL DEFAULT false,
  is_active  boolean                NOT NULL DEFAULT true,
  name       character varying(100) NOT NULL,
  code       character varying(100) NOT NULL,
  region_id  bigint                 NOT NULL,
  PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_fixed_asset_types IS '眼科固定資產類別';

COMMENT ON COLUMN public.eye_fixed_asset_types.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_types.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_types.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_fixed_asset_types.is_active IS '是否啟用';
COMMENT ON COLUMN public.eye_fixed_asset_types.name IS '名稱';
COMMENT ON COLUMN public.eye_fixed_asset_types.code IS '編號';
COMMENT ON COLUMN public.eye_fixed_asset_types.region_id IS '區域';

ALTER TABLE public.eye_fixed_asset_types
  ADD CONSTRAINT eye_fixed_asset_types_region_id_fkey
  FOREIGN KEY (region_id)
  REFERENCES public.regions(id) ON UPDATE CASCADE;


CREATE TABLE public.eye_fixed_assets_types
(
  eye_fixed_asset_id      bigint NOT NULL,
  eye_fixed_asset_type_id bigint NOT NULL,
  PRIMARY KEY (eye_fixed_asset_id, eye_fixed_asset_type_id)
);
COMMENT ON TABLE public.eye_fixed_assets_types IS '眼科固定資產與類別關聯';

COMMENT ON COLUMN public.eye_fixed_assets_types.eye_fixed_asset_id IS '眼科固定資產';
COMMENT ON COLUMN public.eye_fixed_assets_types.eye_fixed_asset_type_id IS '眼科固定資產類別';

ALTER TABLE public.eye_fixed_assets_types
    ADD CONSTRAINT eye_fixed_assets_types_eye_fixed_asset_id_foreign 
    FOREIGN KEY (eye_fixed_asset_id) 
    REFERENCES public.eye_fixed_assets(id) ON UPDATE CASCADE;

ALTER TABLE public.eye_fixed_assets_types
    ADD CONSTRAINT eye_fixed_assets_types_eye_fixed_asset_type_id_foreign 
    FOREIGN KEY (eye_fixed_asset_type_id) 
    REFERENCES public.eye_fixed_asset_types(id) ON UPDATE CASCADE;

-- 預設資料
INSERT INTO public.eye_fixed_asset_types (created_at, updated_at, deleted, is_active, "name", code, region_id) VALUES('2023-08-04 13:45:56.942 +0800', '2023-08-04 13:45:56.942 +0800', false, true, '一般固资', 'GeneralFixedAssets', 2);
INSERT INTO public.eye_fixed_asset_types (created_at, updated_at, deleted, is_active, "name", code, region_id) VALUES('2023-08-04 13:45:56.942 +0800', '2023-08-04 13:45:56.942 +0800', false, true, '展示机', 'DemoMachines', 2);
