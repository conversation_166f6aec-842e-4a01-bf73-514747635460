-- 添加欄位: code
ALTER TABLE maintenance.eye_service_order_types ADD code varchar(30) NULL;
COMMENT ON COLUMN maintenance.eye_service_order_types.code IS '編號';

TRUNCATE TABLE maintenance.eye_service_order_types RESTART IDENTITY CASCADE;

INSERT INTO maintenance.eye_service_order_types ("name", view_order, code) VALUES('一般維修', 1, 'GeneralMaintenance');
INSERT INTO maintenance.eye_service_order_types ("name", view_order, code) VALUES('保固保養', 2, 'WarrantyMaintenance');
INSERT INTO maintenance.eye_service_order_types ("name", view_order, code) VALUES('固資租借', 2, 'FixedAssetRental');
