-- 添加: 主管
ALTER TABLE public.departments ADD manager_id int8 NULL;
COMMENT ON COLUMN public.departments.manager_id IS '主管';

ALTER TABLE  public.departments 
    ADD CONSTRAINT departments_manager_id_foreign FOREIGN KEY (manager_id) 
    REFERENCES public.users(id) ON UPDATE CASCADE;

-- 添加: 上層部門
ALTER TABLE public.departments ADD parent_id int8 NULL;
COMMENT ON COLUMN public.departments.parent_id IS '上層部門';

ALTER TABLE public.departments 
    ADD CONSTRAINT departments_parent_id_foreign FOREIGN KEY (parent_id) 
    REFERENCES public.departments(id) ON UPDATE CASCADE;

-- 添加: 同步來源
ALTER TABLE public.departments ADD sync_source_id int8 NULL;
COMMENT ON COLUMN public.departments.sync_source_id IS '同步來源 @GraphQLEnable=false';

ALTER TABLE public.departments ADD sync_code varchar(100) NULL;
COMMENT ON COLUMN public.departments.sync_code IS '同步代碼 @GraphQLEnable=false';

-- 預設時間為now()
ALTER TABLE public.departments ALTER COLUMN created_at SET DEFAULT now();
ALTER TABLE public.departments ALTER COLUMN updated_at SET DEFAULT now();
