-- 調度士
ALTER TABLE maintenance.eye_service_orders ADD dispatcher_user_id int8 NULL;
COMMENT ON COLUMN maintenance.eye_service_orders.dispatcher_user_id IS '調度士';

ALTER TABLE maintenance.eye_service_orders
  ADD CONSTRAINT eye_service_orders_dispatcher_user_id_fkey
    FOREIGN KEY (dispatcher_user_id) 
    REFERENCES public.users(id);

-- 成本中心
ALTER TABLE maintenance.eye_service_orders ADD cost_center_id int8 NULL;
COMMENT ON COLUMN maintenance.eye_service_orders.cost_center_id IS '成本中心';

ALTER TABLE maintenance.eye_service_orders
  ADD CONSTRAINT eye_service_orders_cost_center_id_fkey
    FOREIGN KEY (cost_center_id)
    REFERENCES public.cost_centers(id);

-- 核准狀態
ALTER TABLE maintenance.eye_service_orders ADD approval_status int NOT NULL;
COMMENT ON COLUMN maintenance.eye_service_orders.approval_status IS '核准狀態';

-- 公司 改為 nullable
ALTER TABLE maintenance.eye_service_orders ALTER COLUMN company_id DROP NOT NULL;

-- 異動dept_id
ALTER TABLE maintenance.eye_service_orders RENAME COLUMN dept_id TO assignee_dept_id;
COMMENT ON COLUMN maintenance.eye_service_orders.assignee_dept_id IS '指派部門';
