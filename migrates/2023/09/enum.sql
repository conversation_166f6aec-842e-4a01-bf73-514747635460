-- 添加服務單類別 enums
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_order_types', 'code', 'EnumEyeServiceOrderTypeCode', 'GeneralMaintenance', 'GeneralMaintenance', '一般維修', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_order_types', 'code', 'EnumEyeServiceOrderTypeCode', 'WarrantyMaintenance', 'WarrantyMaintenance', '保固保養', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_order_types', 'code', 'EnumEyeServiceOrderTypeCode', 'FixedAssetRental', 'FixedAssetRental', '固資租借', true);

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_orders', 'approval_status', 'EnumEyeServiceOrderApprovalStatus', 'Processing', '1', '核准中', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_orders', 'approval_status', 'EnumEyeServiceOrderApprovalStatus', 'Approved', '2', '已核准', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_orders', 'approval_status', 'EnumEyeServiceOrderApprovalStatus', 'Canceled', '0', '取消', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('maintenance', 'eye_service_orders', 'approval_status', 'EnumEyeServiceOrderApprovalStatus', 'Rejected', '-1', '拒絕', true);

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('sales_rep_workstation', 'visit_types', 'code', 'EnumVisitTypeCode', 'BusinessNotIncluded', 'Business_Not_Included', '無商機拜訪', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('sales_rep_workstation', 'visit_types', 'code', 'EnumVisitTypeCode', 'BusinessIncluded', 'Business_Included', '商機拜訪', true);

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_types', 'code', 'EnumEyeFixedAssetTypeCode', 'DemoMachines', 'DemoMachines', '展示機', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_types', 'code', 'EnumEyeFixedAssetTypeCode', 'GeneralFixedAssets', 'GeneralFixedAssets', '一般固資', true);

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_rental_objects', 'code', 'EnumEyeFixedAssetRentalObjectCode', 'Others', 'Others', '其他', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_rental_objects', 'code', 'EnumEyeFixedAssetRentalObjectCode', 'Exhibition', 'Exhibition', '展场', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_rental_objects', 'code', 'EnumEyeFixedAssetRentalObjectCode', 'Customer', 'Customer', '客户', true);

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_service_providers', 'code', 'EnumEyeFixedAssetServiceProviderCode', 'Sales', 'Sales', '业务', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", value, description, enabled) VALUES('public', 'eye_fixed_asset_service_providers', 'code', 'EnumEyeFixedAssetServiceProviderCode', 'Engineers', 'Engineers', '工程师', true);
