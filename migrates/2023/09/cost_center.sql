-- 成本中心

CREATE TABLE public.cost_centers
(
  id              bigint                 GENERATED ALWAYS AS IDENTITY,
  created_at      timestamptz            NOT NULL DEFAULT now(),
  updated_at      timestamptz            NOT NULL DEFAULT now(),
  deleted         boolean                DEFAULT false,
  is_active       boolean                DEFAULT true,
  name            character varying(100) NOT NULL,
  code            character varying(100) NOT NULL,
  region_id       int                    NOT NULL,
  company_id      bigint                 NOT NULL,
  PRIMARY KEY (id)
);

COMMENT ON TABLE public.cost_centers IS '成本中心';
COMMENT ON COLUMN public.cost_centers.created_at IS '資料新增日期';
COMMENT ON COLUMN public.cost_centers.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.cost_centers.deleted IS '資料刪除';
COMMENT ON COLUMN public.cost_centers.is_active IS '是否啟用';
COMMENT ON COLUMN public.cost_centers.name IS '名稱';
COMMENT ON COLUMN public.cost_centers.code IS '編號';
COMMENT ON COLUMN public.cost_centers.region_id IS '區域';
COMMENT ON COLUMN public.cost_centers.company_id IS '公司';

ALTER TABLE public.cost_centers
  ADD CONSTRAINT cost_centers_region_id_fkey
    FOREIGN KEY (region_id) 
    REFERENCES public.regions(id);

ALTER TABLE public.cost_centers
  ADD CONSTRAINT cost_centers_company_id_fkey
  FOREIGN KEY (company_id)
  REFERENCES public.companies(id);