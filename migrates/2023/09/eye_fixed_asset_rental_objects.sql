CREATE TABLE public.eye_fixed_asset_rental_objects
(
  id         bigint                 GENERATED ALWAYS AS IDENTITY,
  created_at timestamptz            NOT NULL DEFAULT now(),
  updated_at timestamptz            NOT NULL DEFAULT now(),
  deleted    boolean                NOT NULL DEFAULT false,
  is_active  boolean                NOT NULL DEFAULT true,
  name       character varying(100) NOT NULL,
  code       character varying(100),
  region_id  bigint                ,
  PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_fixed_asset_rental_objects IS '眼科固定資產租借對象';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.is_active IS '是否啟用';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.name IS '名稱';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.code IS '編號';
COMMENT ON COLUMN public.eye_fixed_asset_rental_objects.region_id IS '區域';

ALTER TABLE maintenance.eye_service_orders ADD eye_fixed_asset_rental_object_id int8 NULL;
COMMENT ON COLUMN maintenance.eye_service_orders.eye_fixed_asset_rental_object_id IS '眼科固定資產租借對象';

ALTER TABLE maintenance.eye_service_orders
  ADD CONSTRAINT eye_service_orders_rental_object_id_fkey
    FOREIGN KEY (eye_fixed_asset_rental_object_id)
    REFERENCES public.eye_fixed_asset_rental_objects (id);

-- 資料匯入
INSERT INTO public.eye_fixed_asset_rental_objects ("name", "code", "region_id") VALUES('客户', 'Customer', 2);
INSERT INTO public.eye_fixed_asset_rental_objects ("name", "code", "region_id") VALUES('展场', 'Exhibition', 2);
INSERT INTO public.eye_fixed_asset_rental_objects ("name", "code", "region_id") VALUES('其他', 'Others', 2);
