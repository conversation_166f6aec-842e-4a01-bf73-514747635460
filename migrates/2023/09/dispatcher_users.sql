-- 調度士

CREATE TABLE maintenance.dispatcher_users
(
  id              bigint                 GENERATED ALWAYS AS IDENTITY,
  created_at      timestamptz            NOT NULL DEFAULT now(),
  updated_at      timestamptz            NOT NULL DEFAULT now(),
  deleted         boolean                DEFAULT false,
  is_active       boolean                DEFAULT true,
  user_id         bigint                 NOT NULL,
  PRIMARY KEY (id)
);

COMMENT ON TABLE maintenance.dispatcher_users IS '調度士';
COMMENT ON COLUMN maintenance.dispatcher_users.created_at IS '資料新增日期';
COMMENT ON COLUMN maintenance.dispatcher_users.updated_at IS '資料修改日期';
COMMENT ON COLUMN maintenance.dispatcher_users.deleted IS '資料刪除';
COMMENT ON COLUMN maintenance.dispatcher_users.is_active IS '是否啟用';
COMMENT ON COLUMN maintenance.dispatcher_users.user_id IS '調度士';

ALTER TABLE maintenance.dispatcher_users
  ADD CONSTRAINT dispatcher_users_user_id_fkey
    FOREIGN KEY (user_id) 
    REFERENCES public.users(id);