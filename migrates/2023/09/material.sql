-- 添加與調整欄位
ALTER TABLE inventory.materials ADD sync_source_id int8 NULL;
COMMENT ON COLUMN inventory.materials.sync_source_id IS '同步來源';
ALTER TABLE inventory.materials ADD sync_code varchar(100) NULL;
COMMENT ON COLUMN inventory.materials.sync_code IS '同步代碼';


CREATE TABLE inventory.material_images
(
  id              BIGINT       NOT NULL GENERATED ALWAYS AS IDENTITY,
  created_at      TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
  updated_at      TIMESTAMPTZ  NOT NULL DEFAULT NOW(),
  created_user_id BIGINT      ,
  updated_user_id BIGINT      ,
  deleted         BOOL         NOT NULL DEFAULT false,
  material_id     BIGINT       NOT NULL,
  name            VARCHAR(300),
  extension       VARCHAR(30)  NOT NULL,
  s3_key          VARCHAR(300) NOT NULL,
  memo            TEXT        ,
  PRIMARY KEY (id)
);

COMMENT ON TABLE inventory.material_images IS '料號圖片';
COMMENT ON COLUMN inventory.material_images.name IS '檔案名稱';
COMMENT ON COLUMN inventory.material_images.extension IS '副檔名';
COMMENT ON COLUMN inventory.material_images.s3_key IS 'S3 key';
COMMENT ON COLUMN inventory.material_images.memo IS '備註';

CREATE TABLE inventory.sync_source
(
  id   BIGINT       NOT NULL GENERATED ALWAYS AS IDENTITY,
  name VARCHAR(100) NOT NULL,
  code VARCHAR(100),
  PRIMARY KEY (id)
);
COMMENT ON TABLE inventory.sync_source IS '同步來源';

-- inventory.materials
ALTER TABLE inventory.materials
  ADD CONSTRAINT materials_sync_source_id_fkey
    FOREIGN KEY (sync_source_id)
    REFERENCES inventory.sync_source (id);

-- inventory.material_images
ALTER TABLE inventory.material_images
  ADD CONSTRAINT material_images_material_id_fkey
    FOREIGN KEY (material_id)
    REFERENCES inventory.materials (id);

INSERT INTO inventory.sync_source ("name", code) VALUES('用友', 'yonyou');
