CREATE TABLE maintenance.eye_service_order_work_diary_check_ins
(
  id                              bigint      NOT NULL GENERATED ALWAYS AS IDENTITY,
  deleted                         boolean     DEFAULT false,
  created_at                      timestamptz NOT NULL DEFAULT now(),
  updated_at                      timestamptz NOT NULL DEFAULT now(),
  created_user_id                 bigint     ,
  updated_user_id                 bigint     ,
  eye_service_order_work_diary_id bigint      NOT NULL,
  lat                             float8     ,
  lng                             float8     ,
  content                         text       ,
  PRIMARY KEY (id)
);
COMMENT ON TABLE maintenance.eye_service_order_work_diary_check_ins IS '眼科服務單工作記錄打卡紀錄';

-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.deleted IS '資料刪除';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.created_at IS '資料新增日期';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.updated_at IS '資料修改日期';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.created_user_id IS '資料建立人員';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.updated_user_id IS '資料修改人員';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.eye_service_order_work_diary_id IS '眼科服務單工作記錄';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.lat IS 'WGS84 緯度';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.lng IS 'WGS84 經度';
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_check_ins.content IS '內容';


-- maintenance.eye_service_order_work_diary_check_ins foreign keys

ALTER TABLE maintenance.eye_service_order_work_diary_check_ins 
  ADD CONSTRAINT eye_service_order_work_diary_check_ins_created_user_id_fkey 
    FOREIGN KEY (created_user_id) 
    REFERENCES public.users(id);

ALTER TABLE maintenance.eye_service_order_work_diary_check_ins 
  ADD CONSTRAINT eye_service_order_work_diary_check_ins_updated_user_id_fkey 
    FOREIGN KEY (updated_user_id) 
    REFERENCES public.users(id);

ALTER TABLE maintenance.eye_service_order_work_diary_check_ins 
  ADD CONSTRAINT eye_service_order_work_diary_check_ins_esowd_id_fkey 
    FOREIGN KEY (eye_service_order_work_diary_id) 
    REFERENCES maintenance.eye_service_order_work_diaries (id);