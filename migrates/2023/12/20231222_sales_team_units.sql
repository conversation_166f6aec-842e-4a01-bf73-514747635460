-- START: http://asking.clinico.com.tw/issues/92957
ALTER TABLE sales_rep_workstation.visits ADD COLUMN sales_team_unit_id INT8 NULL;
COMMENT ON COLUMN sales_rep_workstation.visits.sales_team_unit_id IS '業務團隊單位';
ALTER TABLE sales_rep_workstation.visits
	ADD CONSTRAINT visits_sales_team_unit_id_foreign
	FOREIGN KEY (sales_team_unit_id)
	REFERENCES sales_rep_workstation.sales_team_units (id)
	ON UPDATE CASCADE;

ALTER TABLE sales_rep_workstation.businesses ADD COLUMN sales_team_unit_id INT8 NULL;
COMMENT ON COLUMN sales_rep_workstation.businesses.sales_team_unit_id IS '業務團隊單位';
ALTER TABLE sales_rep_workstation.businesses
	ADD CONSTRAINT businesses_sales_team_unit_id_foreign
	FOREIGN KEY (sales_team_unit_id)
	REFERENCES sales_rep_workstation.sales_team_units (id)
	ON UPDATE CASCADE;

CREATE TABLE sales_rep_workstation.contact_people_sales_team_units (
	contact_person_id int8 NOT NULL, -- 客戶
	sales_team_unit_id int8 NOT NULL, -- 業務團隊單位
	CONSTRAINT contact_people_sales_team_units_pkey PRIMARY KEY (contact_person_id, sales_team_unit_id)
);
COMMENT ON TABLE sales_rep_workstation.contact_people_sales_team_units IS '聯絡人與業務團隊單位關聯';

COMMENT ON COLUMN sales_rep_workstation.contact_people_sales_team_units.contact_person_id IS '聯絡人';
COMMENT ON COLUMN sales_rep_workstation.contact_people_sales_team_units.sales_team_unit_id IS '業務團隊單位';


ALTER TABLE sales_rep_workstation.contact_people_sales_team_units
	ADD CONSTRAINT contact_people_sales_team_units_contact_person_id_foreign FOREIGN KEY (contact_person_id)
	REFERENCES sales_rep_workstation.contact_people (id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.contact_people_sales_team_units
	ADD CONSTRAINT contact_people_sales_team_units_sales_team_unit_id_foreign FOREIGN KEY (sales_team_unit_id)
	REFERENCES sales_rep_workstation.sales_team_units (id) ON UPDATE CASCADE;
-- END: http://asking.clinico.com.tw/issues/92957
