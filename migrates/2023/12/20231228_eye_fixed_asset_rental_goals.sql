-- START: http://asking.clinico.com.tw/issues/94393
CREATE TABLE public.eye_fixed_asset_rental_goals (
	id int8 NOT NULL GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
	created_at timestamptz NOT NULL DEFAULT now(), -- 資料新增日期
	updated_at timestamptz NOT NULL DEFAULT now(), -- 資料修改日期
	deleted bool NOT NULL DEFAULT false, -- 資料刪除
	is_active bool NOT NULL DEFAULT true, -- 是否啟用
	name varchar(100) NOT NULL, -- 名稱
	code varchar(100) NULL, -- 編號
	region_id int8 NULL, -- 區域
	CONSTRAINT eye_fixed_asset_rental_goals_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_fixed_asset_rental_goals IS '眼科固定資產租借目的';

COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.is_active IS '是否啟用';
COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.name IS '名稱';
COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.code IS '編號';
COMMENT ON COLUMN public.eye_fixed_asset_rental_goals.region_id IS '區域';

INSERT INTO public.eye_fixed_asset_rental_goals
	(name, code, region_id)
VALUES
	('展会', NULL, 1),
	('客户展示', NULL, 1),
	('维修顶用', NULL, 1),
	('其他（培训）', NULL, 1);

ALTER TABLE maintenance.eye_service_orders ADD COLUMN eye_fixed_asset_rental_goal_id INT8 NULL;

COMMENT ON COLUMN maintenance.eye_service_orders.eye_fixed_asset_rental_goal_id IS '眼科固定資產租借目的';

ALTER TABLE maintenance.eye_service_orders
	ADD CONSTRAINT eye_service_eye_fixed_asset_rental_goal_id_fkey
	FOREIGN KEY (eye_fixed_asset_rental_goal_id)
	REFERENCES public.eye_fixed_asset_rental_goals (id);
-- END: http://asking.clinico.com.tw/issues/94393
