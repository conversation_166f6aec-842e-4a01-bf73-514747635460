--- START: http://asking.clinico.com.tw/issues/90936

CREATE TABLE sales_rep_workstation.sales_team_units (
    id bigserial NOT NULL,
    sales_team_id int8 NOT NULL, -- 業務團隊
    "name" varchar(100) NOT NULL, -- 名稱
    view_order int4 NULL, -- 檢視順序
    deleted bool NOT NULL DEFAULT false, -- 資料刪除
    created_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料新增日期
    updated_at timestamptz(0) NOT NULL DEFAULT now(), -- 資料修改日期
    user_id int8 NULL, -- 負責業務
    CONSTRAINT sales_team_units_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.sales_team_units IS '業務團隊位置';

COMMENT ON COLUMN sales_rep_workstation.sales_team_units.sales_team_id IS '業務團隊';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units."name" IS '名稱';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.view_order IS '檢視順序';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.deleted IS '資料刪除';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.created_at IS '資料新增日期';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.updated_at IS '資料修改日期';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.user_id IS '負責業務';

ALTER TABLE sales_rep_workstation.sales_team_units
    ADD CONSTRAINT sales_team_units_sales_team_id_foreign FOREIGN KEY (sales_team_id)
    REFERENCES sales_rep_workstation.sales_teams(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.sales_team_units
    ADD CONSTRAINT sales_team_units_user_id_foreign FOREIGN KEY (user_id)
    REFERENCES public.users(id) ON UPDATE CASCADE;

CREATE TABLE sales_rep_workstation.customers_sales_team_units (
    customer_id int8 NOT NULL, -- 客戶
    sales_team_unit_id int8 NOT NULL, -- 業務團隊單位
    CONSTRAINT customers_sales_team_units_pkey PRIMARY KEY (customer_id, sales_team_unit_id)
);
COMMENT ON TABLE sales_rep_workstation.customers_sales_team_units IS '客戶與業務團隊單位關聯';

COMMENT ON COLUMN sales_rep_workstation.customers_sales_team_units.customer_id IS '客戶';
COMMENT ON COLUMN sales_rep_workstation.customers_sales_team_units.sales_team_unit_id IS '業務團隊單位';

ALTER TABLE sales_rep_workstation.customers_sales_team_units
    ADD CONSTRAINT customers_sales_team_units_customer_id_foreign FOREIGN KEY (customer_id)
    REFERENCES sales_rep_workstation.customers(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customers_sales_team_units
    ADD CONSTRAINT customers_sales_team_units_sales_team_unit_id_foreign FOREIGN KEY (sales_team_unit_id)
    REFERENCES sales_rep_workstation.sales_team_units(id) ON UPDATE CASCADE;

--- END: http://asking.clinico.com.tw/issues/90936