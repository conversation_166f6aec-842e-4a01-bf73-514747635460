-- START: http://asking.clinico.com.tw/issues/94241
ALTER TABLE sales_rep_workstation.visits ADD COLUMN has_assisted_visit_supervisor bool NOT NULL DEFAULT FALSE;
COMMENT ON COLUMN sales_rep_workstation.visits.has_assisted_visit_supervisor IS '是否主管協訪';

ALTER TABLE sales_rep_workstation.visits ADD COLUMN assisted_visit_supervisor_id INT8;
COMMENT ON COLUMN sales_rep_workstation.visits.assisted_visit_supervisor_id IS '協訪主管';

ALTER TABLE sales_rep_workstation.visits
    ADD CONSTRAINT visits_assisted_visit_supervisor_id_foreign
    FOREIGN KEY (assisted_visit_supervisor_id)
    REFERENCES public.users (id) ON UPDATE CASCADE;
-- END: http://asking.clinico.com.tw/issues/94241
