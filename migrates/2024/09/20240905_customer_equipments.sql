CREATE TABLE sales_rep_workstation.customer_equipments (
	id bigserial NOT NULL,
	customer_id int8 NOT NULL,
    competitor_id int8 NOT NULL,  -- 競爭對手id
    manufacture_year int8 NOT NULL,              -- 出廠年份(西元）
    competitor_price int4 NULL,      -- 競品售價
    notes text,
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
    deleted bool DEFAULT false NOT NULL,
    CONSTRAINT customer_equipments_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.customer_equipments ADD CONSTRAINT customer_equipments_customer_id_foreign FOREIGN KEY (customer_id) REFERENCES sales_rep_workstation.customers(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_equipments ADD CONSTRAINT customer_equipments_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_equipments ADD CONSTRAINT customer_equipments_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_equipments ADD CONSTRAINT customer_equipments_competitor_id_foreign FOREIGN KEY (competitor_id) REFERENCES sales_rep_workstation.competitors(id) ON UPDATE CASCADE;