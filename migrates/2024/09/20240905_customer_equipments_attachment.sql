CREATE TABLE sales_rep_workstation.customer_equipment_attachments (
    id bigserial NOT NULL,
    equipment_id int8 NOT NULL,                    -- 關聯到設備的 ID
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"name" varchar(300) NOT NULL,
	s3_key varchar(300) NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	"extension" varchar(30) NOT NULL,
    CONSTRAINT customer_equipment_attachments_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.customer_equipment_attachments ADD CONSTRAINT customer_equipment_attachments_equipment_id_foreign FOREIGN KEY (equipment_id) REFERENCES sales_rep_workstation.customer_equipments(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_equipment_attachments ADD CONSTRAINT customer_equipment_attachments_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_equipment_attachments ADD CONSTRAINT customer_equipment_attachments_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
