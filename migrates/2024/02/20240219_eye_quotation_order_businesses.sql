-- START: http://asking.clinico.com.tw/issues/99377
CREATE TABLE eye_quotation_order_businesses (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    eye_quotation_order_id int8 NOT NULL,
    business_id int8 NOT NULL,
    CONSTRAINT eye_quotation_order_businesses_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_quotation_order_businesses IS '眼科報價單商機';

COMMENT ON COLUMN public.eye_quotation_order_businesses.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_quotation_order_businesses.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_quotation_order_businesses.eye_quotation_order_id IS '眼科報價單';
COMMENT ON COLUMN public.eye_quotation_order_businesses.business_id IS '商機';

ALTER TABLE public.eye_quotation_order_businesses
  ADD CONSTRAINT eye_quotation_order_businesses_eye_quotation_order_id_fkey
  FOREIGN KEY (eye_quotation_order_id) REFERENCES public.eye_quotation_orders (id);
ALTER TABLE public.eye_quotation_order_businesses
  ADD CONSTRAINT eye_quotation_order_businesses_business_id_fkey
  FOREIGN KEY (business_id) REFERENCES sales_rep_workstation.businesses (id);

COMMENT ON COLUMN public.eye_quotation_orders.business_id IS '商機（棄用；改用多對一關聯）';
-- END: http://asking.clinico.com.tw/issues/99377

COMMENT ON COLUMN public.eye_quotation_order_product_items.eye_quotation_order_id IS '眼科報價單';
COMMENT ON COLUMN public.eye_quotation_order_product_items.eye_quotation_order_product_id IS '眼科報價單商品';
COMMENT ON COLUMN public.eye_quotation_order_product_items.eye_quotation_order_promotion_id IS '眼科報價單優惠';

COMMENT ON COLUMN public.eye_quotation_order_products.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_quotation_order_products.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_quotation_order_products.eye_quotation_order_id IS '眼科報價單';
COMMENT ON COLUMN public.eye_quotation_order_products.eye_product_id IS '眼科商品';
COMMENT ON COLUMN public.eye_quotation_order_products.qty IS '數量';

COMMENT ON COLUMN public.eye_quotation_order_promotions.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_quotation_order_promotions.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_quotation_order_promotions.eye_quotation_order_id IS '眼科報價單';
COMMENT ON COLUMN public.eye_quotation_order_promotions.eye_promotion_id IS '眼科優惠';