CREATE TABLE maintenance.eye_service_order_extensions (
	id int8 NOT NULL GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
	created_at timestamptz NOT NULL DEFAULT now(),
	updated_at timestamptz NOT NULL DEFAULT now(),
	deleted bool NOT NULL DEFAULT false,
	eye_service_order_id int8 NOT NULL,
	extend_date1 date NOT NULL,
	extend_date2 date NOT NULL,
	memo text NULL,
	approval_status int4 NOT NULL DEFAULT 1,
	bpm_instance_id varchar(128) NULL,
	CONSTRAINT eye_service_order_extends_pkey PRIMARY KEY (id),
	CONSTRAINT eye_service_order_extends_eye_service_order_id_fkey FOREIGN KEY (eye_service_order_id) REFERENCES maintenance.eye_service_orders(id)
);
CREATE INDEX ix_eye_service_order_extensions_eye_service_order_id ON maintenance.eye_service_order_extensions USING btree (eye_service_order_id);

INSERT INTO generator.enums ("schema","table","column",name,"key",value,description,enabled) VALUES
	('maintenance','eye_service_order_extensions','approval_status','EnumEyeServiceOrderExtensionApprovalStatus','Rejected','-1','拒絕',true),
	('maintenance','eye_service_order_extensions','approval_status','EnumEyeServiceOrderExtensionApprovalStatus','Canceled','0','取消',true),
	('maintenance','eye_service_order_extensions','approval_status','EnumEyeServiceOrderExtensionApprovalStatus','Processing','1','核准中',true),
	('maintenance','eye_service_order_extensions','approval_status','EnumEyeServiceOrderExtensionApprovalStatus','Approved','2','已核准',true);