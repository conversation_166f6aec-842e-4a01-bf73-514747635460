-- START: http://asking.clinico.com.tw/issues/96416
CREATE TABLE public.eye_quotation_order_types (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY(INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    region_id int4 NOT NULL,
    company_id int8 NOT NULL,
    name varchar(30) NOT NULL,
    code varchar(100) NULL,
    view_order int4 NULL,
    CONSTRAINT eye_quotation_order_types_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_quotation_order_types IS '眼科報價單類別';

COMMENT ON COLUMN public.eye_quotation_order_types.created_at IS '資料新增日期';
COMMENT ON COLUMN public.eye_quotation_order_types.updated_at IS '資料修改日期';
COMMENT ON COLUMN public.eye_quotation_order_types.deleted IS '資料刪除';
COMMENT ON COLUMN public.eye_quotation_order_types.region_id IS '區域';
COMMENT ON COLUMN public.eye_quotation_order_types.company_id IS '公司';
COMMENT ON COLUMN public.eye_quotation_order_types.name IS '名稱';
COMMENT ON COLUMN public.eye_quotation_order_types.code IS '編號';
COMMENT ON COLUMN public.eye_quotation_order_types.view_order IS '檢視順序';

ALTER TABLE public.eye_quotation_order_types
    ADD CONSTRAINT eye_quotation_order_types_region_id_fkey
    FOREIGN KEY (region_id) REFERENCES public.regions (id) ON UPDATE CASCADE;
ALTER TABLE public.eye_quotation_order_types
    ADD CONSTRAINT eye_quotation_order_types_company_id_fkey
    FOREIGN KEY (company_id) REFERENCES public.companies (id) ON UPDATE CASCADE;

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", "description", "enabled")
    VALUES ('public', 'eye_quotation_order_types', 'code', 'EnumEyeQuotationOrderType', 'Sale', 'Sale', '銷售', true);
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", "description", "enabled")
    VALUES ('public', 'eye_quotation_order_types', 'code', 'EnumEyeQuotationOrderType', 'Repair', 'Repair', '維修', true);

INSERT INTO public.eye_quotation_order_types ("region_id", "company_id", "name", "code", "view_order")
	VALUES (2, 32, '销售', 'Sale', 1);
INSERT INTO public.eye_quotation_order_types ("region_id", "company_id", "name", "code", "view_order")
	VALUES (2, 32, '维修', 'Repair', 2);



ALTER TABLE public.eye_quotation_orders
    ADD COLUMN eye_quotation_order_type_id int8 NULL;

COMMENT ON COLUMN public.eye_quotation_orders.eye_quotation_order_type_id IS '眼科報價單類別';

ALTER TABLE public.eye_quotation_orders
    ADD CONSTRAINT eye_quotation_orders_eye_quotation_order_type_id_fkey
    FOREIGN KEY (eye_quotation_order_type_id) REFERENCES public.eye_quotation_order_types (id) ON UPDATE CASCADE;

-- END: http://asking.clinico.com.tw/issues/96416