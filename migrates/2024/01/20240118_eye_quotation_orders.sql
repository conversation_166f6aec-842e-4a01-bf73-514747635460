ALTER TABLE public.eye_quotation_orders ADD bpm_official_seal_instance_id varchar(128) NULL;
COMMENT ON COLUMN public.eye_quotation_orders.bpm_official_seal_instance_id IS '用印BPM id';

ALTER TABLE public.eye_quotation_orders ADD official_seal_status int4 NULL;
COMMENT ON COLUMN public.eye_quotation_orders.official_seal_status IS '用印狀態';

INSERT INTO generator.enums ("schema","table","column","name","key",value,description,enabled) VALUES
	('public','eye_quotation_orders','official_seal_status','EnumEyeQuotationOrderOfficialSealStatus','Approved','3','完成',true),
	('public','eye_quotation_orders','official_seal_status','EnumEyeQuotationOrderOfficialSealStatus','Canceled','0','取消',true),
	('public','eye_quotation_orders','official_seal_status','EnumEyeQuotationOrderOfficialSealStatus','Draft','1','草稿',false),
	('public','eye_quotation_orders','official_seal_status','EnumEyeQuotationOrderOfficialSealStatus','Processing','2','送簽中',true),
	('public','eye_quotation_orders','official_seal_status','EnumEyeQuotationOrderOfficialSealStatus','Rejected','4','拒絕',true);