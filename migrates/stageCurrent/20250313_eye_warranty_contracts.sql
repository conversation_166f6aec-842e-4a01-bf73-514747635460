ALTER TABLE maintenance.eye_warranty_contracts ADD warranty_buy_type int4 null;
COMMENT ON COLUMN maintenance.eye_warranty_contracts.warranty_buy_type IS '保固合约类别';

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", "description", "enabled") 
VALUES ('maintenance', 'eye_warranty_contracts', 'warranty_buy_type', 'EnumEyeWarrantyContractWarrantyBuyType', 'NotBuy', '1', '不買合約', 't'),
       ('maintenance', 'eye_warranty_contracts', 'warranty_buy_type', 'EnumEyeWarrantyContractWarrantyBuyType', 'BuyWithProduct', '2', '與商品一起買合約', 't'),
       ('maintenance', 'eye_warranty_contracts', 'warranty_buy_type', 'EnumEyeWarrantyContractWarrantyBuyType', 'BuySeparately', '3', '單買合約', 't');