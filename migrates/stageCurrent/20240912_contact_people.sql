ALTER TABLE sales_rep_workstation.contact_people ADD type_id int8 NULL;
ALTER TABLE sales_rep_workstation.contact_people ADD CONSTRAINT contact_people_types_id_foreign FOREIGN KEY (type_id) REFERENCES sales_rep_workstation.contact_people_types(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.contact_people ADD bank_branch varchar(30) NULL;
ALTER TABLE sales_rep_workstation.contact_people ADD bank_card_number varchar(30) NULL;
COMMENT ON COLUMN sales_rep_workstation.contact_people.type_id IS '聯絡人分級';
COMMENT ON COLUMN sales_rep_workstation.contact_people.bank_branch IS '銀行分行';
COMMENT ON COLUMN sales_rep_workstation.contact_people.bank_card_number IS '銀行卡號';