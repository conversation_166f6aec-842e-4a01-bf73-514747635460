CREATE TABLE sales_rep_workstation.contact_people_attachments (
    id bigserial NOT NULL,
    contact_people_id int8 NOT NULL,                    
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"name" varchar(300) NOT NULL,
	s3_key varchar(300) NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	"extension" varchar(30) NOT NULL,
    CONSTRAINT contact_people_attachments_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.contact_people_attachments ADD CONSTRAINT contact_people_attachments_contact_people_id_foreign FOREIGN KEY (contact_people_id) REFERENCES sales_rep_workstation.contact_people(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.contact_people_attachments ADD CONSTRAINT contact_people_attachments_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.contact_people_attachments ADD CONSTRAINT contact_people_attachments_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;