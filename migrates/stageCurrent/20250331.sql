CREATE TABLE maintenance.logistics_suppliers (
	id bigserial NOT NULL,
	"name" varchar(100) NOT NULL,
	view_order int4 NULL,
	deleted bool DEFAULT false NOT NULL,
	created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	CONSTRAINT logistics_suppliers_pkey PRIMARY KEY (id)
)
COMMENT ON TABLE maintenance.logistics_suppliers IS '物流供货商';

INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('申通快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('韵达快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('圆通速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中通快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('EMS 邮政特快专递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('顺丰速运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('百世快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('宅急送');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中国邮政');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('德邦快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('安能物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('百福东方物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('传喜物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('D 速物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('递四方');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('E 邮宝');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('港中能达物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('GSM 快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('GATI 快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('国际邮件查询');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('国内邮件查询');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('共速达');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('国通快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('港快速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('高铁速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('冠达快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('华宇物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('恒路物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('好来运快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('华夏龙物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('海航天天');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('河北建华');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('海盟速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('华企快运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('汇强快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('昊盛物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('户通物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('华航快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('黄马甲快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('佳吉物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('佳怡物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('加运美快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('急先达物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('京广速递快件');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('晋越快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('嘉里大通');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('捷特快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('久易快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('京东快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('跨越物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('快优达速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('LDXpress');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('联昊通物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('龙邦速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('联邦快递（国内）');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('乐捷递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('立即送');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('民航快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('能达速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('平安达腾飞');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('陪行物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('全一快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('全日通快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('全晨快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('7 天连锁物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('如风达快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('日昱物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('速尔快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('山东海红');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('10 盛辉物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('盛丰物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('上大物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('三态速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('赛澳递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('圣安物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('穗佳物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('沈阳佳惠尔');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('上海林道货运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('天天快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('天地华宇');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('通和天下');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('UC 优速快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('万象物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('微特派');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('万家物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('希优特快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('新邦物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('信丰物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('新蛋物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('祥龙运通物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('西安城联速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('运通快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('邮政国内');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('远成物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('亚风速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('亿顺航');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('越丰物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('源安达快递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('原飞航物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('银捷速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('一统飞鸿');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('宇鑫物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('易通达');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('邮必佳');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('一柒物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('音素快运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('亿领速运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('煜嘉物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中铁快运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中铁物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中邮物流');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中国东方 (COE)');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('芝麻开门');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中速快件');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中天万运');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中睿速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('中外运速递');
INSERT INTO maintenance.logistics_suppliers (name) 
VALUES ('增益速递');



ALTER TABLE maintenance.eye_service_order_work_diaries ADD logistics_supplier_id int4 NULL;
COMMENT ON COLUMN maintenance.eye_service_order_work_diaries.logistics_supplier_id IS '物流供货商';

ALTER TABLE maintenance.eye_service_order_work_diaries ADD logistics_number varchar(100) NULL;
COMMENT ON COLUMN maintenance.eye_service_order_work_diaries.logistics_number IS '物流单号';

ALTER TABLE maintenance.eye_service_order_work_diaries
    ADD CONSTRAINT work_diaries_logistics_supplier_id_fkey
    FOREIGN KEY (logistics_supplier_id)
    REFERENCES maintenance.logistics_suppliers (id)
    ON UPDATE CASCADE;