ALTER TABLE sales_rep_workstation.sales_team_units ADD on_duty_date date NULL;
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.on_duty_date IS '到岗日期';

ALTER TABLE maintenance.eye_service_order_work_diaries ADD contact_people_id int8 NULL;
COMMENT ON COLUMN maintenance.eye_service_order_work_diaries.contact_people_id IS '联络人';

ALTER TABLE maintenance.eye_service_order_work_diaries
    ADD CONSTRAINT work_diaries_contact_people_id_fkey
    FOREIGN KEY (contact_people_id)
    REFERENCES sales_rep_workstation.contact_people(id)
    ON UPDATE CASCADE;
