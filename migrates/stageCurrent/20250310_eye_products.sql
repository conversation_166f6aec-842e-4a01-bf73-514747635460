ALTER TABLE public.eye_products ADD single_currency_id int8 NULL;
COMMENT ON COLUMN public.eye_products.single_currency_id IS '单台奖金币别';
ALTER TABLE public.eye_products ADD shipping_guidance_price_bonus numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.shipping_guidance_price_bonus IS '出货指导售价奖金';
ALTER TABLE public.eye_products ADD dsm_price_bonus numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.dsm_price_bonus IS 'DSM价格奖金';
ALTER TABLE public.eye_products ADD rsm_price_bonus numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.rsm_price_bonus IS 'RSM价格奖金';
ALTER TABLE public.eye_products ADD nsm_price_bonus numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.nsm_price_bonus IS 'NSM价格奖金';
ALTER TABLE public.eye_products ADD general_manager_price_bonus numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.general_manager_price_bonus IS '总经理价格奖金';
ALTER TABLE public.eye_products ADD group_bonus numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.group_bonus IS '集团奖金';
ALTER TABLE public.eye_products ADD hospital_suggest_sale_price numeric(12,2) NULL;
COMMENT ON COLUMN public.eye_products.hospital_suggest_sale_price IS '医院建议售价';

ALTER TABLE public.eye_products
    ADD CONSTRAINT eye_product_single_currency_id_fkey
    FOREIGN KEY (single_currency_id)
    REFERENCES public.currencies (id)
    ON UPDATE CASCADE;