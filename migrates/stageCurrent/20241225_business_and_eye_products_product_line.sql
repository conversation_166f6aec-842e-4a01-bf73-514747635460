CREATE TABLE public.eye_products_to_product_lines (
    eye_product_id int8 not null,
    product_line_id int8 not null,
    CONSTRAINT eye_products_to_product_lines_pkey PRIMARY KEY (eye_product_id, product_line_id)
);

ALTER TABLE
    public.eye_products_to_product_lines
ADD
    CONSTRAINT eye_products_to_product_lines_eye_product_id_foreign FOREIGN KEY (eye_product_id) REFERENCES public.eye_products(id) ON UPDATE CASCADE;

ALTER TABLE
    public.eye_products_to_product_lines
ADD
    CONSTRAINT eye_products_to_product_lines_product_line_id_foreign FOREIGN KEY (product_line_id) REFERENCES sales_rep_workstation.product_lines(id) ON UPDATE CASCADE;

CREATE TABLE sales_rep_workstation.business_products_to_product_lines (
    business_product_id int8 not null,
    product_line_id int8 not null,
    CONSTRAINT business_products_to_product_lines_pkey PRIMARY KEY (business_product_id, product_line_id)
);

ALTER TABLE
    sales_rep_workstation.business_products_to_product_lines
ADD
    CONSTRAINT business_products_to_product_lines_business_product_id_foreign FOREIGN KEY (business_product_id) REFERENCES sales_rep_workstation.business_products(id) ON UPDATE CASCADE;

ALTER TABLE
    sales_rep_workstation.business_products_to_product_lines
ADD
    CONSTRAINT business_products_to_product_lines_product_line_id_foreign FOREIGN KEY (product_line_id) REFERENCES sales_rep_workstation.product_lines(id) ON UPDATE CASCADE;