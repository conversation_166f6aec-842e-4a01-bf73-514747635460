CREATE TABLE sales_rep_workstation.product_lines (
    id bigserial NOT NULL,
    name varchar(100) NOT NULL,
    region_id int4 NOT NULL,
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool DEFAULT false NOT NULL,
    CONSTRAINT product_lines_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.product_lines IS '产品线';

ALTER TABLE sales_rep_workstation.sales_team_units ADD product_line_id int4 NULL;
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.product_line_id IS '产品线';
ALTER TABLE sales_rep_workstation.sales_team_units
  ADD CONSTRAINT sales_team_units_product_line_id_fkey FOREIGN KEY (product_line_id) REFERENCES sales_rep_workstation.product_lines (id) ON UPDATE CASCADE;

ALTER TABLE sales_rep_workstation.business_products ADD product_line_id int4 NULL;
COMMENT ON COLUMN sales_rep_workstation.business_products.product_line_id IS '产品线';
ALTER TABLE sales_rep_workstation.business_products
  ADD CONSTRAINT business_products_product_line_id_fkey FOREIGN KEY (product_line_id) REFERENCES sales_rep_workstation.product_lines (id) ON UPDATE CASCADE;

ALTER TABLE public.eye_products ADD product_line_id int4 NULL;
COMMENT ON COLUMN public.eye_products.product_line_id IS '产品线';
ALTER TABLE public.eye_products
  ADD CONSTRAINT eye_products_product_line_id_fkey FOREIGN KEY (product_line_id) REFERENCES sales_rep_workstation.product_lines (id) ON UPDATE CASCADE;

INSERT INTO sales_rep_workstation.product_lines ( "name", "region_id" )
VALUES
	( 'DI - 数字影像', 2 ),   
	( 'LT - 激光治疗', 2 ),
    ( 'OT - 检查 & 其他', 2 ),
    ( 'MT - 全能', 2 ),
    ( 'KA - 全龙 Key Account', 2 );