-- 已更新至正式区数据库
CREATE TABLE sales_rep_workstation.sales_target_years (
    id bigserial NOT NULL,
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool DEFAULT false NOT NULL,
    year int4 NOT NULL,
    CONSTRAINT sales_target_years_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.sales_target_years IS '指标年份';

INSERT INTO sales_rep_workstation.sales_target_years (year) 
VALUES (2025), (2026), (2027), (2028), (2029), (2030);

ALTER TABLE sales_rep_workstation.sales_targets ADD sales_target_year_id int4 NULL;
ALTER TABLE sales_rep_workstation.sales_targets ADD eye_product_group_id int8 NULL;
COMMENT ON COLUMN sales_rep_workstation.sales_targets.sales_target_year_id IS '年份';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.eye_product_group_id IS '眼科商品群组';
