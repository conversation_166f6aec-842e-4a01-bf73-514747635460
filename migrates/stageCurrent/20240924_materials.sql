ALTER TABLE inventory.materials ADD internal_warranty_price numeric(10, 2) NULL;
COMMENT ON COLUMN inventory.materials.internal_warranty_price IS '对内保修价格';

ALTER TABLE inventory.materials ADD internal_warranty_currency_id int4 NULL;
COMMENT ON COLUMN inventory.materials.internal_warranty_currency_id IS '对内保修价格幣別';

ALTER TABLE inventory.materials ADD external_warranty_price numeric(10, 2) NULL;
COMMENT ON COLUMN inventory.materials.external_warranty_price IS '对外保修价格';

ALTER TABLE inventory.materials ADD external_warranty_currency_id int4 NULL;
COMMENT ON COLUMN inventory.materials.external_warranty_currency_id IS '对外保修价格幣別';

ALTER TABLE inventory.materials ADD warranty_period_type_id int8 NULL;
COMMENT ON COLUMN inventory.materials.warranty_period_type_id IS '保固類型週期';

ALTER TABLE inventory.materials ADD CONSTRAINT materials_eye_warranty_period_type_id_fkey FOREIGN KEY (warranty_period_type_id) REFERENCES maintenance.eye_warranty_period_types(id);
ALTER TABLE inventory.materials ADD CONSTRAINT materials_internal_warranty_currency_id_fkey FOREIGN KEY (internal_warranty_currency_id) REFERENCES currencies(id);
ALTER TABLE inventory.materials ADD CONSTRAINT materials_external_warranty_currency_id_fkey FOREIGN KEY (external_warranty_currency_id) REFERENCES currencies(id);