CREATE TABLE sales_rep_workstation.bids_equipment (
	bid_id int8 NOT NULL,
	bid_equipments_id int8 NOT NULL,
	CONSTRAINT bids_equipment_pkey PRIMARY KEY (bid_id, bid_equipments_id)
);


ALTER TABLE sales_rep_workstation.bids_equipment ADD CONSTRAINT bids_equipment_bid_id_foreign FOREIGN KEY (bid_id) REFERENCES sales_rep_workstation.bids(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.bids_equipment ADD CONSTRAINT bids_equipment_bid_equipments_id_foreign FOREIGN KEY (bid_equipments_id) REFERENCES sales_rep_workstation.authorization_products(id) ON UPDATE CASCADE;
