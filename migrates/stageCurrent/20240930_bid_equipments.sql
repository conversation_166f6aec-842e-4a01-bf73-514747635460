CREATE TABLE sales_rep_workstation.bid_equipments (
	id bigserial NOT NULL,
	code varchar(30) NULL, 
    "name" varchar(300) NOT NULL,
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
    deleted bool DEFAULT false NOT NULL,
    CONSTRAINT bid_equipments_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.bid_equipments ADD CONSTRAINT bid_equipments_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.bid_equipments ADD CONSTRAINT bid_equipments_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
