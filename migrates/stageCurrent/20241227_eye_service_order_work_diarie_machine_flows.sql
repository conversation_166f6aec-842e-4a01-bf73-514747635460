CREATE TABLE maintenance.eye_service_order_work_diary_machine_flows (
    id bigserial NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"name" varchar(100) NOT NULL,
	region_id int8 NOT NULL,
    CONSTRAINT eye_service_order_work_diary_machine_flows_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE maintenance.eye_service_order_work_diary_machine_flows IS '展示机流向';

INSERT INTO maintenance.eye_service_order_work_diary_machine_flows (name,region_id) 
VALUES ('回库',2),
	   ('继续展示下一家',2);

ALTER TABLE maintenance.eye_service_order_work_diaries ADD eye_service_order_work_diary_machine_flow_id int4 NULL;
COMMENT ON COLUMN maintenance.eye_service_order_work_diaries.eye_service_order_work_diary_machine_flow_id IS '展示机流向';
ALTER TABLE maintenance.eye_service_order_work_diaries
    ADD CONSTRAINT eye_service_order_work_diary_machine_flow_id_fkey
    FOREIGN KEY (eye_service_order_work_diary_machine_flow_id)
    REFERENCES maintenance.eye_service_order_work_diary_machine_flows (id)
    ON UPDATE CASCADE;