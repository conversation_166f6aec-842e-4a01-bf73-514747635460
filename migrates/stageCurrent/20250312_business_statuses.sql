ALTER TABLE sales_rep_workstation.business_statuses DROP CONSTRAINT business_statuses_type_check;
ALTER TABLE sales_rep_workstation.business_statuses ADD CONSTRAINT business_statuses_type_check CHECK (type = ANY (ARRAY['Created', 'InProgress', 'ClosedInWinning', 'ClosedInLosing', 'ClosedInOthers', 'ClosedInCancel']));
INSERT INTO sales_rep_workstation.business_statuses ("name", "view_order", "sales_team_group_id", "type", "buying_opportunity") 
VALUES ('取消', 7, 4, 'ClosedInCancel', '0%');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", "description", "enabled") 
VALUES ('sales_rep_workstation', 'business_statuses', 'type', 'EnumBusinessStatusType', 'ClosedInCancel', 'ClosedInCancel', '取消', 't');
