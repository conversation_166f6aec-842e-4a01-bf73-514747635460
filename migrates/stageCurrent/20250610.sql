ALTER TABLE maintenance.eye_service_orders ADD satisfaction_contact_person varchar(50) NULL;
ALTER TABLE maintenance.eye_service_orders ADD satisfaction_contact_phone varchar(50) NULL;
ALTER TABLE maintenance.eye_service_orders ADD satisfaction_level int4 NULL;
ALTER TABLE maintenance.eye_service_orders ADD satisfaction_feedback text NULL;
ALTER TABLE maintenance.eye_service_orders ADD rating_service_engineer int4 NULL;
ALTER TABLE maintenance.eye_service_orders ADD equipment_rating int4 NULL;

COMMENT ON COLUMN maintenance.eye_service_orders.satisfaction_contact_person IS '满意度调查联络人';
COMMENT ON COLUMN maintenance.eye_service_orders.satisfaction_contact_phone IS '满意度调查联络电话';
COMMENT ON COLUMN maintenance.eye_service_orders.satisfaction_level IS '满意度';
COMMENT ON COLUMN maintenance.eye_service_orders.satisfaction_feedback IS '满意度反馈';
COMMENT ON COLUMN maintenance.eye_service_orders.rating_service_engineer IS '对本次服务工程师是否满意';
COMMENT ON COLUMN maintenance.eye_service_orders.equipment_rating IS '是否会考虑将该设备推荐给同行';

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '库龄查询', 'inventoryAge.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '库存'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '库龄查询', 'inventoryAge.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '库存'), 2);

ALTER TABLE sales_rep_workstation.tenders ADD message_header text NULL;
COMMENT ON COLUMN sales_rep_workstation.tenders.message_header IS '信息标题';

ALTER TABLE sales_rep_workstation.performance_forecasts ALTER COLUMN amount_early_month TYPE numeric(12,2);
ALTER TABLE sales_rep_workstation.performance_forecasts ALTER COLUMN amount_mid_month TYPE numeric(12,2);
ALTER TABLE sales_rep_workstation.performance_forecasts ALTER COLUMN amount_late_month TYPE numeric(12,2);