CREATE TABLE sales_rep_workstation.authorization_products (
	id bigserial NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	"name" varchar(100) NULL,
	code varchar(100) NULL,
	registrant varchar(100) NULL,
	agent varchar(100) NULL,
	model varchar(100) NULL,
	CONSTRAINT authorization_products_pkey PRIMARY KEY (id)
);

CREATE TABLE sales_rep_workstation.distributor_authorization_attachments (
	id bigserial NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	"name" varchar(300) NOT NULL,
	"extension" varchar(30) NOT NULL,
	s3_key varchar(300) NOT NULL,
	memo text NULL,
	distributor_authorization_id int8 NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	CONSTRAINT distributor_authorization_attachments_pkey PRIMARY KEY (id)
);

CREATE TABLE sales_rep_workstation.distributor_authorizations (
	id bigserial NOT NULL,
	created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	deleted bool DEFAULT false NOT NULL,
	company_id int8 NOT NULL,
	customer_id int8 NOT NULL,
	project_name varchar(100) NULL,
	project_code varchar(100) NULL,
	plan_code varchar(100) NULL,
	memo text NULL,
	start_date date NULL,
	end_date date NULL,
	bpm_instance_id varchar(128) NULL,
	approval_status int4 NOT NULL,
	CONSTRAINT distributor_authorizations_pkey PRIMARY KEY (id)
);

CREATE TABLE sales_rep_workstation.distributor_authorization_products (
	id bigserial NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	distributor_authorization_id int8 NOT NULL,
	authorization_product_id  int8 NOT NULL,
	CONSTRAINT distributor_authorization_products_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.distributor_authorization_attachments ADD CONSTRAINT distributor_authorization_attachments_da_id_fkey FOREIGN KEY (distributor_authorization_id) REFERENCES distributor_authorizations(id);

ALTER TABLE sales_rep_workstation.distributor_authorizations ADD CONSTRAINT distributor_authorizations_company_id_fkey FOREIGN KEY (company_id) REFERENCES financial_companies(id);
ALTER TABLE sales_rep_workstation.distributor_authorizations ADD CONSTRAINT distributor_authorizations_created_user_id_fkey FOREIGN KEY (created_user_id) REFERENCES users(id);
ALTER TABLE sales_rep_workstation.distributor_authorizations ADD CONSTRAINT distributor_authorizations_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES customers(id);
ALTER TABLE sales_rep_workstation.distributor_authorizations ADD CONSTRAINT distributor_authorizations_updated_user_id_fkey FOREIGN KEY (updated_user_id) REFERENCES users(id);

ALTER TABLE sales_rep_workstation.distributor_authorization_products ADD CONSTRAINT distributor_authorization_attachments_da_id_fkey FOREIGN KEY (distributor_authorization_id) REFERENCES distributor_authorizations(id);
ALTER TABLE sales_rep_workstation.distributor_authorization_products ADD CONSTRAINT distributor_authorization_attachments_ap_id_fkey FOREIGN KEY (authorization_product_id) REFERENCES authorization_products(id);

COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.created_at IS '建立日期';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.updated_at IS '更新日期';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.created_user_id IS '建立人';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.updated_user_id IS '修改人';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.company_id IS '公司';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.customer_id IS '客戶';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.project_name IS '項目名稱';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.project_code IS '項目編號';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.plan_code IS '計劃編號';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.memo IS '備註';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.start_date IS '授權開始日期';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.end_date IS '授權結束日期';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.bpm_instance_id IS 'BPM';
COMMENT ON COLUMN sales_rep_workstation.distributor_authorizations.approval_status IS '授權狀態(BPM）';

-- Auto-generated SQL script #202409302335
INSERT INTO generator.enums ("schema","table","column","name","key",value,description,enabled)
	VALUES ('sales_rep_workstation','distributor_authorizations','approval_status','EnumDistributorAuthorizationApprovalStatus','Rejected','-1','拒絕',true);
INSERT INTO generator.enums ("schema","table","column","name","key",value,description,enabled)
	VALUES ('sales_rep_workstation','distributor_authorizations','approval_status','EnumDistributorAuthorizationApprovalStatus','Canceled','0','取消',true);
INSERT INTO generator.enums ("schema","table","column","name","key",value,description,enabled)
	VALUES ('sales_rep_workstation','distributor_authorizations','approval_status','EnumDistributorAuthorizationApprovalStatus','Processing','1','核准中',true);
INSERT INTO generator.enums ("schema","table","column","name","key",value,description,enabled)
	VALUES ('sales_rep_workstation','distributor_authorizations','approval_status','EnumDistributorAuthorizationApprovalStatus','Approved','2','已核准',true);

CREATE INDEX distributor_authorizations_approval_status_idx ON sales_rep_workstation.distributor_authorizations USING btree (approval_status);
CREATE INDEX distributor_authorizations_customer_id_idx ON sales_rep_workstation.distributor_authorizations USING btree (customer_id);
CREATE INDEX distributor_authorizations_deleted_idx ON sales_rep_workstation.distributor_authorizations USING btree (deleted);
CREATE INDEX distributor_authorizations_end_date_idx ON sales_rep_workstation.distributor_authorizations USING btree (end_date);
CREATE INDEX distributor_authorizations_start_date_idx ON sales_rep_workstation.distributor_authorizations USING btree (start_date);

CREATE INDEX distributor_authorization_products_ap_id_idx ON sales_rep_workstation.distributor_authorization_products USING btree (authorization_product_id);
CREATE INDEX distributor_authorization_products_da_id_idx ON sales_rep_workstation.distributor_authorization_products USING btree (distributor_authorization_id);

INSERT INTO permission.permission_groups
("name", code, description)
VALUES('經銷商授權', 'distributor_authorization', NULL);

INSERT INTO permission.permissions
("name", code, description, deleted, group_id, application_id)
VALUES('經銷商授權閱覽', 'distributor_authorization.read', NULL, false, 23, 1);
INSERT INTO permission.permissions
("name", code, description, deleted, group_id, application_id)
VALUES('經銷商授權新增', 'distributor_authorization.create', NULL, false, 23, 1);
INSERT INTO permission.permissions
("name", code, description, deleted, group_id, application_id)
VALUES('經銷商授權修改', 'distributor_authorization.update', NULL, false, 23, 1);
INSERT INTO permission.permissions
("name", code, description, deleted, group_id, application_id)
VALUES('經銷商授權閱覽', 'distributor_authorization.read', NULL, false, 23, 2);
INSERT INTO permission.permissions
("name", code, description, deleted, group_id, application_id)
VALUES('經銷商授權新增', 'distributor_authorization.create', NULL, false, 23, 2);
INSERT INTO permission.permissions
("name", code, description, deleted, group_id, application_id)
VALUES('經銷商授權修改', 'distributor_authorization.update', NULL, false, 23, 2);

INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '内窥镜摄像系统', 'E4');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科内窥镜', 'OME 200等');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '非接触式眼压计', 'TonoVue');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '免散瞳眼底照相机', 'NFC-700');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '同视机Synoptophore', '2001');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '手持式压平眼压计', 'Perkins MK 3');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '扫描激光传输装置', '70059.70069');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '一次性使用眼内激光光纤探针EndoProbe', '10547、12934、14030、14120、14400、10562、13920、14390');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科半导体激光光凝仪', 'IQ577');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '半导体激光治疗仪', 'OcuLight SLX');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '激光光凝仪', 'OcuLight TX');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科半导体激光治疗仪Laser System', 'Cyclo G6');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼电生理仪', 'UTAS');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '视觉电生理诊断装置', 'RETeval');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '倍频Nd：YVO激光光凝仪', 'NAVILAS');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '角膜地形图仪', 'Keratron Scout');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼用钳 NEW PHACO TIPS WRENCH', '113402');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '引流袋 COLLECTION BAG', '116002');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼用注吸器 I/A CANNULA', '113301、113307、113308、113309、114216、114217、114218、114219、114222、114223、114101');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '吸引管 I/A TUBING SET WITH ACS3', '117001、117004');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '超声乳化仪', 'Pulsar');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '超乳玻切一体机', 'Pulsar2');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科Q开关Nd:YAG激光治疗机     Medical therapeutic laser device', 'OptoYag&SLT M，OptoYag M');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '光学相干断层扫描仪', 'iVue100');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '光学相干断层扫描仪', 'RTVue XR');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '角膜移植架', '510.410.001');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '飞秒激光治疗仪一次性手术包', '一批');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科飞秒激光治疗仪', 'FEMTO LDV Z4，FEMTO LDV Z6');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '飞秒激光治疗仪一次性手术包', '一批');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科飞秒激光治疗仪', 'FEMTO LDV Z8');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), 'A超角膜测厚扫描仪', 'PacScan 300AP+');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科A/B型超声诊断仪Ophthalmic Ultrasound Equipment', 'Vupad');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '视功能检查仪 Vision Tester', 'FUNCTIONAL VISION ANALYZER(FVA)');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '视觉功能分析仪', 'iTrace');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '角膜内皮显微镜', 'CellChek C');
INSERT INTO authorization_products (deleted, created_at, updated_at, "name", code) VALUES(false, now(), now(), '眼科Nd：YVO倍频激光治疗仪    Green Laser Photocoagulator 532 nm', 'MERILAS 532a');