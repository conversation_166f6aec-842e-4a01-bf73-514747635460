CREATE TABLE public.eye_quotation_order_commission_types (
    id bigserial NOT NULL,
		created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		region_id int4 NOT NULL,
		company_id int8 NOT NULL,
   CONSTRAINT eye_quotation_order_commission_types_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.eye_quotation_order_commission_types IS '研究费类别';

INSERT INTO public.eye_quotation_order_commission_types (name,region_id,company_id) 
VALUES ('运营赋能',2,32),
	   ('专项',2,32);

ALTER TABLE public.eye_quotation_orders ADD eye_quotation_order_commission_type_id int4 NULL;
COMMENT ON COLUMN public.eye_quotation_orders.eye_quotation_order_commission_type_id IS '研究费类别';
ALTER TABLE public.eye_quotation_orders
    ADD CONSTRAINT eye_quotation_order_commission_type_id_fkey
    FOREIGN KEY (eye_quotation_order_commission_type_id)
    REFERENCES public.eye_quotation_order_commission_types (id)
    ON UPDATE CASCADE;