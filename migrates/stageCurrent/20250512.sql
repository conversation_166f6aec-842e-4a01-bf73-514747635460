ALTER TABLE customer_ibs RENAME COLUMN ibs_code TO code;

INSERT INTO public.codes (region_id, company_id, type, prefix, current_code, max_code) 
VALUES (2, 32, 'customerIBS', 'CN-IBS', 0, 999999);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '业绩看板查询', 'performanceDashboard.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '業務'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '业绩看板查询', 'performanceDashboard.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '業務'), 2);