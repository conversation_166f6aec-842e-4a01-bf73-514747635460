CREATE TABLE sales_rep_workstation.customer_ibs_nav (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    no varchar(50) NULL,
		serial_no varchar(50) NULL,
		Service_item_group_code varchar(50) NULL,
		description text null,
		description2 text null,
		status int4 null,
		priority int4 null,
		customer_no varchar(50) NULL,
		item_no varchar(50) NULL,
		unit_of_measure_code varchar(50) NULL,
		location_of_service_item varchar(100) NULL,
		sales_unit_price numeric(18,10) NULL,
		sales_unit_cost numeric(18,10) NULL,
		warranty_starting_date_labor date null,
		warranty_ending_date_labor date null,
		warranty_starting_date_parts date null,
		warranty_ending_date_parts date null,
		warranty_parts int4 null,
		warranty_labor int4 null,
		response_time_hours int4 null,
		installation_date date null,
		sales_date date null,
		last_service_date date null,
		default_contract_value int4 null,
		default_contract_discount int4 null,
		vendor_no varchar(50) NULL,
		vendor_item_no varchar(50) NULL,
		no_series varchar(50) NULL,
		vendor_item_name varchar(100) NULL,
		default_contract_cost int4 null,
		search_description varchar(100) NULL,
		sales_serv_shpt_document_no varchar(50) NULL,
		sales_serv_shpt_line_no varchar(50) NULL,
		shipment_type int4 null,
		clinico_sn varchar(50) NULL,
		virtual int4 null,
    CONSTRAINT customer_ibs_nav_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.customer_ibs_nav IS '客户IBS信息nav数据';


CREATE TABLE sales_rep_workstation.customer_ibs (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
	created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
	is_active bool NOT NULL DEFAULT true,
    created_user_id int8 NULL,
    updated_user_id int8 NULL,
	ibs_code varchar(30) NULL,
	nav_customer_code varchar(30) NULL,
	customer_id int8 NULL,
	material_code varchar(30) NULL,
	material_id int8 NULL,
	sn varchar(50) NULL,
	installation_date date NULL,
	sales_date date NULL,
	last_service_date date NULL,
    CONSTRAINT customer_ibs_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.customer_ibs IS '客户IBS信息';
ALTER TABLE sales_rep_workstation.customer_ibs
    ADD CONSTRAINT customer_ibs_created_user_id_fkey
    FOREIGN KEY (created_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_ibs
    ADD CONSTRAINT customer_ibs_updated_user_id_fkey
    FOREIGN KEY (updated_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.customer_ibs
    ADD CONSTRAINT customer_ibs_customer_id_fkey
    FOREIGN KEY (customer_id)
    REFERENCES sales_rep_workstation.customers (id)
    ON UPDATE CASCADE;	
ALTER TABLE sales_rep_workstation.customer_ibs
    ADD CONSTRAINT customer_ibs_material_id_fkey
    FOREIGN KEY (material_id)
    REFERENCES inventory.materials (id)
    ON UPDATE CASCADE;	


