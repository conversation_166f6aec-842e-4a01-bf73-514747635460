ALTER TABLE maintenance.eye_service_order_items
ALTER COLUMN udi DROP NOT NULL;

ALTER TABLE maintenance.eye_service_orders
ALTER COLUMN contact_person DROP NOT NULL;

ALTER TABLE maintenance.eye_service_orders
ALTER COLUMN contact_phone DROP NOT NULL;

ALTER TABLE maintenance.eye_service_orders
ALTER COLUMN address DROP NOT NULL;

ALTER TABLE maintenance.eye_service_orders ADD installation_customer_id int8 NULL;
ALTER TABLE maintenance.eye_service_orders ADD installation_customer_eye_warranty_contract_id int8 NULL;
ALTER TABLE maintenance.eye_service_orders ADD installation_customer_contact_person varchar(100) NULL;
ALTER TABLE maintenance.eye_service_orders ADD installation_customer_contact_phone varchar(30) NULL;
ALTER TABLE maintenance.eye_service_orders ADD installation_customer_address varchar(300) NULL;
COMMENT ON COLUMN maintenance.eye_service_orders.installation_customer_id IS '装机客户';
COMMENT ON COLUMN maintenance.eye_service_orders.installation_customer_eye_warranty_contract_id IS '装机客户合约编号';
COMMENT ON COLUMN maintenance.eye_service_orders.installation_customer_contact_person IS '装机客户联络人';
COMMENT ON COLUMN maintenance.eye_service_orders.installation_customer_contact_phone IS '装机客户电话';
COMMENT ON COLUMN maintenance.eye_service_orders.installation_customer_address IS '装机客户地址';
ALTER TABLE maintenance.eye_service_orders
    ADD CONSTRAINT eye_service_order_installation_customer_id_fkey
    FOREIGN KEY (installation_customer_id)
    REFERENCES sales_rep_workstation.customers (id)
    ON UPDATE CASCADE;
ALTER TABLE maintenance.eye_service_orders
    ADD CONSTRAINT installation_customer_eye_warranty_contract_id_fkey
    FOREIGN KEY (installation_customer_eye_warranty_contract_id)
    REFERENCES maintenance.eye_warranty_contracts (id)
    ON UPDATE CASCADE;