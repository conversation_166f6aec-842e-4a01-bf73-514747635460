ALTER TABLE
    sales_rep_workstation.weekly_work_reports
ADD
    rejected_reason text NULL;

COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.rejected_reason IS '工作週報退回原因';

ALTER TABLE
    sales_rep_workstation.weekly_work_reports DROP CONSTRAINT weekly_work_reports_state_check;

ALTER TABLE
    sales_rep_workstation.weekly_work_reports
ADD
    CONSTRAINT weekly_work_reports_state_check CHECK (
        (
            state = ANY (
                ARRAY ['WaitToRead'::text, 'AlreadyRead'::text,'Rejected'::text, 'Deleted'::text]
            )
        )
    );