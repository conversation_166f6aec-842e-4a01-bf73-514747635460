CREATE TABLE public.market_activity_types (
        id bigserial NOT NULL,
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		view_order int4 NULL,
        CONSTRAINT market_activity_types_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_types IS '市场活动类型';
INSERT INTO public.market_activity_types ("name", "view_order")
VALUES
	('展会', null),   
	('专业论譠', null),
	('宣传推广', null),
	('新品发布会', null),
	('产品促销', null),
	('品牌形象宣传', null),
	('赛事赞助', null);

CREATE TABLE public.market_activity_natures (
        id bigserial NOT NULL,
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		view_order int4 NULL,
        CONSTRAINT market_activity_natures_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_natures IS '市场活动性质';
INSERT INTO public.market_activity_natures ("name", "view_order")
VALUES
	('自辨', null),   
	('第三方', null),
	('协辨', null);


CREATE TABLE public.market_activity_participation_types (
        id bigserial NOT NULL,
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		view_order int4 NULL,
        CONSTRAINT market_activity_participation_types_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_participation_types IS '市场活动参与类型';
INSERT INTO public.market_activity_participation_types ("name", "view_order")
VALUES
	('线下', null),   
	('线上', null);

CREATE TABLE public.market_activity_expert_types (
        id bigserial NOT NULL,
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		view_order int4 NULL,
        CONSTRAINT market_activity_expert_types_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_expert_types IS '市场活动专家类型';
INSERT INTO public.market_activity_expert_types ("name", "view_order")
VALUES
	('主持', null),   
	('讲者', null),
	('特别嘉宾', null);

CREATE TABLE public.market_activity_fee_types (
    id bigserial NOT NULL,
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		view_order int4 NULL,
    CONSTRAINT market_activity_fee_types_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_fee_types IS '市场活动费用类型';
INSERT INTO public.market_activity_fee_types ("name", "view_order")
VALUES
	('KOL 费用', null),   
	('住宿费', null),
	('交通费', null),
	('伙食费', null),
	('场地费', null),
	('演讲费', null),
	('交际费', null),
	('运费', null),
	('其他杂项', null);

CREATE TABLE public.market_activities (
        id bigserial NOT NULL,
		created_at timestamptz NOT NULL DEFAULT NOW(),
        updated_at timestamptz NOT NULL DEFAULT NOW(),
		created_user_id int8 NULL,
		updated_user_id int8 NULL,
		deleted bool DEFAULT false NOT NULL,
		"name" varchar(100) NOT NULL,
		code varchar(100) NOT NULL,
		link varchar(300) NULL,
		date1 date NOT NULL,
		date2 date NOT NULL,
		type_id int8 NOT NULL,
		participation_type_id int8 NOT NULL,
		nature_id int8 NOT NULL,
		memo TEXT NULL,
		region_id int8 NULL,
		province_id int8 NULL,
		city_id int8 NULL,
		district_id int8 NULL,
		address TEXT NULL,
		department_id int8 NULL,
		primary_user_id int8 NULL,
		expense_budget numeric(12,2) NULL,
		budget_code varchar(100) NULL,
		status int4 NOT NULL DEFAULT 1,
		bpm_instance_id varchar(128) NULL,
        CONSTRAINT market_activities_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activities IS '市场活动';
COMMENT ON COLUMN public.market_activities.link IS '活动链接';
COMMENT ON COLUMN public.market_activities.type_id IS '活动类型';
COMMENT ON COLUMN public.market_activities.nature_id IS '活动性质';
COMMENT ON COLUMN public.market_activities.participation_type_id IS '参与类型';
COMMENT ON COLUMN public.market_activities.department_id IS '负责部门';
COMMENT ON COLUMN public.market_activities.primary_user_id IS '主要联络人';
COMMENT ON COLUMN public.market_activities.expense_budget IS '费用预算';
COMMENT ON COLUMN public.market_activities.budget_code IS '预算编号';
COMMENT ON COLUMN public.market_activities.status IS '状态';
COMMENT ON COLUMN public.market_activities.bpm_instance_id IS 'BPM Instance id';

ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_created_user_id_fkey FOREIGN KEY (created_user_id) REFERENCES public.users (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_updated_user_id_fkey FOREIGN KEY (updated_user_id) REFERENCES public.users (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_type_id_fkey FOREIGN KEY (type_id) REFERENCES public.market_activity_types (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_participation_type_id_fkey FOREIGN KEY (participation_type_id) REFERENCES public.market_activity_participation_types (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_nature_id_fkey FOREIGN KEY (nature_id) REFERENCES public.market_activity_natures (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_region_id_fkey FOREIGN KEY (region_id) REFERENCES public.regions (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_province_id_fkey FOREIGN KEY (province_id) REFERENCES public.provinces (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_city_id_fkey FOREIGN KEY (city_id) REFERENCES public.cities (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_district_id_fkey FOREIGN KEY (district_id) REFERENCES public.districts (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.departments (id) ON UPDATE CASCADE;
ALTER TABLE public.market_activities
    ADD CONSTRAINT market_activities_primary_user_id_fkey FOREIGN KEY (primary_user_id) REFERENCES public.users (id) ON UPDATE CASCADE;


INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'market_activities', 'status', 'EnumMarketActivityStatus', 'Rejected', '-1', '拒絕', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'market_activities', 'status', 'EnumMarketActivityStatus', 'Canceled', '0', '取消', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'market_activities', 'status', 'EnumMarketActivityStatus', 'New', '1', '新建立', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'market_activities', 'status', 'EnumMarketActivityStatus', 'Processing', '2', '核准中', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'market_activities', 'status', 'EnumMarketActivityStatus', 'Approved', '3', '已核准', 't');


CREATE TABLE public.market_activity_attach_files (
    id bigserial NOT NULL,
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    created_user_id int8 NULL,
    updated_user_id int8 NULL,
    market_activity_id int8 NOT NULL,
    "name" varchar(300) NOT NULL,
    extension varchar(30) NOT NULL,
    s3_key varchar(300) NOT NULL,
    memo TEXT NULL,
    CONSTRAINT market_activity_attach_files_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_attach_files IS '市场活动附件';

ALTER TABLE public.market_activity_attach_files
    ADD CONSTRAINT market_activity_attach_files_created_user_id_fkey
    FOREIGN KEY (created_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;
ALTER TABLE public.market_activity_attach_files
    ADD CONSTRAINT market_activity_attach_files_updated_user_id_fkey
    FOREIGN KEY (updated_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;
ALTER TABLE public.market_activity_attach_files
    ADD CONSTRAINT market_activity_attach_files_market_activity_id_fkey
    FOREIGN KEY (market_activity_id)
    REFERENCES public.market_activities (id)
    ON UPDATE CASCADE;

CREATE TABLE public.market_activity_fee_items (
    id bigserial NOT NULL,
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    market_activity_id int8 NOT NULL,
	fee_type_id int8 NULL,
    expense numeric(12,2) NULL, 
    memo TEXT NULL,
    CONSTRAINT market_activity_fee_items_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_fee_items IS '市场活动费用';
ALTER TABLE public.market_activity_fee_items
    ADD CONSTRAINT market_activity_fee_items_market_activity_id_fkey
    FOREIGN KEY (market_activity_id)
    REFERENCES public.market_activities (id)
    ON UPDATE CASCADE;
ALTER TABLE public.market_activity_fee_items
    ADD CONSTRAINT market_activity_fee_items_fee_type_id_fkey
    FOREIGN KEY (fee_type_id)
    REFERENCES public.market_activity_fee_types (id)
    ON UPDATE CASCADE;

CREATE TABLE public.market_activity_expert_fee_items (
    id bigserial NOT NULL,
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    market_activity_id int8 NOT NULL,
	expert_type_id int8 NULL,
    "name" varchar(300) NULL,
    expense numeric(12,2) NULL, 
    memo TEXT NULL,
    CONSTRAINT market_activity_expert_fee_items_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.market_activity_expert_fee_items IS '市场活动邀请专家费用';
ALTER TABLE public.market_activity_expert_fee_items
    ADD CONSTRAINT market_activity_expert_fee_items_market_activity_id_fkey
    FOREIGN KEY (market_activity_id)
    REFERENCES public.market_activities (id)
    ON UPDATE CASCADE;
ALTER TABLE public.market_activity_expert_fee_items
    ADD CONSTRAINT market_activity_expert_fee_items_expert_type_id_fkey
    FOREIGN KEY (expert_type_id)
    REFERENCES public.market_activity_expert_types (id)
    ON UPDATE CASCADE;


INSERT INTO public.codes ("region_id", "company_id", "type", "prefix", "current_code", "max_code") 
VALUES (2, 32, 'marketActivity', 'AC', 0, 99999);

INSERT INTO permission.permission_groups ("name", code, description) 
VALUES ('市场活动', 'market_activity', NULL);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES 
	('市场活动阅览', 'market_activity.read', NULL, 'f', 22, 1),
	('市场活动新增', 'market_activity.create', NULL, 'f', 22, 1),
    ('市场活动修改', 'market_activity.update', NULL, 'f', 22, 1),
	('市场活动阅览', 'market_activity.read', NULL, 'f', 22, 2),
	('市场活动新增', 'market_activity.create', NULL, 'f', 22, 2),
	('市场活动修改', 'market_activity.update', NULL, 'f', 22, 2),
    ('市场活动删除', 'market_activity.delete', NULL, 'f', 22, 1),
    ('市场活动导出', 'marketActivity.export', NULL, 'f', 22, 1),
    ('市场活动删除', 'market_activity.delete', NULL, 'f', 22, 2);