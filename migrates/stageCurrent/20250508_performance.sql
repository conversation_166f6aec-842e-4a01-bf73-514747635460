-- U8业绩
CREATE TABLE sales_rep_workstation.performance_u8 (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    u8_id varchar(30) NOT NULL,
    account_set varchar(30) NULL,
    sales_type varchar(30) NULL,
		document_date date null,
		document_code varchar(50) NULL,
		contract_review_number varchar(50) NULL,
		large_category varchar(30) NULL,
		reason_code varchar(30) NULL,
		sell_customer_code varchar(30) NULL,
		sell_customer_name varchar(300) NULL,
		bill_customer_code varchar(30) NULL,
		bill_customer_name varchar(300) NULL,
		dept_code varchar(30) NULL,
		user_code varchar(30) NULL,
		user_name varchar(30) NULL,
		sales_order_no varchar(30) NULL,
		order_date date null,
		brand varchar(30) NULL,
		inventory_code varchar(50) NULL,
		device_type varchar(30) NULL,
		model varchar(300) NULL,
		unit varchar(30) NULL,
		warehouse varchar(30) NULL,
		material_name varchar(100) NULL,
		quantity int4 NULL,
		total_amount numeric(12, 4) NULL,
		sales_role varchar(30) NULL,
		customer_type varchar(30) NULL,
		currency varchar(30) NULL,
    CONSTRAINT performance_u8_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.performance_u8 IS 'U8业绩';

COMMENT ON COLUMN sales_rep_workstation.performance_u8.u8_id IS 'u8 ID';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.account_set IS '账套';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.sales_type IS '单据别';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.document_date IS '过帐日期';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.document_code IS '单据编号';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.contract_review_number IS '合约审查核准号';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.large_category IS '大类';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.reason_code IS '销货原因';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.sell_customer_code IS 'Sell-To 客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.sell_customer_name IS 'Sell-To 客户名称';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.bill_customer_code IS 'Bill-To 客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.bill_customer_name IS 'Bill-To 客户名称';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.dept_code IS '销售部门';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.user_code IS '人员CODE';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.user_name IS '人员姓名';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.sales_order_no IS '销售订单号';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.order_date IS '订单日期';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.brand IS '厂牌';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.inventory_code IS '存货编码';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.device_type IS '设备类型';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.model IS '规格型号';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.unit IS '计量单位';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.warehouse IS '发货仓';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.material_name IS '物料名称';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.quantity IS '数量';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.total_amount IS '本币未税金额';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.sales_role IS '角色';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.customer_type IS '客户类别';
COMMENT ON COLUMN sales_rep_workstation.performance_u8.currency IS '币种';


-- crm业绩
CREATE TABLE sales_rep_workstation.performance_crm (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    u8_id varchar(30) NOT NULL,
    account_set varchar(30) NULL,
    sales_type varchar(30) NULL,
		document_date date null,
		document_code varchar(50) NULL,
		contract_review_number varchar(50) NULL,
		large_category varchar(30) NULL,
		reason_code varchar(30) NULL,
		sell_customer_code varchar(30) NULL,
		sell_customer_name varchar(300) NULL,
		bill_customer_code varchar(30) NULL,
		bill_customer_name varchar(300) NULL,
		dept_code varchar(30) NULL,
		user_code varchar(30) NULL,
		user_name varchar(30) NULL,
		sales_order_no varchar(30) NULL,
		order_date date null,
		brand varchar(30) NULL,
		inventory_code varchar(50) NULL,
		device_type varchar(30) NULL,
		model varchar(300) NULL,
		unit varchar(30) NULL,
		warehouse varchar(30) NULL,
		material_name varchar(100) NULL,
		quantity int4 NULL,
		total_amount numeric(12, 4) NULL,
		sales_role varchar(30) NULL,
		customer_type varchar(30) NULL,
		currency varchar(30) NULL,
    CONSTRAINT performance_crm_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.performance_crm IS 'crm业绩';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.u8_id IS 'u8 ID';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.account_set IS '账套';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.sales_type IS '单据别';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.document_date IS '过帐日期';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.document_code IS '单据编号';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.contract_review_number IS '合约审查核准号';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.large_category IS '大类';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.reason_code IS '销货原因';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.sell_customer_code IS 'Sell-To 客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.sell_customer_name IS 'Sell-To 客户名称';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.bill_customer_code IS 'Bill-To 客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.bill_customer_name IS 'Bill-To 客户名称';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.dept_code IS '销售部门';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.user_code IS '人员CODE';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.user_name IS '人员姓名';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.sales_order_no IS '销售订单号';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.order_date IS '订单日期';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.brand IS '厂牌';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.inventory_code IS '存货编码';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.device_type IS '设备类型';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.model IS '规格型号';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.unit IS '计量单位';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.warehouse IS '发货仓';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.material_name IS '物料名称';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.quantity IS '数量';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.total_amount IS '本币未税金额';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.sales_role IS '角色';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.customer_type IS '客户类别';
COMMENT ON COLUMN sales_rep_workstation.performance_crm.currency IS '币种';



-- 业绩拆分
CREATE TABLE sales_rep_workstation.performance_splits (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    sales_order_no varchar(30) NULL,
    role varchar(30) NULL,
   proportion numeric(10, 2) NULL,
   split_type int2 NOT NULL,
   split_material_code varchar(100) NULL,
    CONSTRAINT performance_splits_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.performance_splits IS '业绩拆分';
COMMENT ON COLUMN sales_rep_workstation.performance_splits.sales_order_no IS '销售订单号';
COMMENT ON COLUMN sales_rep_workstation.performance_splits.role IS '角色';
COMMENT ON COLUMN sales_rep_workstation.performance_splits.proportion IS '比例';
COMMENT ON COLUMN sales_rep_workstation.performance_splits.split_type IS '1:按照比例 2:按照料号拆分（产品线）3:合并开票，修正角色名';
COMMENT ON COLUMN sales_rep_workstation.performance_splits.split_material_code IS '拆分物料编号';



-- 业绩提前结案
CREATE TABLE sales_rep_workstation.performance_early_closures (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
		end_date date NULL,
    sales_order_no varchar(30) NULL,
		contract_review_number varchar(30) NULL,
    CONSTRAINT performance_early_closures_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.performance_early_closures IS '业绩提前结案';
COMMENT ON COLUMN sales_rep_workstation.performance_early_closures.end_date IS '结案日期';
COMMENT ON COLUMN sales_rep_workstation.performance_early_closures.sales_order_no IS '销售订单号';
COMMENT ON COLUMN sales_rep_workstation.performance_early_closures.contract_review_number IS '合约审查号';




-- 业绩结果
CREATE TABLE sales_rep_workstation.performance_results (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    data_level int4 NULL,
    dept_code varchar(50) NULL,
		mtd_target numeric(18,2) NULL,
		mtd_performance numeric(18,2) NULL,
		mtd_rate numeric(18,4) NULL,
		q_target numeric(18,2) NULL,
		q_performance numeric(18,2) NULL,
		q_rate numeric(18,4) NULL,
		ytd_target numeric(18,2) NULL,
		ytd_performance numeric(18,2) NULL,
		ytd_rate numeric(18,4) NULL,
		fy_target numeric(18,2) NULL,
		fy_performance numeric(18,2) NULL,
		fy_rate numeric(18,4) NULL,
		projection_rate numeric(18,4) NULL,
		performance_year  varchar(10) NULL,
		performance_month varchar(10) NULL,
    CONSTRAINT performance_results_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.performance_results IS '业绩结果集';

COMMENT ON COLUMN sales_rep_workstation.performance_results.data_level IS '数据LEVEL';
COMMENT ON COLUMN sales_rep_workstation.performance_results.dept_code IS '部门CODE';
COMMENT ON COLUMN sales_rep_workstation.performance_results.mtd_target IS 'MTD 目标金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.mtd_performance IS 'MTD 业绩金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.mtd_rate IS 'MTD 达成率';
COMMENT ON COLUMN sales_rep_workstation.performance_results.q_target IS 'QTD 目标金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.q_performance IS 'QTD 业绩金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.q_rate IS 'QTD 达成率';
COMMENT ON COLUMN sales_rep_workstation.performance_results.ytd_target IS 'YTD 目标金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.ytd_performance IS 'YTD 业绩金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.ytd_rate IS 'YTD 达成率';
COMMENT ON COLUMN sales_rep_workstation.performance_results.fy_target IS '全年预测目标金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.fy_performance IS '全年业绩金额';
COMMENT ON COLUMN sales_rep_workstation.performance_results.fy_rate IS '全年业绩达成率';
COMMENT ON COLUMN sales_rep_workstation.performance_results.projection_rate IS '预估全年业绩达成率';
COMMENT ON COLUMN sales_rep_workstation.performance_results.performance_year IS '数据汇算年份';
COMMENT ON COLUMN sales_rep_workstation.performance_results.performance_month IS '数据汇算月份';


-- 业绩订单U8
CREATE TABLE sales_rep_workstation.performance_order_u8 (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    order_u8_id varchar(30) NOT NULL,
    account_set varchar(30) NULL,
    sales_type varchar(30) NULL,
		document_date date null,
		document_code varchar(50) NULL,
		contract_review_number varchar(50) NULL,
		large_category varchar(30) NULL,
		reason_code varchar(30) NULL,
		sell_customer_code varchar(30) NULL,
		sell_customer_name varchar(300) NULL,
		bill_customer_code varchar(30) NULL,
		bill_customer_name varchar(300) NULL,
		dept_code varchar(30) NULL,
		user_code varchar(30) NULL,
		user_name varchar(30) NULL,
		sales_order_no varchar(30) NULL,
		order_date date null,
		brand varchar(30) NULL,
		inventory_code varchar(50) NULL,
		device_type varchar(30) NULL,
		model varchar(300) NULL,
		unit varchar(30) NULL,
		warehouse varchar(30) NULL,
		material_name varchar(100) NULL,
		quantity int4 NULL,
		total_amount numeric(12, 4) NULL,
		sales_role varchar(30) NULL,
		customer_type varchar(30) NULL,
		currency varchar(30) NULL,
    CONSTRAINT performance_order_u8_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.performance_order_u8 IS 'U8业绩订单';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.order_u8_id IS 'order_u8 ID';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.account_set IS '账套';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.sales_type IS '单据别';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.document_date IS '过帐日期';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.document_code IS '单据编号';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.contract_review_number IS '合约审查核准号';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.large_category IS '大类';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.reason_code IS '销货原因';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.sell_customer_code IS 'Sell-To 客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.sell_customer_name IS 'Sell-To 客户名称';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.bill_customer_code IS 'Bill-To 客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.bill_customer_name IS 'Bill-To 客户名称';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.dept_code IS '销售部门';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.user_code IS '人员CODE';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.user_name IS '人员姓名';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.sales_order_no IS '销售订单号';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.order_date IS '订单日期';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.brand IS '厂牌';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.inventory_code IS '存货编码';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.device_type IS '设备类型';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.model IS '规格型号';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.unit IS '计量单位';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.warehouse IS '发货仓';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.material_name IS '物料名称';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.quantity IS '数量';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.total_amount IS '本币未税金额';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.sales_role IS '角色';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.customer_type IS '客户类别';
COMMENT ON COLUMN sales_rep_workstation.performance_order_u8.currency IS '币种';



-- 业绩外购
CREATE TABLE sales_rep_workstation.performance_outsourcings (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
		outsourcing_id varchar(50) NULL,
		document_date date null,
		sales_order_no varchar(50) NULL,
		quotation_no varchar(50) NULL,
		contract_review_number varchar(50) NULL,
		business_code varchar(50) NULL,
		dept_code varchar(30) NULL,
		user_code varchar(50) NULL,
		user_name varchar(30) NULL,
		sales_role varchar(30) NULL,
		invoice_code varchar(30) NULL,
		sell_customer_code varchar(30) NULL,
		sell_customer_name varchar(300) NULL,
		bill_customer_code varchar(30) NULL,
		bill_customer_name varchar(300) NULL,
		customer_type varchar(50) NULL,
		material_code varchar(50) NULL,
		material_name varchar(100) NULL,
		unit varchar(30) NULL,
		currency varchar(30) NULL,
		tax_rate int4 NULL,
		unit_price numeric(12, 2) NULL,
		quantity numeric(12, 2) NULL,
		original_currency_amount numeric(12, 2) NULL,
		local_currency_amount numeric(12, 2) NULL,
		sales_type VARCHAR(30) NULL,
    CONSTRAINT performance_outsourcings_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.performance_outsourcings IS '业绩外购订单';

COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.outsourcing_id IS '外购id';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.document_date IS '过账日期';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.sales_order_no IS '订单号';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.quotation_no IS '报价单号';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.contract_review_number IS '合约审查号';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.business_code IS '商机编号';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.dept_code IS '部门';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.user_code IS '业务员编号';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.user_name IS '业务员';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.sales_role IS '业务员角色';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.invoice_code IS '金税发票号码';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.sell_customer_code IS '装机客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.sell_customer_name IS '装机客户';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.bill_customer_code IS '开票客户编码';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.bill_customer_name IS '开票客户';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.customer_type IS '客户类别';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.material_code IS '料号';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.material_name IS '存货名称';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.unit IS '单位';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.currency IS '币种';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.tax_rate IS '税率';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.unit_price IS '单价';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.quantity IS '数量';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.original_currency_amount IS '原币金额';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.local_currency_amount IS '本币金额';
COMMENT ON COLUMN sales_rep_workstation.performance_outsourcings.sales_type IS '单据别';


-- 业务指标预测
CREATE TABLE sales_rep_workstation.sales_targets_forecasts (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    month varchar(10) NOT NULL,
    sales_target_year_id int4 NOT NULL,
    service_group_id int8 NOT NULL,
    percent numeric(18, 10) NULL,
    CONSTRAINT sales_targets_forecasts_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.sales_targets_forecasts IS '业务指标';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.created_at IS '资料新增日期';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.updated_at IS '资料修改日期';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.deleted IS '资料是否删除';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.month IS '月份';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.sales_target_year_id IS '年份';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.service_group_id IS '1:销售 2:维修';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_forecasts.percent IS '目标YTD占比百分比';

-- 业务指标维修
CREATE TABLE sales_rep_workstation.sales_targets_maintenance (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    month varchar(10) NOT NULL,
	service_type_id int4 NOT NULL,
    sales_target_year_id int4 NOT NULL,
    service_group_id int8 NOT NULL,
    amount numeric(18, 2) NULL,
    dept_id int4 NULL,
    cost_center_id int8 NULL,
    CONSTRAINT sales_targets_maintenance_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.sales_targets_maintenance IS '业务指标';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.created_at IS '资料新增日期';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.updated_at IS '资料修改日期';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.deleted IS '资料是否删除';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.month IS '月份';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.service_type_id IS '服务类型ID 1:医院托管 2:飞秒维修 3:常规维修';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.sales_target_year_id IS '年份';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.service_group_id IS '1:销售 2:维修';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.amount IS '目标金额';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.dept_id IS '归属部门ID';
COMMENT ON COLUMN sales_rep_workstation.sales_targets_maintenance.cost_center_id IS '成本中心ID';


-- 错误日志表
CREATE TABLE sales_rep_workstation.function_error_logs (
    function_name varchar(255) NULL,
	error_message TEXT NULL,
    error_context TEXT NULL,
	reference_id int8 NULL,
	created_at timestamptz NULL
);
COMMENT ON TABLE sales_rep_workstation.function_error_logs IS '错误日志表';


ALTER TABLE sales_rep_workstation.sales_targets ADD target_month VARCHAR(10) NOT NULL;
COMMENT ON COLUMN sales_rep_workstation.sales_targets.target_month IS '月份不带月';