CREATE TABLE sales_rep_workstation.tenders (
        id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
		created_at timestamptz NOT NULL DEFAULT NOW(),
        updated_at timestamptz NOT NULL DEFAULT NOW(),
        deleted bool NOT NULL DEFAULT false,
        standard_text_id varchar(30) NULL,
		keyword varchar(50) NULL,
        province_id int8 NULL,
		city_id int8 NULL,
		district_id int8 NULL,
		release_time timestamp NULL,
		project_code varchar(100) NULL,
		bid_customer_id int8 NULL,
        bid_customer_name varchar(255) NULL,
		bid_budget numeric(12,2) NULL,
		bid_contact_person varchar(50) NULL,
		bid_contact_phone varchar(50) NULL,
		win_bid_customer varchar(100) NULL,
		win_bid_budget numeric(12,2) NULL,
		win_bid_contact_person varchar(50) NULL,
		win_bid_contact_phone varchar(50) NULL,
		agency varchar(100) NULL,
		proxy_contact_person varchar(50) NULL,
		proxy_contact_phone varchar(50) NULL,
		information_type varchar(30) NULL,
		bid_method varchar(50) NULL,
		bid_acquisition_time timestamp NULL,
		bid_deadline timestamp NULL,
		bid_start_time timestamp NULL,
		bid_end_time timestamp NULL,
		bid_opening_time timestamp NULL,
		bid_evaluation_expert varchar(100) NULL,
		fund_source varchar(30) NULL,
		is_electronic_bid bool NULL,
		secondary_information_type varchar(30) NULL,
    CONSTRAINT tenders_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.tenders IS '千里马招投标';
COMMENT ON COLUMN sales_rep_workstation.tenders.standard_text_id IS '标文ID';
COMMENT ON COLUMN sales_rep_workstation.tenders.keyword IS '关键词';
COMMENT ON COLUMN sales_rep_workstation.tenders.province_id IS '省份';
COMMENT ON COLUMN sales_rep_workstation.tenders.city_id IS '市';
COMMENT ON COLUMN sales_rep_workstation.tenders.district_id IS '县';
COMMENT ON COLUMN sales_rep_workstation.tenders.release_time IS '发布时间';
COMMENT ON COLUMN sales_rep_workstation.tenders.project_code IS '招标/项目编号';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_customer_id IS '招标单位';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_customer_name IS '招标单位名称';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_budget IS '招标预算';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_contact_person IS '招标单位联系人';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_contact_phone IS '招标单位联系方式';
COMMENT ON COLUMN sales_rep_workstation.tenders.win_bid_customer IS '中标单位';
COMMENT ON COLUMN sales_rep_workstation.tenders.win_bid_budget IS '中标金额';
COMMENT ON COLUMN sales_rep_workstation.tenders.win_bid_contact_person IS '中标单位联系人';
COMMENT ON COLUMN sales_rep_workstation.tenders.win_bid_contact_phone IS '中标单位联系方式';
COMMENT ON COLUMN sales_rep_workstation.tenders.agency IS '代理机构';
COMMENT ON COLUMN sales_rep_workstation.tenders.proxy_contact_person IS '代理联系人';
COMMENT ON COLUMN sales_rep_workstation.tenders.proxy_contact_phone IS '代理联系电话';
COMMENT ON COLUMN sales_rep_workstation.tenders.information_type IS '信息类型';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_method IS '招标方式';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_acquisition_time IS '标书获取时间';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_deadline IS '标书截止时间';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_start_time IS '投标开始时间';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_end_time IS '投标截止时间';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_opening_time IS '开标时间';
COMMENT ON COLUMN sales_rep_workstation.tenders.bid_evaluation_expert IS '评标专家';
COMMENT ON COLUMN sales_rep_workstation.tenders.fund_source IS '资金来源';
COMMENT ON COLUMN sales_rep_workstation.tenders.is_electronic_bid IS '是否电子招标';
COMMENT ON COLUMN sales_rep_workstation.tenders.secondary_information_type IS '二级信息类型';

ALTER TABLE sales_rep_workstation.tenders
    ADD CONSTRAINT tenders_province_id_fkey
    FOREIGN KEY (province_id)
    REFERENCES public.provinces (id)
    ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.tenders
    ADD CONSTRAINT tenders_city_id_fkey
    FOREIGN KEY (city_id)
    REFERENCES public.cities (id)
    ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.tenders
    ADD CONSTRAINT tenders_district_id_fkey
    FOREIGN KEY (district_id)
    REFERENCES public.districts (id)
    ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.tenders
    ADD CONSTRAINT tenders_bid_customer_id_fkey
    FOREIGN KEY (bid_customer_id)
    REFERENCES sales_rep_workstation.customers (id)
    ON UPDATE CASCADE;




CREATE TABLE sales_rep_workstation.tender_items (
        id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
		created_at timestamptz NOT NULL DEFAULT NOW(),
        updated_at timestamptz NOT NULL DEFAULT NOW(),
        deleted bool NOT NULL DEFAULT false,
		tender_id int8 NULL,
        standard_text_id varchar(30) NULL,
		bid_matter_name varchar(100) NULL,
		bid_matter_brand varchar(100) NULL,
		bid_matter_model varchar(100) NULL,
		quantity int8 NULL,
		unit_price numeric(12,2) NULL,
		total_price numeric(12,2) NULL,
		CONSTRAINT tender_items_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.tender_items IS '千里马招投标项目';
COMMENT ON COLUMN sales_rep_workstation.tender_items.tender_id IS '招投标ID';
COMMENT ON COLUMN sales_rep_workstation.tender_items.standard_text_id IS '标文ID';
COMMENT ON COLUMN sales_rep_workstation.tender_items.bid_matter_name IS '标的物名称';
COMMENT ON COLUMN sales_rep_workstation.tender_items.bid_matter_brand IS '标的物品牌';
COMMENT ON COLUMN sales_rep_workstation.tender_items.bid_matter_model IS '标的物型号';
COMMENT ON COLUMN sales_rep_workstation.tender_items.quantity IS '数量';
COMMENT ON COLUMN sales_rep_workstation.tender_items.unit_price IS '单价';
COMMENT ON COLUMN sales_rep_workstation.tender_items.total_price IS '总价';

ALTER TABLE sales_rep_workstation.tender_items
    ADD CONSTRAINT tender_items_tender_id_fkey
    FOREIGN KEY (tender_id)
    REFERENCES sales_rep_workstation.tenders (id)
    ON UPDATE CASCADE;


INSERT INTO permission.permission_groups (name, code, "description") 
VALUES ('招投标_CN', 'tender', '');

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '招投标查询', 'tender.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '招投标_CN'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '招投标新增', 'tender.create', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '招投标_CN'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '招投标更新', 'tender.update', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '招投标_CN'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '招投标删除', 'tender.delete', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '招投标_CN'), 1);