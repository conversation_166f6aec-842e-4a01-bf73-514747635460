CREATE TABLE inventory.materials_to_product_lines (
	material_id int8 NOT NULL,
	product_line_id int8 NOT NULL,
	CONSTRAINT eye_products_to_product_lines_pkey PRIMARY KEY (material_id, product_line_id)
);

ALTER TABLE inventory.materials_to_product_lines ADD CONSTRAINT materials_to_product_lines_material_id_foreign FOREIGN KEY (material_id) REFERENCES inventory.materials(id) ON UPDATE CASCADE;
ALTER TABLE inventory.materials_to_product_lines ADD CONSTRAINT materials_to_product_lines_product_line_id_foreign FOREIGN KEY (product_line_id) REFERENCES sales_rep_workstation.product_lines(id) ON UPDATE CASCADE;

