ALTER TABLE public.eye_fixed_assets ADD rental_status int4 NULL;
COMMENT ON COLUMN public.eye_fixed_assets.rental_status IS '租借状态';

INSERT INTO generator.enums (schema,"table", "column", name, key, value, description, enabled) 
VALUES ('public', 'eye_fixed_assets', 'rental_status', 'EnumEyeFixedAssetRentalStatus', 'Waiting', '1', '待租借', 't'),
       ('public', 'eye_fixed_assets', 'rental_status', 'EnumEyeFixedAssetRentalStatus', 'Displayed', '2', '展示中', 't'),
       ('public', 'eye_fixed_assets', 'rental_status', 'EnumEyeFixedAssetRentalStatus', 'Transit', '3', '运输中', 't'),
       ('public', 'eye_fixed_assets', 'rental_status', 'EnumEyeFixedAssetRentalStatus', 'Returned', '4', '已归还', 't'),
       ('public', 'eye_fixed_assets', 'rental_status', 'EnumEyeFixedAssetRentalStatus', 'Repair', '5', '维修', 't');

ALTER TABLE maintenance.eye_service_order_work_diary_types ADD code varchar(30) null;
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_types.code IS '编号';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'Installation'
WHERE name = '交货装机';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'Training'
WHERE name = '操作教育';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'DisplayInstallation'
WHERE name = '展示装机';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'DisplayRemoval'
WHERE name = '展示撤机';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'AfterSalesTraining'
WHERE name = '售后培训';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'Meeting'
WHERE name = '科室会';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'GeneralMaintenance'
WHERE name = '一般维修';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'WarrantyMaintenance'
WHERE name = '保固保养';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'ContractMaintenance'
WHERE name = '合约保养';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'Repair'
WHERE name = '叫修 （收费/合约/保固）';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'IncomingInspection'
WHERE name = '进货测试';
UPDATE maintenance.eye_service_order_work_diary_types 
SET code = 'Other'
WHERE name = '特殊/其他';

INSERT INTO generator.enums (schema,"table", "column", name, key, value, description, enabled) 
VALUES ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'Installation', 'Installation', '交货装机', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'Training', 'Training', '操作教育', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'DisplayInstallation', 'DisplayInstallation', '展示装机', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'DisplayRemoval', 'DisplayRemoval', '展示撤机', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'AfterSalesTraining', 'AfterSalesTraining', '售后培训', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'Meeting', 'Meeting', '科室会', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'GeneralMaintenance', 'GeneralMaintenance', '一般维修', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'WarrantyMaintenance', 'WarrantyMaintenance', '保固保养', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'ContractMaintenance', 'ContractMaintenance', '合约保养', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'Repair', 'Repair', '叫修 （收费/合约/保固）', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'IncomingInspection', 'IncomingInspection', '进货测试', 't'),
       ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'Other', 'Other', '特殊/其他', 't');

ALTER TABLE maintenance.eye_service_order_work_diary_machine_flows ADD code varchar(30) null;
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_machine_flows.code IS '编号';
UPDATE maintenance.eye_service_order_work_diary_machine_flows 
SET code = 'Return'
WHERE name = '回库';
UPDATE maintenance.eye_service_order_work_diary_machine_flows 
SET code = 'Next'
WHERE name = '继续展示下一家';

INSERT INTO generator.enums (schema,"table", "column", name, key, value, description, enabled) 
VALUES ('maintenance', 'eye_service_order_work_diary_machine_flows', 'code', 'EnumEyeServiceOrderWorkDiaryMachineFlowCode', 'Return', 'Return', '回库', 't'),
       ('maintenance', 'eye_service_order_work_diary_machine_flows', 'code', 'EnumEyeServiceOrderWorkDiaryMachineFlowCode', 'Next', 'Next', '继续展示下一家', 't');