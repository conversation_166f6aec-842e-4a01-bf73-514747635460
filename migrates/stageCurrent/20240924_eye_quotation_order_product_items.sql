ALTER TABLE public.eye_quotation_order_product_items ADD warranty_months int4 NULL;
COMMENT ON COLUMN public.eye_quotation_order_product_items.warranty_months IS '保固月份';

ALTER TABLE public.eye_quotation_order_product_items ADD warranty_period_type_id int8 NULL;
COMMENT ON COLUMN public.eye_quotation_order_product_items.warranty_period_type_id IS '保固類型週期';

ALTER TABLE public.eye_quotation_order_product_items ADD CONSTRAINT eye_quotation_order_product_items_eye_warranty_period_type_id_fkey FOREIGN KEY (warranty_period_type_id) REFERENCES maintenance.eye_warranty_period_types(id);

