CREATE TABLE sales_rep_workstation.performance_forecast_years (
    id bigserial NOT NULL,
	created_at timestamptz NOT NULL DEFAULT NOW(),
	updated_at timestamptz NOT NULL DEFAULT NOW(),
	deleted bool DEFAULT false NOT NULL,
	year int4 NOT NULL,
    CONSTRAINT performance_forecast_years_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.performance_forecast_years IS '业绩预测年份';

CREATE TABLE sales_rep_workstation.performance_forecasts (
    id bigserial NOT NULL,
		updated_at timestamptz NOT NULL DEFAULT NOW(),
		updated_user_id int8 NULL,
		deleted bool DEFAULT false NOT NULL,
		performance_forecast_year_id int8 NOT NULL,
		month int4 NOT NULL,
		amount_early_month int4 NOT NULL,
		amount_mid_month int4 NOT NULL,
		amount_late_month int4 NOT NULL,
		sales_team_unit_id int8 NOT NULL,
    CONSTRAINT performance_forecasts_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sales_rep_workstation.performance_forecasts IS '业绩预测';
COMMENT ON COLUMN sales_rep_workstation.performance_forecasts.amount_early_month IS '月初金额预测';
COMMENT ON COLUMN sales_rep_workstation.performance_forecasts.amount_mid_month IS '月中金额预测';
COMMENT ON COLUMN sales_rep_workstation.performance_forecasts.amount_late_month IS '月底金额预测';

ALTER TABLE sales_rep_workstation.performance_forecasts
    ADD CONSTRAINT performance_forecasts_updated_user_id_fkey FOREIGN KEY (updated_user_id) REFERENCES public.users (id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.performance_forecasts
    ADD CONSTRAINT performance_forecasts_sales_team_unit_id_fkey FOREIGN KEY (sales_team_unit_id) REFERENCES sales_rep_workstation.sales_team_units (id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.performance_forecasts
    ADD CONSTRAINT performance_forecasts_performance_forecast_year_id_fkey FOREIGN KEY (performance_forecast_year_id) REFERENCES sales_rep_workstation.performance_forecast_years (id) ON UPDATE CASCADE;


INSERT INTO "permission".permissions
("name", code, description, deleted, group_id, application_id)
values
('业绩预测阅览', 'performance_forecast.read', NULL, false, 19, 2),
('业绩预测修改', 'performance_forecast.update', NULL, false, 19, 2),
('业绩预测新增年度', 'performance_forecast.create_year', NULL, false, 19, 2);
