CREATE TABLE eye_product_groups (
	id int8 GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE) NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	region_id int4 NOT NULL,
	company_id int8 NOT NULL,
	"name" varchar(30) NOT NULL,
	view_order int4 NULL,
	CONSTRAINT eye_product_groups_pkey PRIMARY KEY (id)
);

ALTER TABLE public.eye_product_groups ADD CONSTRAINT eye_product_groups_company_id_fkey FOREIGN KEY (company_id) REFERENCES companies(id);
ALTER TABLE public.eye_product_groups ADD CONSTRAINT eye_product_groups_region_id_fkey FOREIGN KEY (region_id) REFERENCES regions(id);

ALTER TABLE public.eye_products ADD eye_product_group_id int8 NULL;
COMMENT ON COLUMN public.eye_products.eye_product_group_id IS '群組';

ALTER TABLE public.eye_products ADD CONSTRAINT eye_products_eye_product_group_id_fkey FOREIGN KEY (eye_product_group_id) REFERENCES eye_product_groups(id);

INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '900 BQ/BP', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '900BI', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '900BM', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Angiovue OCT', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'BX 900', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CCP-9000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CDC-1/CDC-9000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CDR-9000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Cellchek C', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CellchekD/CellchekD PLUS', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CEP-8800/CEP-8800P', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Clear手朮包', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CLM-1', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CNT-1/CNT-1P', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Cornea 手朮包', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CR-2 AF / CR-2 Plus AF', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CRK-8800/CRK-8800P', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CRT-5000/CRT-8800', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CSL-5000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CVS-1', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'CX-1', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'DOMS检查台', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'DRS plus', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'E2/E4', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'EIDON', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'EYESI SLIT LAMP', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'FRASTEMA检查台', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HCP-7000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HDC-9100', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HDR-7000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HDR-9000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HFC-1', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HNT-1', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HRK-7000A', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HRK-9000A', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HS100 OCT', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HS-5000(LED)', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'HS-5000/HS-7000', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'IM910/IM600', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'IQ 577', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'IR-15522 Rev2', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'iScan', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'iTrace basic', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'iTrace network', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'iTrace Prime', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'iTrace Classic', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'iVue', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Keratron Scout', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'LEAF', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'LION', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'LS 900 APS 五系', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'LS 900 Biometry 九系', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'LS 900 Manual Essential', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'LS 900 Myopia', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'MK-3', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'NFC700 （方舟Plus）', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Octopus 900 / 600', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'OCT-S1', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Opto YAG M', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Opto YAG&SLT-M', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Pacscan 300+', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Pulsar', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Pulsar 2 超乳', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Pulsar 2 超乳+VIT', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'RETeval', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'R-Evo Smart CR', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'R-Evo Smart E', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'R-Evo Smart S', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'RH-I', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'RH-kit', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'SL-FVA', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'SLx810', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Solix', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Synoptophore 2001', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'TearCheck', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Tonometer（AT900）', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'TONOVUE', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'TX-20/TX-20P', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Tx532', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'UTAS', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'VT-10A/VT-5B', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'VuPad', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Z4/Z6', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Z8 CATA', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Z8 LASIK', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Z8 双功能（LASIK+CATA）', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, 'Z8 双功能（LASIK+CLEAR）', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '各式眼内光纤', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '美瑞532', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '超乳玻切耗材（万）', NULL);
INSERT INTO eye_product_groups
(created_at, updated_at, region_id, company_id, "name", view_order)
VALUES(now(), now(), 2, 32, '赛乐G6', NULL);