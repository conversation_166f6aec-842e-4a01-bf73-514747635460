INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '招投标查询', 'tender.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '招投标_CN'), 2);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '研究费查询', 'researchFee.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '業務'), 1);
INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '研究费导出', 'researchFee.export', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '業務'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '客户IBS查询', 'customerIbs.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '客戶'), 1);
INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '客户IBS新增', 'customerIbs.create', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '客戶'), 1);
INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '客户IBS更新', 'customerIbs.update', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '客戶'), 1);
INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '客户IBS删除', 'customerIbs.delete', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '客戶'), 1);
