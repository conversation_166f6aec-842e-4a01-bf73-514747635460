ALTER TABLE sales_rep_workstation.sales_targets ADD key varchar(100) NULL;
ALTER TABLE sales_rep_workstation.sales_targets ADD product varchar(100) NULL;
ALTER TABLE sales_rep_workstation.sales_targets ADD material_code varchar(100) NULL;
ALTER TABLE sales_rep_workstation.sales_targets ADD memo TEXT NULL;
ALTER TABLE sales_rep_workstation.sales_targets ADD position varchar(50) NULL;
COMMENT ON COLUMN sales_rep_workstation.sales_targets.product IS '产品';



INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES 
('public', 'eye_fixed_asset_rental_records', 'status', 'EnumEyeFixedAssetsRentalStatus', 'Exception', '5', '异常收货', 't');
