CREATE TABLE public.eye_fixed_asset_rental_record_logs (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    created_user_id int8 NULL,
    deleted bool NOT NULL DEFAULT false,
    eye_service_order_id int8 NULL,
    sn varchar(50) NULL,
    new_sn varchar(50) NULL,
    date1 timestamptz NULL,
    date2 timestamptz NULL,
    new_date1 timestamptz NULL,
    new_date2 timestamptz NULL,
    approval_status int4 NOT NULL DEFAULT 1,
    bpm_instance_id varchar(128) NULL,
    memo TEXT NULL,
    CONSTRAINT eye_fixed_asset_rental_record_logs_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE public.eye_fixed_asset_rental_record_logs IS '服务单租借变更记录';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.created_at IS '资料新增日期';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.updated_at IS '资料修改日期';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.created_user_id IS '创建人';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.deleted IS '是否删除';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.eye_service_order_id IS '服务单';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.sn IS 'SN';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.new_sn IS '新SN';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.date1 IS '租借时间起';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.date2 IS '租借时间迄';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.new_date1 IS '新租借时间起';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.new_date2 IS '新租借时间迄';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.approval_status IS '审批状态';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.bpm_instance_id IS 'BPM Instance id';
COMMENT ON COLUMN public.eye_fixed_asset_rental_record_logs.memo IS '备注';

ALTER TABLE public.eye_fixed_asset_rental_record_logs
    ADD CONSTRAINT eye_fixed_asset_rental_record_logs_created_user_id_fkey
    FOREIGN KEY (created_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;
ALTER TABLE public.eye_fixed_asset_rental_record_logs
    ADD CONSTRAINT eye_fixed_asset_rental_record_logs_eye_service_order_id_fkey
    FOREIGN KEY (eye_service_order_id)
    REFERENCES maintenance.eye_service_orders (id)
    ON UPDATE CASCADE;

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'eye_fixed_asset_rental_record_logs', 'approval_status', 'EnumEyeFixedAssetRentalRecordLogApprovalStatus', 'Rejected', '-1', '拒絕', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'eye_fixed_asset_rental_record_logs', 'approval_status', 'EnumEyeFixedAssetRentalRecordLogApprovalStatus', 'Canceled', '0', '取消', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'eye_fixed_asset_rental_record_logs', 'approval_status', 'EnumEyeFixedAssetRentalRecordLogApprovalStatus', 'Processing', '1', '核准中', 't');
INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('public', 'eye_fixed_asset_rental_record_logs', 'approval_status', 'EnumEyeFixedAssetRentalRecordLogApprovalStatus', 'Approved', '2', '已核准', 't');



