CREATE TABLE public.eye_quotation_order_product_item_warranty_prices (
	id int8 GENERATED ALWAYS AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE) NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	eye_quotation_order_product_item_id int8 NULL,
	internal_warranty_price numeric(10, 2) NULL,
	internal_warranty_currency_id int4 null,
	CONSTRAINT eye_quotation_order_product_item_warranty_prices_pkey PRIMARY KEY (id)
);

ALTER TABLE public.eye_quotation_order_product_item_warranty_prices ADD CONSTRAINT eye_quotation_order_product_item_warranty_prices_c_fkey FOREIGN KEY (internal_warranty_currency_id) REFERENCES currencies(id);
ALTER TABLE public.eye_quotation_order_product_item_warranty_prices ADD CONSTRAINT eye_quotation_order_product_item_warranty_prices_eqopi_fkey FOREIGN KEY (eye_quotation_order_product_item_id) REFERENCES eye_quotation_order_product_items(id);