CREATE TABLE sales_rep_workstation.customers_consumable_prices ( id bigserial NOT NULL, created_at timestamptz DEFAULT now() NOT NULL, updated_at timestamptz DEFAULT now() NOT NULL, created_user_id int8 NULL, updated_user_id int8 NULL, deleted bool DEFAULT false NOT NULL, region_id int4 NOT NULL, customer_id int8 NOT NULL --出貨客戶
, invoicing_customer_id int8 NOT NULL --開票客戶
, material_id int8 NOT NULL --料件
, clean_price numeric(10, 2) NOT null --淨價
, consumable_price_type_id int8 null --類型
, discount_Policy_id int8 null --折讓政策
, remark_1 text null --備註1
, remark_2 text null --備註2
, remark_3 text null --備註3
, effective_start_date DATE not null --有效(起)
, effective_end_date DATE not null --有效(迄)
, CONSTRAINT customers_consumable_prices_pkey PRIMARY KEY (id) );

CREATE TABLE sales_rep_workstation.customers_consumable_discount_policies ( id bigserial NOT NULL, "name" varchar(100) NOT NULL, view_order int4 NULL, deleted bool DEFAULT false NOT NULL, created_at timestamptz(0) DEFAULT now() NOT NULL, updated_at timestamptz(0) DEFAULT now() NOT NULL, code varchar(30) NULL, CONSTRAINT customers_consumable_discount_policies_pkey PRIMARY KEY (id) );

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_contact_material_id_foreign FOREIGN KEY (material_id) REFERENCES inventory.materials(id);

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_customer_id_foreign FOREIGN KEY (customer_id) REFERENCES sales_rep_workstation.customers(id);

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_invoicing_customer_id_foreign FOREIGN KEY (invoicing_customer_id) REFERENCES sales_rep_workstation.customers(id);

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_created_user_id_fkey FOREIGN KEY (created_user_id) REFERENCES public.users(id);

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_updated_user_id_fkey FOREIGN KEY (updated_user_id) REFERENCES public.users(id);

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_region_id_fkey FOREIGN KEY (region_id) REFERENCES public.regions(id);

ALTER TABLE sales_rep_workstation.customers_consumable_prices ADD CONSTRAINT customers_consumable_prices_discount_policy_id_fkey FOREIGN KEY (discount_policy_id) REFERENCES sales_rep_workstation.customers_consumable_discount_policies(id); COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.created_at IS '資料建立時間'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.updated_at IS '資料更新時間'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.customer_id IS '出貨客戶ID'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.invoicing_customer_id IS '開票客戶ID'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.material_id IS '料件ID'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.clean_price IS '淨價'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.consumable_price_type_id IS '消耗品價格類型ID'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.discount_policy_id IS '折讓政策ID'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.remark_1 IS '備註1'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.remark_2 IS '備註2'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.remark_3 IS '備註3'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.effective_start_date IS '有效起始日期'; COMMENT
ON COLUMN sales_rep_workstation.customers_consumable_prices.effective_end_date IS '有效結束日期';
INSERT INTO sales_rep_workstation.customers_consumable_discount_policies ( "name", view_order, code ) VALUES ('一次性250赠50', 1, 'oneTime250Gift50'), ('一次性500赠100', 2, 'oneTime500Gift100'), ('一次性500赠50', 3, 'oneTime500Gift50'), ('净价开票', 4, 'netPriceInvoice'), ('净价开票,单次200个(含)起', 5, 'netPriceInvoiceMin200'), ('净价开票,单次300个(含)起', 6, 'netPriceInvoiceMin300');

INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格新增', 'customers_consumable_prices.create', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格更新', 'customers_consumable_prices.update', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格刪除', 'customers_consumable_prices.delete', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格讀取', 'customers_consumable_prices.read', NULL, false, 1, 1);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格新增', 'customers_consumable_prices.create', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格更新', 'customers_consumable_prices.update', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格刪除', 'customers_consumable_prices.delete', NULL, false, 1, 2);
INSERT INTO "permission".permissions ("name", code, description, deleted, group_id, application_id) VALUES('客戶耗材價格讀取', 'customers_consumable_prices.read', NULL, false, 1, 2);