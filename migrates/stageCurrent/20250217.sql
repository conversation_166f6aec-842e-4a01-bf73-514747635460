INSERT INTO maintenance.eye_service_order_types
("name", view_order, code)
VALUES('售后培训', 11, 'AfterSalesTraining');
INSERT INTO maintenance.eye_service_order_types
("name", view_order, code)
VALUES('远程案件', 12, 'RemoteCase');

ALTER TABLE maintenance.eye_service_order_types ADD deleted bool DEFAULT false NOT NULL;
COMMENT ON COLUMN maintenance.eye_service_order_types.deleted IS '資料刪除';


INSERT INTO maintenance.eye_service_order_work_diary_types
(created_at, updated_at, "name")
VALUES(now(), now(), '售后培训');
INSERT INTO maintenance.eye_service_order_work_diary_types
(created_at, updated_at, "name")
VALUES(now(), now(), '科室会');

ALTER TABLE maintenance.eye_service_order_work_diary_types ADD deleted bool DEFAULT false NOT NULL;
COMMENT ON COLUMN maintenance.eye_service_order_work_diary_types.deleted IS '資料刪除';

CREATE TABLE public.eye_quotation_order_commission_amounts (
	id bigserial NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	commission_amount numeric(12, 2) NOT NULL,
	eye_quotation_order_commission_type_id int4 NULL,
    eye_quotation_order_id int8 NOT NULL,
	CONSTRAINT eye_quotation_order_commission_amounts_pkey PRIMARY KEY (id)
);

ALTER TABLE public.eye_quotation_order_commission_amounts ADD CONSTRAINT eye_quotation_order_commission_amounts_eye_quotation_orders FOREIGN KEY (eye_quotation_order_id) REFERENCES public.eye_quotation_orders(id);
ALTER TABLE public.eye_quotation_order_commission_amounts ADD CONSTRAINT eye_quotation_order_commission_amounts_commission_types_fkey FOREIGN KEY (eye_quotation_order_commission_type_id) REFERENCES public.eye_quotation_order_commission_types(id);