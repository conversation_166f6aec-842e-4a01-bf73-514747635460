ALTER TABLE public.departments ADD cost_center_id int8 NULL;
COMMENT ON COLUMN public.departments.cost_center_id IS '成本中心';

UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E') WHERE company_id = 32 AND code = 'E';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-BJ1') WHERE company_id = 32 AND code = 'E-BJ1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-BJ1') WHERE company_id = 32 AND code = 'E-BJ1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-BJ1') WHERE company_id = 32 AND code = 'E-BJ1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-BJ1') WHERE company_id = 32 AND code = 'E-BJ1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-BJ1') WHERE company_id = 32 AND code = 'E-BJ1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CC') WHERE company_id = 32 AND code = 'E-CC';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CD') WHERE company_id = 32 AND code = 'E-CD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CD') WHERE company_id = 32 AND code = 'E-CD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CD') WHERE company_id = 32 AND code = 'E-CD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CD') WHERE company_id = 32 AND code = 'E-CD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CQ') WHERE company_id = 32 AND code = 'E-CQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CQ') WHERE company_id = 32 AND code = 'E-CQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CQ') WHERE company_id = 32 AND code = 'E-CQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CS') WHERE company_id = 32 AND code = 'E-CS';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CS') WHERE company_id = 32 AND code = 'E-CS';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CS') WHERE company_id = 32 AND code = 'E-CS';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-CS') WHERE company_id = 32 AND code = 'E-CS';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-GZ') WHERE company_id = 32 AND code = 'E-GZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-GZ') WHERE company_id = 32 AND code = 'E-GZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-GZ') WHERE company_id = 32 AND code = 'E-GZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-GZ') WHERE company_id = 32 AND code = 'E-GZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-GZ') WHERE company_id = 32 AND code = 'E-GZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-GZ') WHERE company_id = 32 AND code = 'E-GZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HEB') WHERE company_id = 32 AND code = 'E-HEB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HEB') WHERE company_id = 32 AND code = 'E-HEB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HEB') WHERE company_id = 32 AND code = 'E-HEB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HF') WHERE company_id = 32 AND code = 'E-HF';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HF') WHERE company_id = 32 AND code = 'E-HF';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HF') WHERE company_id = 32 AND code = 'E-HF';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HHHT') WHERE company_id = 32 AND code = 'E-HHHT';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HZ') WHERE company_id = 32 AND code = 'E-HZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HZ') WHERE company_id = 32 AND code = 'E-HZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HZ') WHERE company_id = 32 AND code = 'E-HZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-HZ') WHERE company_id = 32 AND code = 'E-HZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-JN') WHERE company_id = 32 AND code = 'E-JN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-JN') WHERE company_id = 32 AND code = 'E-JN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-JN') WHERE company_id = 32 AND code = 'E-JN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-JN') WHERE company_id = 32 AND code = 'E-JN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-JN') WHERE company_id = 32 AND code = 'E-JN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KAM') WHERE company_id = 32 AND code = 'E-KAM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KAM') WHERE company_id = 32 AND code = 'E-KAM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KM') WHERE company_id = 32 AND code = 'E-KM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KM') WHERE company_id = 32 AND code = 'E-KM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KM') WHERE company_id = 32 AND code = 'E-KM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KM') WHERE company_id = 32 AND code = 'E-KM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-KM') WHERE company_id = 32 AND code = 'E-KM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-LZ') WHERE company_id = 32 AND code = 'E-LZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-LZ') WHERE company_id = 32 AND code = 'E-LZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-LZ') WHERE company_id = 32 AND code = 'E-LZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NC') WHERE company_id = 32 AND code = 'E-NC';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NC') WHERE company_id = 32 AND code = 'E-NC';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NC') WHERE company_id = 32 AND code = 'E-NC';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NC') WHERE company_id = 32 AND code = 'E-NC';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NJ') WHERE company_id = 32 AND code = 'E-NJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NJ') WHERE company_id = 32 AND code = 'E-NJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NJ') WHERE company_id = 32 AND code = 'E-NJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NJ') WHERE company_id = 32 AND code = 'E-NJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NJ') WHERE company_id = 32 AND code = 'E-NJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NN') WHERE company_id = 32 AND code = 'E-NN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NN') WHERE company_id = 32 AND code = 'E-NN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NN') WHERE company_id = 32 AND code = 'E-NN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-NN') WHERE company_id = 32 AND code = 'E-NN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-QD') WHERE company_id = 32 AND code = 'E-QD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-QD') WHERE company_id = 32 AND code = 'E-QD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-QD') WHERE company_id = 32 AND code = 'E-QD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-QD') WHERE company_id = 32 AND code = 'E-QD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-R-HB1') WHERE company_id = 32 AND code = 'E-R-HB1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-R-HB2') WHERE company_id = 32 AND code = 'E-R-HB2';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-R-HN1') WHERE company_id = 32 AND code = 'E-R-HN1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-R-XB') WHERE company_id = 32 AND code = 'E-R-XB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-R-XN') WHERE company_id = 32 AND code = 'E-R-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-S';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HEB') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SY') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CC') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HEB') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HEB') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SY') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SY') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CC') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CC') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CC') WHERE company_id = 32 AND code = 'E-S-DB';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-BJ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-TJ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-TJ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-BJ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-BJ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-BJ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SJZ') WHERE company_id = 32 AND code = 'E-S-JJJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-QD') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-JN') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-JN') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-ZZ') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-ZZ') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-JN') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-ZZ') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-QD') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-QD') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-ZZ') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-ZZ') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-ZZ') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-JN') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-JN') WHERE company_id = 32 AND code = 'E-S-LY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-TY') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HHHT') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-TY') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-XA') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-TY') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HHHT') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-XA') WHERE company_id = 32 AND code = 'E-S-MSJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HF') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NJ') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NJ') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HF') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NJ') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NJ') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NJ') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HF') WHERE company_id = 32 AND code = 'E-S-SW';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WH') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WH') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CS') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WH') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NC') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CS') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WH') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CS') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NC') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CS') WHERE company_id = 32 AND code = 'E-S-XEG';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-LZ') WHERE company_id = 32 AND code = 'E-S-XGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-LZ') WHERE company_id = 32 AND code = 'E-S-XGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WLMQ') WHERE company_id = 32 AND code = 'E-S-XGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-LZ') WHERE company_id = 32 AND code = 'E-S-XGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CD') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CD') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-KM') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CD') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CD') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CQ') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CQ') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CQ') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-CD') WHERE company_id = 32 AND code = 'E-S-XN';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NN') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NN') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-GZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-GZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-GZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-NN') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-GZ') WHERE company_id = 32 AND code = 'E-S-YGQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SH') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WZ') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-SH') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HZ') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HZ') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-WZ') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-XM') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S-HZ') WHERE company_id = 32 AND code = 'E-S-ZHM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-S') WHERE company_id = 32 AND code = 'E-SERVICE';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SH') WHERE company_id = 32 AND code = 'E-SH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SH') WHERE company_id = 32 AND code = 'E-SH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SH') WHERE company_id = 32 AND code = 'E-SH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SJZ') WHERE company_id = 32 AND code = 'E-SJZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SJZ') WHERE company_id = 32 AND code = 'E-SJZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SY') WHERE company_id = 32 AND code = 'E-SY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SY') WHERE company_id = 32 AND code = 'E-SY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SY') WHERE company_id = 32 AND code = 'E-SY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SZ') WHERE company_id = 32 AND code = 'E-SZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-SZ') WHERE company_id = 32 AND code = 'E-SZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-TJ') WHERE company_id = 32 AND code = 'E-TJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-TJ') WHERE company_id = 32 AND code = 'E-TJ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-TY') WHERE company_id = 32 AND code = 'E-TY';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WH') WHERE company_id = 32 AND code = 'E-WH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WH') WHERE company_id = 32 AND code = 'E-WH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WH') WHERE company_id = 32 AND code = 'E-WH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WH') WHERE company_id = 32 AND code = 'E-WH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WH') WHERE company_id = 32 AND code = 'E-WH';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WLMQ') WHERE company_id = 32 AND code = 'E-WLMQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-WLMQ') WHERE company_id = 32 AND code = 'E-WLMQ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-XA') WHERE company_id = 32 AND code = 'E-XA';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-XA') WHERE company_id = 32 AND code = 'E-XA';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-XA') WHERE company_id = 32 AND code = 'E-XA';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-XM') WHERE company_id = 32 AND code = 'E-XM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-XM') WHERE company_id = 32 AND code = 'E-XM';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-ZZ') WHERE company_id = 32 AND code = 'E-ZZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-ZZ') WHERE company_id = 32 AND code = 'E-ZZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-ZZ') WHERE company_id = 32 AND code = 'E-ZZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-ZZ') WHERE company_id = 32 AND code = 'E-ZZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-ZZ') WHERE company_id = 32 AND code = 'E-ZZ';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E0JK') WHERE company_id = 32 AND code = 'E0JK';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E0JK') WHERE company_id = 32 AND code = 'E0JK';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-K-HD') WHERE company_id = 32 AND code = 'E0JK-HD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'E-K-HD') WHERE company_id = 32 AND code = 'E0JK-HD';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-1';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-2';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-2';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-2';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-2';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-2';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-3';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-3';
UPDATE departments SET cost_center_id = (SELECT id FROM cost_centers WHERE company_id = 32 AND code = 'M-20-1') WHERE company_id = 32 AND code = 'M-20-1-3';