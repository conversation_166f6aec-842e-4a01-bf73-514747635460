CREATE TABLE sales_rep_workstation.sales_targets (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    eye_product_id int4 NOT NULL,
    product_type varchar(50) NULL,
    product_name varchar(50) NULL,
    month varchar(10) NOT NULL,
    amount numeric(12,0) NOT NULL,
    qty int4 NOT NULL,
    sales_team_unit_id int4 NOT NULL,
    CONSTRAINT sales_targets_pkey PRIMARY KEY (id)
);


COMMENT ON TABLE sales_rep_workstation.sales_targets IS '业务指标';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.created_at IS '资料新增日期';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.updated_at IS '资料修改日期';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.deleted IS '资料是否删除';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.eye_product_id IS '眼科商品';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.product_type IS '型号';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.product_name IS '商品名称';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.month IS '月份';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.amount IS '金额';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.qty IS '台数';
COMMENT ON COLUMN sales_rep_workstation.sales_targets.sales_team_unit_id IS '业务位置';

ALTER TABLE sales_rep_workstation.sales_targets
    ADD CONSTRAINT sales_targets_eye_product_id_fkey
        FOREIGN KEY (eye_product_id)
        REFERENCES public.eye_products (id)
        ON UPDATE CASCADE;		
ALTER TABLE sales_rep_workstation.sales_targets
    ADD CONSTRAINT sales_targets_sales_team_unit_id_fkey
        FOREIGN KEY (sales_team_unit_id)
        REFERENCES sales_rep_workstation.sales_team_units (id)
        ON UPDATE CASCADE;


INSERT INTO permission.permissions (name, code, deleted, group_id, application_id) 
VALUES 
		('业务指标阅览', 'target.read', 'f', 19, 2),
		('业务指标导入', 'target.import', 'f', 19, 2);