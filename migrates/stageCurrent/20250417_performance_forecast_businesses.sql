CREATE TABLE sales_rep_workstation.performance_forecast_businesses (
	performance_forecast_id int8 NOT NULL,
	business_id int8 NOT NULL,
	CONSTRAINT performance_forecast_businesses_pkey PRIMARY KEY (performance_forecast_id, business_id)
);
COMMENT ON TABLE sales_rep_workstation.performance_forecast_businesses IS '业绩预测关联商机';
ALTER TABLE sales_rep_workstation.performance_forecast_businesses ADD CONSTRAINT performance_forecast_businesses_performance_forecast_id_foreign FOREIGN KEY (performance_forecast_id) REFERENCES sales_rep_workstation.performance_forecasts(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.performance_forecast_businesses ADD CONSTRAINT performance_forecast_businesses_business_id_foreign FOREIGN KEY (business_id) REFERENCES sales_rep_workstation.businesses(id) ON UPDATE CASCADE;


INSERT INTO permission.permissions ( name, code, description, deleted, group_id, application_id) 
VALUES ('业绩预测汇出', 'performance_forecast.export', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '業務' LIMIT 1), 2);