CREATE TABLE sales_rep_workstation.bids (
	id bigserial NOT NULL,
	customer_id int8 NOT NULL,
    code varchar(30) NULL,  -- 授權書編號
    name varchar(30) null, -- 項目名稱
    bid_date date NULL,
    public_link_url varchar(100) NULL,
    content varchar(100) NULL,
    result text DEFAULT 'InProgress'::text NOT NULL,
    notes text,
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
    deleted bool DEFAULT false NOT NULL,
    CONSTRAINT bids_pkey PRIMARY KEY (id),
    CONSTRAINT bids_result_check CHECK ((result = ANY (ARRAY['InProgress'::text, 'Abandoned'::text, 'Pass'::text,'Acceptance'::text,'Canceled'::text])))
);
COMMENT ON COLUMN sales_rep_workstation.bids.code IS '授權書編號';
COMMENT ON COLUMN sales_rep_workstation.bids.name IS '項目名稱';
COMMENT ON COLUMN sales_rep_workstation.bids.bid_date IS '招標時間';
COMMENT ON COLUMN sales_rep_workstation.bids.public_link_url IS '公開資訊連結';
COMMENT ON COLUMN sales_rep_workstation.bids.content IS '項目內容';
COMMENT ON COLUMN sales_rep_workstation.bids.result IS '結果';
COMMENT ON COLUMN sales_rep_workstation.bids.notes IS '備註';


ALTER TABLE sales_rep_workstation.bids ADD CONSTRAINT bids_customer_id_foreign FOREIGN KEY (customer_id) REFERENCES sales_rep_workstation.customers(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.bids ADD CONSTRAINT bids_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.bids ADD CONSTRAINT bids_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
