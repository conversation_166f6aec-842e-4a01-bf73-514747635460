INSERT INTO "permission".permission_groups
("name", code, description)
VALUES('HR培训记录', 'training', NULL);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( 'HR培训记录查询', 'training.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE code = 'training'), 1);

CREATE TABLE sales_rep_workstation.training (
	id bigserial NOT NULL,
	emp_code varchar(255) NULL,
	emp_name varchar(255) NULL,
	training_org varchar(255) NULL,
	course varchar(255) NULL,
	training_type varchar(50) NULL,
	teacher_name varchar(255) NULL,
	product_type varchar(255) NULL,
	is_product_training varchar(255) NULL,
	start_date date NULL,
	end_date date NULL,
	is_certified bool NULL,
	certification_no varchar(255) NULL,
	training_hours numeric(5, 2) NULL,
	training_fee numeric(12) NULL,
	score varchar(255) NULL,
	created_at timestamp DEFAULT now() NULL,
	updated_at timestamp DEFAULT now() NULL,
	CONSTRAINT training_pkey PRIMARY KEY (id)
);

COMMENT ON COLUMN sales_rep_workstation.training.emp_code IS '員工編碼';
COMMENT ON COLUMN sales_rep_workstation.training.emp_name IS '姓名';
COMMENT ON COLUMN sales_rep_workstation.training.training_org IS '訓練機構';
COMMENT ON COLUMN sales_rep_workstation.training.course IS '訓練科目';
COMMENT ON COLUMN sales_rep_workstation.training.training_type IS '內訓或外訓';
COMMENT ON COLUMN sales_rep_workstation.training.teacher_name IS '講師';
COMMENT ON COLUMN sales_rep_workstation.training.product_type IS '產品類別';
COMMENT ON COLUMN sales_rep_workstation.training.is_product_training IS '產品及法規訓練';
COMMENT ON COLUMN sales_rep_workstation.training.start_date IS '開始日期';
COMMENT ON COLUMN sales_rep_workstation.training.end_date IS '結束日期';
COMMENT ON COLUMN sales_rep_workstation.training.is_certified IS '是否有證書';
COMMENT ON COLUMN sales_rep_workstation.training.certification_no IS '證書號碼';
COMMENT ON COLUMN sales_rep_workstation.training.training_hours IS '訓練時數';
COMMENT ON COLUMN sales_rep_workstation.training.training_fee IS '訓練費用';
COMMENT ON COLUMN sales_rep_workstation.training.score IS '成績';
COMMENT ON COLUMN sales_rep_workstation.training.created_at IS '建立時間';
COMMENT ON COLUMN sales_rep_workstation.training.updated_at IS '更新時間';
-- 員工代碼查詢訓練紀錄
CREATE INDEX idx_training_emp_code ON sales_rep_workstation.training (emp_code);
-- 員工姓名模糊查詢
CREATE INDEX idx_training_emp_name ON sales_rep_workstation.training (emp_name);
-- 課程名稱查詢
CREATE INDEX idx_training_course ON sales_rep_workstation.training (course);
-- 訓練類型（內訓 / 外訓）
CREATE INDEX idx_training_type ON sales_rep_workstation.training (training_type);
-- 開始與結束日期查詢範圍（建議複合索引）
CREATE INDEX idx_training_date_range ON sales_rep_workstation.training (start_date, end_date);
-- 是否產品訓練篩選
CREATE INDEX idx_training_is_product_training ON sales_rep_workstation.training (is_product_training);
-- 是否有證書篩選
CREATE INDEX idx_training_is_certified ON sales_rep_workstation.training (is_certified);
