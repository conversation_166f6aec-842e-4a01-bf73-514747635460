CREATE TABLE sales_rep_workstation.training_courses (
	id bigserial NOT NULL,
	code varchar(255) NULL,
	"name" varchar(255) NULL,
	created_at timestamp DEFAULT now() NULL,
	updated_at timestamp DEFAULT now() NULL,
	create_date int4 NULL,
	teacher varchar(50) NULL,
	starting_date int4 NULL,
	ending_date int4 NULL,
	CONSTRAINT training_course_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX training_courses_code_idx ON sales_rep_workstation.training_courses USING btree (code);

CREATE TABLE sales_rep_workstation.training_scores (
	id bigserial NOT NULL,
	code varchar(255) NULL,
	created_at timestamp DEFAULT now() NULL,
	updated_at timestamp DEFAULT now() NULL,
	starting_date varchar(10) NULL, -- 課程開始日期（yyyy-MM-dd）
	user_code varchar(20) NULL,
	present varchar(10) NULL, -- 培訓方式，例如：線上、實體
	exam varchar(10) NULL, -- 是否必須考核，Y = 是，N = 否
	exam_cnt int4 NULL, -- 考評次數
	score_max numeric(10, 2) NULL, -- 最高考試成績
	score_avg numeric(38, 6) NULL, -- 平均考試成績
	datetime_first varchar(41) NULL, -- 第一次考評時間
	datetime_high varchar(20) NULL, -- 最佳成績的考評時間
	datetime_last varchar(41) NULL, -- 最後一次考評時間
	best_days int4 NULL, -- 最佳成績與考題建檔日的天數差
	best_mins int4 NULL, -- 最佳成績考試所花的時間（分鐘）
	pass varchar(1) NULL, -- 是否及格，Y = 是，N = 否
	CONSTRAINT training_score_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN sales_rep_workstation.training_scores.starting_date IS '課程開始日期（yyyy-MM-dd）';
COMMENT ON COLUMN sales_rep_workstation.training_scores.present IS '培訓方式，例如：線上、實體';
COMMENT ON COLUMN sales_rep_workstation.training_scores.exam IS '是否必須考核，Y = 是，N = 否';
COMMENT ON COLUMN sales_rep_workstation.training_scores.exam_cnt IS '考評次數';
COMMENT ON COLUMN sales_rep_workstation.training_scores.score_max IS '最高考試成績';
COMMENT ON COLUMN sales_rep_workstation.training_scores.score_avg IS '平均考試成績';
COMMENT ON COLUMN sales_rep_workstation.training_scores.datetime_first IS '第一次考評時間';
COMMENT ON COLUMN sales_rep_workstation.training_scores.datetime_high IS '最佳成績的考評時間';
COMMENT ON COLUMN sales_rep_workstation.training_scores.datetime_last IS '最後一次考評時間';
COMMENT ON COLUMN sales_rep_workstation.training_scores.best_days IS '最佳成績與考題建檔日的天數差';
COMMENT ON COLUMN sales_rep_workstation.training_scores.best_mins IS '最佳成績考試所花的時間（分鐘）';
COMMENT ON COLUMN sales_rep_workstation.training_scores.pass IS '是否及格，Y = 是，N = 否';

CREATE TABLE sales_rep_workstation.training_course_docs (
	id bigserial NOT NULL,
	code varchar(255) NULL,
	created_at timestamp NULL,
	updated_at timestamp DEFAULT now() NULL,
	starting_date varchar(10) NULL,
	ending_date varchar(10) NULL,
	teacher varchar(50) NULL,
	url varchar(100) NULL,
	"version" varchar(20) NULL,
	training_course_id int8 NULL,
	CONSTRAINT training_course_doc_pkey PRIMARY KEY (id),
	CONSTRAINT training_course_docs_training_courses_fk FOREIGN KEY (training_course_id) REFERENCES sales_rep_workstation.training_courses(id)
);
CREATE INDEX training_course_docs_code_idx ON sales_rep_workstation.training_course_docs USING btree (code);