CREATE TABLE public.eye_fixed_asset_departments (
	eye_fixed_asset_id int8 NOT NULL,
	department_id int8 NOT NULL,
	CONSTRAINT eye_fixed_asset_departments_pkey PRIMARY KEY (eye_fixed_asset_id, department_id)
);
COMMENT ON TABLE public.eye_fixed_asset_departments IS '固定资产区域';
ALTER TABLE public.eye_fixed_asset_departments ADD CONSTRAINT eye_fixed_asset_departments_eye_fixed_asset_id_foreign FOREIGN KEY (eye_fixed_asset_id) REFERENCES public.eye_fixed_assets(id) ON UPDATE CASCADE;
ALTER TABLE public.eye_fixed_asset_departments ADD CONSTRAINT eye_fixed_asset_departments_department_id_foreign FOREIGN KEY (department_id) REFERENCES public.departments(id) ON UPDATE CASCADE;


CREATE TABLE maintenance.eye_service_order_attachments (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    created_user_id int8 NULL,
    updated_user_id int8 NULL,
    eye_service_order_id int8 NOT NULL,
    name varchar(300) NOT NULL,
    extension varchar(30) NOT NULL,
    s3_key varchar(300) NOT NULL,
    memo TEXT NULL,
    CONSTRAINT eye_service_order_attachments_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE maintenance.eye_service_order_attachments IS '服务单展前附件';

ALTER TABLE maintenance.eye_service_order_attachments
    ADD CONSTRAINT eye_service_order_attachments_created_user_id_fkey
    FOREIGN KEY (created_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;

ALTER TABLE maintenance.eye_service_order_attachments
    ADD CONSTRAINT eye_service_order_attachments_updated_user_id_fkey
    FOREIGN KEY (updated_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;

ALTER TABLE maintenance.eye_service_order_attachments
    ADD CONSTRAINT eye_service_order_attachments_eye_service_order_id_fkey
    FOREIGN KEY (eye_service_order_id)
    REFERENCES maintenance.eye_service_orders (id)
    ON UPDATE CASCADE;

ALTER TABLE public.cost_centers ADD department_id int8 NULL;
COMMENT ON COLUMN public.cost_centers.department_id IS '部门';
ALTER TABLE public.cost_centers
    ADD CONSTRAINT cost_centers_department_id_fkey
    FOREIGN KEY (department_id)
    REFERENCES public.departments (id)
    ON UPDATE CASCADE;


UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E' AND is_active = TRUE)
WHERE code = 'E'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E0JK' AND is_active = TRUE)
WHERE code = 'E0JK'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-KAM' AND is_active = TRUE)
WHERE code = 'E-KAM'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-BJ1' AND is_active = TRUE)
WHERE code = 'E-BJ1'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-CC' AND is_active = TRUE)
WHERE code = 'E-CC'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-HEB' AND is_active = TRUE)
WHERE code = 'E-HEB'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-HHHT' AND is_active = TRUE)
WHERE code = 'E-HHHT'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-R-HB1' AND is_active = TRUE)
WHERE code = 'E-R-HB1'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-SJZ' AND is_active = TRUE)
WHERE code = 'E-SJZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-SY' AND is_active = TRUE)
WHERE code = 'E-SY'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-TJ' AND is_active = TRUE)
WHERE code = 'E-TJ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-TY' AND is_active = TRUE)
WHERE code = 'E-TY'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-HF' AND is_active = TRUE)
WHERE code = 'E-HF'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-JN' AND is_active = TRUE)
WHERE code = 'E-JN'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-NC' AND is_active = TRUE)
WHERE code = 'E-NC'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-NJ' AND is_active = TRUE)
WHERE code = 'E-NJ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-QD' AND is_active = TRUE)
WHERE code = 'E-QD'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-R-HB2' AND is_active = TRUE)
WHERE code = 'E-R-HB2'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-R-HD' AND is_active = TRUE)
WHERE code = 'E-R-HD'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-HZ' AND is_active = TRUE)
WHERE code = 'E-HZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-SH' AND is_active = TRUE)
WHERE code = 'E-SH'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-XM' AND is_active = TRUE)
WHERE code = 'E-XM'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-CS' AND is_active = TRUE)
WHERE code = 'E-CS'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-GZ' AND is_active = TRUE)
WHERE code = 'E-GZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-NN' AND is_active = TRUE)
WHERE code = 'E-NN'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-R-HN1' AND is_active = TRUE)
WHERE code = 'E-R-HN1'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-SZ' AND is_active = TRUE)
WHERE code = 'E-SZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-WH' AND is_active = TRUE)
WHERE code = 'E-WH'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-R-XB' AND is_active = TRUE)
WHERE code = 'E-R-XB'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-LZ' AND is_active = TRUE)
WHERE code = 'E-LZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-WLMQ' AND is_active = TRUE)
WHERE code = 'E-WLMQ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-XA' AND is_active = TRUE)
WHERE code = 'E-XA'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-ZZ' AND is_active = TRUE)
WHERE code = 'E-ZZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-CD' AND is_active = TRUE)
WHERE code = 'E-CD'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-CQ' AND is_active = TRUE)
WHERE code = 'E-CQ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-KM' AND is_active = TRUE)
WHERE code = 'E-KM'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-R-XN' AND is_active = TRUE)
WHERE code = 'E-R-XN'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-20-1' AND is_active = TRUE)
WHERE code = 'M-20-1'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-20-1' AND is_active = TRUE)
WHERE code = 'M-20-1-1'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-20-1' AND is_active = TRUE)
WHERE code = 'M-20-1-2'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-20-1' AND is_active = TRUE)
WHERE code = 'M-20-1-3'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-20-1' AND is_active = TRUE)
WHERE code = 'M-20-1-4'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'CEO' AND is_active = TRUE)
WHERE code = 'CEO'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S' AND is_active = TRUE)
WHERE code = 'E-S'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-DB' AND is_active = TRUE)
WHERE code = 'E-S-CC'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-DB' AND is_active = TRUE)
WHERE code = 'E-S-HEB'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-DB' AND is_active = TRUE)
WHERE code = 'E-S-SY'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-JJJ' AND is_active = TRUE)
WHERE code = 'E-S-BJ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-JJJ' AND is_active = TRUE)
WHERE code = 'E-S-SJZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-JJJ' AND is_active = TRUE)
WHERE code = 'E-S-TJ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-LY' AND is_active = TRUE)
WHERE code = 'E-S-JN'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-LY' AND is_active = TRUE)
WHERE code = 'E-S-QD'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-LY' AND is_active = TRUE)
WHERE code = 'E-S-ZZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-MSJ' AND is_active = TRUE)
WHERE code = 'E-S-HHHT'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-MSJ' AND is_active = TRUE)
WHERE code = 'E-S-TY'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-MSJ' AND is_active = TRUE)
WHERE code = 'E-S-XA'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-SW' AND is_active = TRUE)
WHERE code = 'E-S-HF'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-SW' AND is_active = TRUE)
WHERE code = 'E-S-NJ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XEG' AND is_active = TRUE)
WHERE code = 'E-S-CS'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XEG' AND is_active = TRUE)
WHERE code = 'E-S-NC'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XEG' AND is_active = TRUE)
WHERE code = 'E-S-WH'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XGQ' AND is_active = TRUE)
WHERE code = 'E-S-LZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XGQ' AND is_active = TRUE)
WHERE code = 'E-S-WLMQ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XN' AND is_active = TRUE)
WHERE code = 'E-S-CD'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XN' AND is_active = TRUE)
WHERE code = 'E-S-CQ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-XN' AND is_active = TRUE)
WHERE code = 'E-S-KM'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-YGQ' AND is_active = TRUE)
WHERE code = 'E-S-GZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-YGQ' AND is_active = TRUE)
WHERE code = 'E-S-NN'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-YGQ' AND is_active = TRUE)
WHERE code = 'E-S-SZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-ZHM' AND is_active = TRUE)
WHERE code = 'E-S-HZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-ZHM' AND is_active = TRUE)
WHERE code = 'E-S-SH'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-ZHM' AND is_active = TRUE)
WHERE code = 'E-S-WZ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'E-S-ZHM' AND is_active = TRUE)
WHERE code = 'E-S-XM'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-MA' AND is_active = TRUE)
WHERE code = 'PD'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-PM' AND is_active = TRUE)
WHERE code = 'M-10'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-50' AND is_active = TRUE)
WHERE code = 'M-50'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'HR' AND is_active = TRUE)
WHERE code = 'HR'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'M-60' AND is_active = TRUE)
WHERE code = 'M-60'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'A-1' AND is_active = TRUE)
WHERE code = 'A-1'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'A-2' AND is_active = TRUE)
WHERE code = 'A-2'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'IT' AND is_active = TRUE)
WHERE code = 'IT'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'V' AND is_active = TRUE)
WHERE code = 'V'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-B2C' AND is_active = TRUE)
WHERE code = 'B-B2C'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ01' AND is_active = TRUE)
WHERE code = 'B-BJ01'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ02' AND is_active = TRUE)
WHERE code = 'B-BJ02'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ03' AND is_active = TRUE)
WHERE code = 'B-BJ03'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ04' AND is_active = TRUE)
WHERE code = 'B-BJ04'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ05' AND is_active = TRUE)
WHERE code = 'B-BJ05'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ06' AND is_active = TRUE)
WHERE code = 'B-BJ06'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ07' AND is_active = TRUE)
WHERE code = 'B-BJ07'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ08' AND is_active = TRUE)
WHERE code = 'B-BJ08'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ09' AND is_active = TRUE)
WHERE code = 'B-BJ09'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ10' AND is_active = TRUE)
WHERE code = 'B-BJ10'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ11' AND is_active = TRUE)
WHERE code = 'B-BJ11'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ12' AND is_active = TRUE)
WHERE code = 'B-BJ12'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ13' AND is_active = TRUE)
WHERE code = 'B-BJ13'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-BJ14' AND is_active = TRUE)
WHERE code = 'B-BJ14'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-JN01' AND is_active = TRUE)
WHERE code = 'B-JN01'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-JN03' AND is_active = TRUE)
WHERE code = 'B-JN03'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-QD02' AND is_active = TRUE)
WHERE code = 'B-QD02'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-QD04' AND is_active = TRUE)
WHERE code = 'B-QD04'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-QD05' AND is_active = TRUE)
WHERE code = 'B-QD05'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-QD06' AND is_active = TRUE)
WHERE code = 'B-QD06'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-WF02' AND is_active = TRUE)
WHERE code = 'B-WF02'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-YT01' AND is_active = TRUE)
WHERE code = 'B-YT01'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-WH01' AND is_active = TRUE)
WHERE code = 'B-WH01'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-WH03' AND is_active = TRUE)
WHERE code = 'B-WH03'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-WH04' AND is_active = TRUE)
WHERE code = 'B-WH04'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-XG01' AND is_active = TRUE)
WHERE code = 'B-XG01'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-SH01' AND is_active = TRUE)
WHERE code = 'B-SH01'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-SH03' AND is_active = TRUE)
WHERE code = 'B-SH03'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'B-B2B' AND is_active = TRUE)
WHERE code = 'B-B2B'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'KLJ' AND is_active = TRUE)
WHERE code = 'KLJ'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'KLF' AND is_active = TRUE)
WHERE code = 'KLF'
AND deleted = FALSE 
AND is_active = TRUE;
UPDATE public.cost_centers 
SET department_id = (SELECT id FROM public.departments WHERE code = 'WV' AND is_active = TRUE)
WHERE code = 'WV'
AND deleted = FALSE 
AND is_active = TRUE;
