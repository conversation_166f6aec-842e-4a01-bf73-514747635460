ALTER TABLE sales_rep_workstation.sales_team_unit_posts ADD code varchar(30) null;
COMMENT ON COLUMN sales_rep_workstation.sales_team_unit_posts.code IS '编号';

UPDATE sales_rep_workstation.sales_team_unit_posts 
SET code = 'Business'
WHERE name = '业务';
UPDATE sales_rep_workstation.sales_team_unit_posts
SET code = 'Region'
WHERE name = '地区';
UPDATE sales_rep_workstation.sales_team_unit_posts 
SET code = 'KeyAccount'
WHERE name = '大客户';
UPDATE sales_rep_workstation.sales_team_unit_posts 
SET code = 'Area'
WHERE name = '大区';
UPDATE sales_rep_workstation.sales_team_unit_posts 
SET code = 'National'
WHERE name = '全国';