CREATE TABLE public.financial_companies (
  id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1),
  created_at timestamptz(6) NOT NULL DEFAULT now(),
  updated_at timestamptz(6) NOT NULL DEFAULT now(),
  deleted bool DEFAULT false,
  is_active bool DEFAULT true,
  name varchar(100)  NOT NULL,
  code varchar(100)   NOT NULL,
  region_id int4 NOT NULL,
  company_id int8 NOT NULL,
  CONSTRAINT financial_company_pkey PRIMARY KEY (id)
);

INSERT INTO public.financial_companies (name, code, region_id, company_id)
VALUES 
	('同科林', '91310000778928467E', 2, 32),
	('德仪林', '91310000067790879U', 2, 32),
	('科宏昌', '91110105675701542D', 2, 32);

ALTER TABLE public.eye_quotation_orders ADD COLUMN financial_company_id INT8 NULL;

COMMENT ON COLUMN public.eye_quotation_orders.financial_company_id IS '公司识别号';

ALTER TABLE public.eye_quotation_orders
    ADD CONSTRAINT eye_quotation_orders_financial_company_id_fkey
    FOREIGN KEY (financial_company_id)
    REFERENCES public.financial_companies (id)
    ON UPDATE CASCADE;