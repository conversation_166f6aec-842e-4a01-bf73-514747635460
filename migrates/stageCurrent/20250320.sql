ALTER TABLE public.eye_quotation_order_product_items ADD material_inventory_price numeric(10, 2) NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_inventory_price SET STORAGE MAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_inventory_price IS '供应商存货价';

ALTER TABLE public.eye_quotation_order_product_items ADD material_inventory_currency_id int8 NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_inventory_currency_id SET STORAGE PLAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_inventory_currency_id IS '供应商存货价币别';

ALTER TABLE public.eye_quotation_order_product_items ADD material_option_sale_price numeric(10, 2) NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_option_sale_price SET STORAGE MAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_option_sale_price IS '選配價格';

ALTER TABLE public.eye_quotation_order_product_items ADD material_option_sale_currency_id int4 NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_option_sale_currency_id SET STORAGE PLAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_option_sale_currency_id IS '選配價格幣別';

ALTER TABLE public.eye_quotation_order_product_items ADD material_clean_price numeric(10, 2) NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_clean_price SET STORAGE MAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_clean_price IS '淨價';

ALTER TABLE public.eye_quotation_order_product_items ADD material_clean_currency_id int4 NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_clean_currency_id SET STORAGE PLAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_clean_currency_id IS '淨價幣別';

ALTER TABLE public.eye_quotation_orders ADD inventory_price numeric(12, 2) NULL;
ALTER TABLE public.eye_quotation_orders ALTER COLUMN inventory_price SET STORAGE MAIN;
COMMENT ON COLUMN public.eye_quotation_orders.inventory_price IS '供应商存货价';

ALTER TABLE maintenance.eye_service_order_work_diary_items ADD sn varchar(50) NULL;