INSERT INTO maintenance.eye_service_order_types (name, view_order, code, deleted) 
VALUES ('展示跟机', 13, 'DisplayFollowUp', 'f');

INSERT INTO maintenance.eye_service_order_work_diary_types (name, deleted, code) 
VALUES ('展示跟机', 'f', 'DisplayFollowUp');

INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('maintenance', 'eye_service_order_types', 'code', 'EnumEyeServiceOrderTypeCode', 'DisplayFollowUp', 'DisplayFollowUp', '展示跟机', 't');


INSERT INTO generator.enums ("schema", "table", "column", "name", "key", "value", description, enabled) 
VALUES ('maintenance', 'eye_service_order_work_diary_types', 'code', 'EnumEyeServiceOrderWorkDiaryTypeCode', 'DisplayFollowUp', 'DisplayFollowUp', '展示跟机', 't');

INSERT INTO permission.permission_groups (name, code, "description") 
VALUES ('库存', 'inventory', '');

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '库存查询', 'inventory.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '库存'), 1);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '库存查询', 'inventory.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '库存'), 2);

INSERT INTO permission.permissions ("name", "code", "description", "deleted", "group_id", "application_id") 
VALUES ( '库存导出', 'inventory.export', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '库存'), 1);