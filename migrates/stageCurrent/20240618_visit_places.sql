

CREATE TABLE sales_rep_workstation.visit_places (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    region_id int4 NOT NULL,
    company_id int8 NOT NULL,
    name varchar(30) NOT NULL,
    parent_id int4 NULL,
    view_order int4 NULL,
    CONSTRAINT visit_places_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.visit_places IS '拜访场所';
COMMENT ON COLUMN sales_rep_workstation.visit_places.created_at IS '资料新增日期';
COMMENT ON COLUMN sales_rep_workstation.visit_places.updated_at IS '资料修改日期';
COMMENT ON COLUMN sales_rep_workstation.visit_places.deleted IS '资料是否删除';
COMMENT ON COLUMN sales_rep_workstation.visit_places.region_id IS '区域';
COMMENT ON COLUMN sales_rep_workstation.visit_places.company_id IS '公司';
COMMENT ON COLUMN sales_rep_workstation.visit_places.name IS '名称';
COMMENT ON COLUMN sales_rep_workstation.visit_places.parent_id IS '父ID';
COMMENT ON COLUMN sales_rep_workstation.visit_places.view_order IS '检视顺序';

ALTER TABLE sales_rep_workstation.visit_places
    ADD CONSTRAINT visit_places_region_id_fkey FOREIGN KEY (region_id) REFERENCES public.regions (id) ON UPDATE CASCADE;

ALTER TABLE sales_rep_workstation.visit_places
    ADD CONSTRAINT visit_places_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies (id) ON UPDATE CASCADE;

ALTER TABLE sales_rep_workstation.visit_places
    ADD CONSTRAINT visit_places_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES sales_rep_workstation.visit_places (id) ON UPDATE CASCADE;

INSERT INTO sales_rep_workstation.visit_places ( "region_id", "company_id", "name", "view_order" )
VALUES
	( 2, 32, '远程（电话、微信、QQ、线上会议、电子邮件等）', 1 ),   
	( 2, 32, '面对面', 2 );

ALTER TABLE sales_rep_workstation.visits ADD COLUMN visit_place_id INT8 NULL;
COMMENT ON COLUMN sales_rep_workstation.visits.visit_place_id IS '拜访场所';

ALTER TABLE sales_rep_workstation.visits
    ADD CONSTRAINT visits_visit_place_id_fkey
    FOREIGN KEY (visit_place_id)
    REFERENCES sales_rep_workstation.visit_places (id)
    ON UPDATE CASCADE;