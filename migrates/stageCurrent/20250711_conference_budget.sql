CREATE TABLE sales_rep_workstation.conference_budgets (
	id bigserial NOT NULL,
	created_at timestamp DEFAULT now() NOT NULL,
	updated_at timestamp DEFAULT now() NOT NULL,
	deleted bool NOT NULL DEFAULT false,
    year int4 NULL,
    sales_team_unit_id int8 NULL,
    annual_budget numeric(12,2) NULL,
	CONSTRAINT conference_budget_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.conference_budgets
    ADD CONSTRAINT conference_budgets_sales_team_unit_id_fkey
    FOREIGN KEY (sales_team_unit_id)
    REFERENCES sales_rep_workstation.sales_team_units (id)
    ON UPDATE CASCADE;

COMMENT ON TABLE sales_rep_workstation.conference_budgets IS '会议预算';
COMMENT ON COLUMN sales_rep_workstation.conference_budgets.sales_team_unit_id IS '业务位置';
COMMENT ON COLUMN sales_rep_workstation.conference_budgets.annual_budget IS '年度预算';

CREATE TABLE sales_rep_workstation.conference_budget_cost_centers (
	conference_budget_id int8 NOT NULL,
	cost_center_id int8 NOT NULL,
	CONSTRAINT conference_budget_cost_centers_pkey PRIMARY KEY (conference_budget_id, cost_center_id)
);

ALTER TABLE sales_rep_workstation.conference_budget_cost_centers
    ADD CONSTRAINT conference_budget_cost_centers_conference_budget_id_fkey
    FOREIGN KEY (conference_budget_id)
    REFERENCES sales_rep_workstation.conference_budgets (id)
    ON UPDATE CASCADE;

ALTER TABLE sales_rep_workstation.conference_budget_cost_centers
    ADD CONSTRAINT conference_budget_cost_centers_cost_center_id_fkey
    FOREIGN KEY (cost_center_id)
    REFERENCES public.cost_centers (id)
    ON UPDATE CASCADE;

COMMENT ON TABLE sales_rep_workstation.conference_budget_cost_centers IS '成本中心会议预算';

INSERT INTO permission.permissions ( name, code, description, deleted, group_id, application_id) 
VALUES ('会议预算查询', 'conferenceBudget.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '市场活动' LIMIT 1), 1);
INSERT INTO permission.permissions ( name, code, description, deleted, group_id, application_id) 
VALUES ('会议预算查询', 'conferenceBudget.read', NULL, 'f', (SELECT id FROM permission.permission_groups WHERE name = '市场活动' LIMIT 1), 2);