INSERT INTO sales_rep_workstation.visit_property_types (id,name, view_order, deleted,sales_team_group_id, code) 
VALUES (5,'销售拜访类型', NULL, 'f', 4, 'Sales_Visits_Type');

ALTER TABLE sales_rep_workstation.visit_properties ADD COLUMN parent_id INT8 NULL;
COMMENT ON COLUMN sales_rep_workstation.visit_properties.parent_id IS '上层类型';

ALTER TABLE sales_rep_workstation.visit_properties
ADD CONSTRAINT visit_properties_parent_id_fkey 
FOREIGN KEY (parent_id) 
REFERENCES sales_rep_workstation.visit_properties (id) 
ON UPDATE CASCADE;

INSERT INTO sales_rep_workstation.visit_properties (name, view_order, deleted,sales_team_group_id, type_id, parent_id) 
VALUES ('一般拜访：客户维护', NULL, 'f', 4, 5, NULL),
       ('一般拜访：营运相关', NULL, 'f', 4, 5, NULL),
	   ('一般拜访：专业相关', NULL, 'f', 4, 5, NULL),
	   ('商业沟通与谈判', NULL, 'f', 4, 5, NULL),
	   ('会议', NULL, 'f', 4, 5, NULL);

INSERT INTO sales_rep_workstation.visit_properties (name, view_order, deleted,sales_team_group_id, type_id, parent_id) 
VALUES 
('拓展客户、意向收集', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：客户维护')), 
('信息交流、营运资料收集或提供', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：客户维护')),
('售后跟进', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：客户维护')),
('定期回访', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：客户维护')),
('合同、订单处理', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：营运相关')),
('物流、缺货协调', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：营运相关')), 
('应收账款', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：营运相关')),
('设备展示', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '一般拜访：专业相关')),	
('商业沟通与谈判', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '商业沟通与谈判')),	
('外部会议与活动', NULL, 'f', 4, 5, (SELECT id FROM visit_properties WHERE name = '会议'));

UPDATE visit_properties
SET type_id = 5, 
    parent_id = (SELECT id FROM visit_properties WHERE name = '一般拜访：专业相关')
WHERE name = '科室会';

