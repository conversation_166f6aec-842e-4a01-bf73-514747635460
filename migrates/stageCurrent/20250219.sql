CREATE TABLE maintenance.eye_warranty_contract_attach_files (
	id bigserial NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	deleted bool DEFAULT false NULL,
	eye_warranty_contract_id int8 NOT NULL,
	"name" varchar(300) NOT NULL,
	"extension" varchar(30) NOT NULL,
	s3_key varchar(300) NOT NULL,
	memo text NULL,
	CONSTRAINT eye_warranty_contracts_attach_files_pkey PRIMARY KEY (id)
);

ALTER TABLE maintenance.eye_warranty_contract_attach_files ADD CONSTRAINT eye_warranty_contracts_attach_files_created_user_id_fkey FOREIGN KEY (created_user_id) REFERENCES public.users(id);
ALTER TABLE maintenance.eye_warranty_contract_attach_files ADD CONSTRAINT eye_warranty_contracts_attach_files_eye_warranty_contract_id_fk FOREIGN KEY (eye_warranty_contract_id) REFERENCES maintenance.eye_warranty_contracts(id);
ALTER TABLE maintenance.eye_warranty_contract_attach_files ADD CONSTRAINT eye_warranty_contracts_attach_files_updated_user_id_fkey FOREIGN KEY (updated_user_id) REFERENCES public.users(id);

CREATE TABLE maintenance.eye_warranty_contract_item_attach_files (
	id bigserial NOT NULL,
	created_at timestamptz DEFAULT now() NOT NULL,
	updated_at timestamptz DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	deleted bool DEFAULT false NULL,
	eye_warranty_contract_item_id int8 NOT NULL,
	"name" varchar(300) NOT NULL,
	"extension" varchar(30) NOT NULL,
	s3_key varchar(300) NOT NULL,
	memo text NULL,
	CONSTRAINT eye_warranty_contract_item_attach_files_pkey PRIMARY KEY (id)
);

ALTER TABLE maintenance.eye_warranty_contract_item_attach_files ADD CONSTRAINT eye_warranty_contract_item_attach_files_created_user_id_fkey FOREIGN KEY (created_user_id) REFERENCES public.users(id);
ALTER TABLE maintenance.eye_warranty_contract_item_attach_files ADD CONSTRAINT eye_warranty_contract_item_attach_files_warranty_contract_item FOREIGN KEY (eye_warranty_contract_item_id) REFERENCES maintenance.eye_warranty_contract_items(id);
ALTER TABLE maintenance.eye_warranty_contract_item_attach_files ADD CONSTRAINT eye_warranty_contract_item_attach_files_updated_user_id_fkey FOREIGN KEY (updated_user_id) REFERENCES public.users(id);