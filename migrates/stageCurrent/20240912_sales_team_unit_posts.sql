CREATE TABLE sales_rep_workstation.sales_team_unit_posts (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted boolean NOT NULL DEFAULT false,
    name varchar(30) NOT NULL,
    view_order integer NULL,
    CONSTRAINT sales_team_unit_posts_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sales_rep_workstation.sales_team_unit_posts IS '业务位置岗位';

INSERT INTO sales_rep_workstation.sales_team_unit_posts
(created_at, updated_at, "name", view_order)
VALUES
(now(), now(), '业务', NULL),
(now(), now(), '地区', NULL),
(now(), now(), '大客户', NULL),
(now(), now(), '大区', NULL),
(now(), now(), '全国', NULL);


ALTER TABLE sales_rep_workstation.sales_team_units ADD sales_team_unit_post_id int8 NULL;
ALTER TABLE sales_rep_workstation.sales_team_units ADD entry_date date NULL;
ALTER TABLE sales_rep_workstation.sales_team_units ADD report_user_id int8 NULL;
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.sales_team_unit_post_id IS '岗位';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.entry_date IS '入职日期';
COMMENT ON COLUMN sales_rep_workstation.sales_team_units.report_user_id IS '汇报人';
ALTER TABLE sales_rep_workstation.sales_team_units
    ADD CONSTRAINT sales_team_units_sales_team_unit_post_id_fkey
    FOREIGN KEY (sales_team_unit_post_id)
    REFERENCES sales_rep_workstation.sales_team_unit_posts (id)
    ON UPDATE CASCADE;
		
ALTER TABLE sales_rep_workstation.sales_team_units
    ADD CONSTRAINT sales_team_units_report_user_id_fkey
    FOREIGN KEY (report_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;


UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '全国')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1219');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1545');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1482');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1343');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1232');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1248');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1288');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1302');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1331');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1405');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1279');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1323');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1355');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1486');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '全国')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1303');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1367');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大客户')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1395');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大客户')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1452');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1286');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1443');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0425');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1403');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1311');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0180');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1199');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1394');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1446');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1457');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1360');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1435');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1326');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1385');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1349');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0766');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1402');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0617');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1387');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0323');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0700');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1384');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1439');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1434');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0545');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1500');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0436');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0499');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0235');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0874');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0885');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1491');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1501');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0378');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1364');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1375');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1398');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1460');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1546');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1043');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1065');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0421');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0760');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0893');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1388');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1502');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1528');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0458');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0384');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0901');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1269');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1346');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0830');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1117');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1261');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1420');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1441');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1503');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1504');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1524');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1433');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0911');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1391');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1464');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1332');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1468');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1246');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1095');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1241');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1283');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1536');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0493');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1259');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0922');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1270');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1330');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1359');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1508');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1509');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1513');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1005');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1327');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1512');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0483');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0964');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1510');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1511');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1514');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1109');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1180');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1333');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1369');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0372');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1366');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1234');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1453');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1516');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1527');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1438');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1310');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1368');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1393');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1411');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1459');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1462');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1515');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0010');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0664');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0682');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C0983');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1408');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '大区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1170');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1175');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1293');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1363');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1518');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1537');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1378');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1320');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1422');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1490');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1519');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1520');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '地区')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1353');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1370');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1431');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1436');
UPDATE sales_team_units 
SET sales_team_unit_post_id = (SELECT id FROM sales_team_unit_posts WHERE name = '业务')
WHERE user_id = (SELECT id FROM public.users WHERE code = 'C1529');
