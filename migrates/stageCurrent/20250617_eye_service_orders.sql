ALTER TABLE maintenance.eye_service_orders ADD service_period int4 NULL;
ALTER TABLE maintenance.eye_service_orders ALTER COLUMN service_period SET STORAGE PLAIN;

ALTER TABLE maintenance.eye_service_orders ADD is_last_service bool DEFAULT false NOT NULL;
ALTER TABLE maintenance.eye_service_orders ALTER COLUMN is_last_service SET STORAGE PLAIN;

UPDATE maintenance.eye_service_orders AS eso
SET
    service_period = CASE
        WHEN eso.description ~ '.*\m(\d+)-(\d+)\M.*' THEN -- 判斷 description 是否包含 {n}-{n} 模式
            -- 提取第一個數字，即 {n} 中的 n
            SUBSTRING(eso.description FROM '(\d+)-\d+')::INT
        ELSE
            eso.service_period -- 如果不符合模式，則保持原值
    END,
    is_last_service = CASE
        WHEN eso.description ~ '.*\m(\d+)-(\d+)\M.*' AND -- 判斷是否包含 {n}-{n} 模式
             SUBSTRING(eso.description FROM '(\d+)-(\d+)')::INT = SUBSTRING(eso.description FROM '\d+-(\d+)')::INT THEN
            -- 如果兩個數字相同 (例如 10-10)，則設為 true
            TRUE
        ELSE
            FALSE -- 否則設為 false
    END
WHERE
    eso.eye_warranty_contract_id IS NOT NULL
    AND eso.description ~ '.*\m(\d+)-(\d+)\M.*'; -- 僅處理包含 {n}-{n} 模式的紀錄

UPDATE maintenance.eye_service_orders AS eso_main
SET
    service_period = calculated_data.rn,
    description = eso_main.description || ' ' || calculated_data.rn::TEXT || '-' || calculated_data.total_period::TEXT,
    is_last_service = (calculated_data.rn = calculated_data.total_period)
FROM (
    SELECT
        eso.id,
        ewc.code,
        (ewc.warranty_months / ewpt.value) AS total_period,
        ROW_NUMBER() OVER (PARTITION BY ewc.code ORDER BY eso.id) AS rn -- 在每個合約代碼內依 eso.id 排序並編號
    FROM
        maintenance.eye_service_orders AS eso
    JOIN
        eye_warranty_contracts ewc ON ewc.id = eso.eye_warranty_contract_id
    JOIN
        eye_warranty_period_types ewpt ON ewc.warranty_period_type_id = ewpt.id
    WHERE
        eso.eye_warranty_contract_id IS NOT NULL
        AND eso.service_period IS NULL
        AND eso.deleted = FALSE
        AND ewc.code LIKE 'CN-%'
) AS calculated_data
WHERE
    eso_main.id = calculated_data.id;