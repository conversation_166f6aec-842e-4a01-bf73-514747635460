CREATE TABLE public.market_activity_business_products (
    market_activity_id int8 NOT NULL,
	business_product_id int8 NOT NULL,
    CONSTRAINT market_activity_business_products_pkey PRIMARY KEY (market_activity_id, business_product_id)
);
COMMENT ON TABLE public.market_activity_business_products IS '市场活动产品类型';

ALTER TABLE public.market_activity_business_products
    ADD CONSTRAINT market_activity_business_products_market_activity_id_fkey FOREIGN KEY (market_activity_id) REFERENCES public.market_activities (id) ON UPDATE CASCADE;
		
ALTER TABLE public.market_activity_business_products
    ADD CONSTRAINT market_activity_business_products_business_product_id_fkey FOREIGN KEY (business_product_id) REFERENCES sales_rep_workstation.business_products (id) ON UPDATE CASCADE;

UPDATE market_activity_types
SET deleted = TRUE
WHERE name = '品牌形象宣传';
UPDATE market_activity_types
SET deleted = TRUE
WHERE name = '赛事赞助';

UPDATE market_activity_fee_types
SET deleted = TRUE
WHERE name = '演讲费';
UPDATE market_activity_fee_types
SET deleted = TRUE
WHERE name = 'KOL 费用';


