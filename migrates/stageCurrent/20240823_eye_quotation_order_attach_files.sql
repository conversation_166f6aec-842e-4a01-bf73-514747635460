CREATE TABLE public.eye_quotation_order_attach_files (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY (INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE),
    created_at timestamptz NOT NULL DEFAULT NOW(),
    updated_at timestamptz NOT NULL DEFAULT NOW(),
    deleted bool NOT NULL DEFAULT false,
    created_user_id int8 NULL,
    updated_user_id int8 NULL,
    eye_quotation_order_id int8 NOT NULL,
    name varchar(300) NOT NULL,
    extension varchar(30) NOT NULL,
    s3_key varchar(300) NOT NULL,
    memo TEXT NULL,
    CONSTRAINT eye_quotation_order_attach_files_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE public.eye_quotation_order_attach_files IS '报价单附件';

ALTER TABLE public.eye_quotation_order_attach_files
    ADD CONSTRAINT eye_quotation_order_attach_files_created_user_id_fkey
    FOREIGN KEY (created_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;

ALTER TABLE public.eye_quotation_order_attach_files
    ADD CONSTRAINT eye_quotation_order_attach_files_updated_user_id_fkey
    FOREIGN KEY (updated_user_id)
    REFERENCES public.users (id)
    ON UPDATE CASCADE;

ALTER TABLE public.eye_quotation_order_attach_files
    ADD CONSTRAINT eye_quotation_order_attach_files_eye_quotation_order_id_fkey
    FOREIGN KEY (eye_quotation_order_id)
    REFERENCES public.eye_quotation_orders (id)
    ON UPDATE CASCADE;