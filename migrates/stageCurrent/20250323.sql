ALTER TABLE inventory.materials ADD bundle_sale_price numeric(10, 2) NULL;
ALTER TABLE inventory.materials ALTER COLUMN bundle_sale_price SET STORAGE MAIN;
COMMENT ON COLUMN inventory.materials.bundle_sale_price IS '組配價格';

ALTER TABLE inventory.materials ADD bundle_sale_currency_id int4 NULL;
ALTER TABLE inventory.materials ALTER COLUMN bundle_sale_currency_id SET STORAGE PLAIN;
COMMENT ON COLUMN inventory.materials.bundle_sale_currency_id IS '組配價格幣別';

ALTER TABLE inventory.materials ADD CONSTRAINT materials_bundle_sale_currency_id_fkey FOREIGN KEY (bundle_sale_currency_id) REFERENCES currencies(id);

ALTER TABLE public.eye_quotation_order_product_items ADD material_bundle_sale_price numeric(10, 2) NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_bundle_sale_price SET STORAGE MAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_bundle_sale_price IS '組配價格';

ALTER TABLE public.eye_quotation_order_product_items ADD material_bundle_sale_currency_id int4 NULL;
ALTER TABLE public.eye_quotation_order_product_items ALTER COLUMN material_bundle_sale_currency_id SET STORAGE PLAIN;
COMMENT ON COLUMN public.eye_quotation_order_product_items.material_bundle_sale_currency_id IS '組配價格幣別';

update inventory.materials
set bundle_sale_price = option_sale_price, bundle_sale_currency_id = option_sale_currency_id;

UPDATE public.eye_quotation_order_product_items
set material_bundle_sale_price = material_option_sale_price, material_bundle_sale_currency_id = material_option_sale_currency_id;