CREATE TABLE sales_rep_workstation.weekly_work_reports (
	id bigserial NOT NULL,
	sales_team_id int8 NULL,
	sales_team_unit_id int8 NULL,
	sales_user_id int8 NULL,
    fill_state text DEFAULT 'NotYet'::text NULL,
    state text DEFAULT 'WaitToRead'::text null,
    weekly_records_id int8 null,
    notes text,
    this_week_work_description varchar(100) null,
    next_week_work_description varchar(100) null,
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
    deleted bool DEFAULT false NOT NULL,
    CONSTRAINT weekly_work_reports_pkey PRIMARY KEY (id),
   	CONSTRAINT weekly_work_reports_team_id_foreign FOREIGN KEY (sales_team_id) REFERENCES sales_rep_workstation.sales_teams(id) ON UPDATE CASCADE,
	CONSTRAINT weekly_work_reports_sales_team_unit_id_foreign FOREIGN KEY (sales_team_unit_id) REFERENCES sales_rep_workstation.sales_team_units(id) ON UPDATE CASCADE,
	CONSTRAINT weekly_work_reports_sales_user_id_foreign FOREIGN KEY (sales_user_id) REFERENCES public.users(id) ON UPDATE CASCADE,
	CONSTRAINT weekly_work_reports_weekly_records_id_foreign FOREIGN KEY (weekly_records_id) REFERENCES sales_rep_workstation.weekly_records(id) ON UPDATE CASCADE,
	CONSTRAINT weekly_work_reports_state_check CHECK ((state = ANY (ARRAY['WaitToRead'::text, 'AlreadyRead'::text, 'Deleted'::text]))),
	CONSTRAINT weekly_work_reports_fill_state_check CHECK ((fill_state = ANY (ARRAY['NotYet'::text, 'Already'::text])))
);
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.sales_team_id IS '所屬團隊';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.sales_team_unit_id IS '位置';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.sales_user_id IS '負責人';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.weekly_records_id IS '週';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.fill_state IS '填寫狀況';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.state IS '計畫書狀態';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.notes IS '備註';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.this_week_work_description IS '本週工作說明';
COMMENT ON COLUMN sales_rep_workstation.weekly_work_reports.next_week_work_description IS '下週工作說明';


ALTER TABLE sales_rep_workstation.weekly_work_reports ADD CONSTRAINT weekly_work_reports_sales_team_id_foreign FOREIGN KEY (sales_team_id) REFERENCES sales_rep_workstation.sales_teams(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports ADD CONSTRAINT weekly_work_reports_sales_team_unit_id_foreign FOREIGN KEY (sales_team_unit_id) REFERENCES sales_rep_workstation.sales_team_units(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports ADD CONSTRAINT weekly_work_reports_sales_user_id_foreign FOREIGN KEY (sales_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports ADD CONSTRAINT weekly_work_reports_weekly_records_id_foreign FOREIGN KEY (weekly_records_id) REFERENCES sales_rep_workstation.weekly_records(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports ADD CONSTRAINT weekly_work_reports_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports ADD CONSTRAINT weekly_work_reports_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;

CREATE TABLE sales_rep_workstation.weekly_work_report_attachments (
    id bigserial NOT NULL,
    weekly_work_report_id int8 NOT NULL,                    
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"name" varchar(300) NOT NULL,
	s3_key varchar(300) NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	"extension" varchar(30) NOT NULL,
    CONSTRAINT weekly_work_report_attachments_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.weekly_work_report_attachments ADD CONSTRAINT weekly_work_report_attachments_weekly_work_report_id_foreign FOREIGN KEY (weekly_work_report_id) REFERENCES sales_rep_workstation.weekly_work_reports(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_report_attachments ADD CONSTRAINT weekly_work_report_attachments_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_report_attachments ADD CONSTRAINT weekly_work_report_attachments_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;


CREATE TABLE sales_rep_workstation.weekly_work_report_attachments (
    id bigserial NOT NULL,
    weekly_work_report_id int8 NOT NULL,                    
    created_at timestamptz(0) DEFAULT now() NOT NULL,
	updated_at timestamptz(0) DEFAULT now() NOT NULL,
	deleted bool DEFAULT false NOT NULL,
	"name" varchar(300) NOT NULL,
	s3_key varchar(300) NOT NULL,
	created_user_id int8 NULL,
	updated_user_id int8 NULL,
	"extension" varchar(30) NOT NULL,
    CONSTRAINT weekly_work_report_attachments_pkey PRIMARY KEY (id)
);

ALTER TABLE sales_rep_workstation.weekly_work_report_attachments ADD CONSTRAINT weekly_work_report_attachments_weekly_work_report_id_foreign FOREIGN KEY (weekly_work_report_id) REFERENCES sales_rep_workstation.weekly_work_reports(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_report_attachments ADD CONSTRAINT weekly_work_report_attachments_created_user_id_foreign FOREIGN KEY (created_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_report_attachments ADD CONSTRAINT weekly_work_report_attachments_updated_user_id_foreign FOREIGN KEY (updated_user_id) REFERENCES public.users(id) ON UPDATE CASCADE;

create table sales_rep_workstation.weekly_records (
	 id bigserial NOT NULL,
	 week_start_date date  NULL,
	 week_end_date date NULL,
	 deleted bool DEFAULT false NOT NULL,
	 CONSTRAINT weekly_records_pkey PRIMARY KEY (id)
);

CREATE TABLE sales_rep_workstation.weekly_work_reports_to_this_week_visits (
	weekly_work_report_id int8 not null,
	this_week_visit_id int8 not null,
	CONSTRAINT weekly_work_reports_to_this_week_visits_pkey PRIMARY KEY (weekly_work_report_id, this_week_visit_id)
);


ALTER TABLE sales_rep_workstation.weekly_work_reports_to_this_week_visits ADD CONSTRAINT weekly_work_reports_to_this_week_visits_weekly_work_report_id_foreign FOREIGN KEY (weekly_work_report_id) REFERENCES sales_rep_workstation.weekly_work_reports(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports_to_this_week_visits ADD CONSTRAINT weekly_work_reports_to_this_week_visits_this_week_visit_id_foreign FOREIGN KEY (this_week_visit_id) REFERENCES sales_rep_workstation.visits(id) ON UPDATE CASCADE;

CREATE TABLE sales_rep_workstation.weekly_work_reports_to_next_week_visits (
	weekly_work_report_id int8 not null,
    next_week_visit_id int8 not null,
	CONSTRAINT weekly_work_reports_to_next_week_visits_pkey PRIMARY KEY (weekly_work_report_id, next_week_visit_id)
);

ALTER TABLE sales_rep_workstation.weekly_work_reports_to_next_week_visits ADD CONSTRAINT weekly_work_reports_to_next_week_visits_weekly_work_report_id_foreign FOREIGN KEY (weekly_work_report_id) REFERENCES sales_rep_workstation.weekly_work_reports(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.weekly_work_reports_to_next_week_visits ADD CONSTRAINT weekly_work_reports_to_next_week_visits_next_week_visit_id_foreign FOREIGN KEY (next_week_visit_id) REFERENCES sales_rep_workstation.visits(id) ON UPDATE CASCADE;


CREATE TABLE sales_rep_workstation.sales_weekly (
	sales_team_unit_id int8 not null,
    weekly_records_id int8 not null,
	CONSTRAINT salesWeekly_pkey PRIMARY KEY (sales_team_unit_id, weekly_records_id)
);

ALTER TABLE sales_rep_workstation.sales_weekly ADD CONSTRAINT sales_weekly_sales_team_unit_id_foreign FOREIGN KEY (sales_team_unit_id) REFERENCES sales_rep_workstation.sales_team_units(id) ON UPDATE CASCADE;
ALTER TABLE sales_rep_workstation.sales_weekly ADD CONSTRAINT sales_weekly_weekly_records_id_foreign FOREIGN KEY (weekly_records_id) REFERENCES sales_rep_workstation.weekly_records(id) ON UPDATE CASCADE;

INSERT INTO "permission".permissions
("name", code, description, deleted, group_id, application_id)
values
('工作週報讀取', 'weeklyWorkReport.read', NULL, false, 26, 1),
('工作週報新增', 'weeklyWorkReport.create', NULL, false, 26, 1),
('工作週報更新', 'weeklyWorkReport.update', NULL, false, 26, 1),
('工作週報刪除', 'weeklyWorkReport.delete', NULL, false, 26, 1),
('工作週報讀取', 'weeklyWorkReport.read', NULL, false, 26, 2),
('工作週報新增', 'weeklyWorkReport.create', NULL, false, 26, 2),
('工作週報更新', 'weeklyWorkReport.update', NULL, false, 26, 2),
('工作週報刪除', 'weeklyWorkReport.delete', NULL, false, 26, 2)




INSERT INTO "permission".permission_groups
("name", code, description)
VALUES('工作週報', 'weeklyWorkReport', '');

WITH weekly_data AS (
    SELECT
        generate_series(
            '2024-01-01'::date,           -- 開始日期
            '2024-12-31'::date,           -- 結束日期
            '1 week'::interval            -- 間隔為每週
        )::date AS week_start
)


INSERT INTO sales_rep_workstation.weekly_records (week_start_date, week_end_date)
SELECT 
    week_start, 
    week_start + INTERVAL '6 days' AS week_end  -- 每週結束日期
FROM 
    weekly_data 
ORDER BY 
    week_start;

 WITH weekly_data AS (
    SELECT
        generate_series(
            '2025-01-06'::date,           -- 開始日期
            '2025-12-31'::date,           -- 結束日期
            '1 week'::interval            -- 間隔為每週
        )::date AS week_start
)
INSERT INTO sales_rep_workstation.weekly_records (week_start_date, week_end_date)
SELECT 
    week_start, 
    week_start + INTERVAL '6 days' AS week_end  -- 每週結束日期
FROM 
    weekly_data 
ORDER BY 
    week_start;
