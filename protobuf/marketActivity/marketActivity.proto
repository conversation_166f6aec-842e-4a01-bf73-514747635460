syntax = "proto3";

package marketActivity;

import "google/protobuf/timestamp.proto";

service MarketActivity {
    rpc create(CreateMarketActivityParams) returns (MarketActivityResult) {};
    rpc update(UpdateMarketActivityParams) returns (MarketActivityResult) {};
    rpc delete(DeleteMarketActivityParams) returns (RequestResult) {};
    rpc sendApproval(SendApprovalMarketActivityParams) returns (RequestResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message ExpertFeeItemParams {
    optional int32 expertTypeId = 1;
    optional string name = 2;
    optional string expense = 3;
    optional string memo = 4;
    optional int32 expertLevelId = 5;
    optional int32 expertServiceTimeId = 6;
}

message FeeItemParams {
    optional int32 feeTypeId = 1;
    optional string expense = 2;
    optional string memo = 3;
}

message ActualExpertFeeItemParams {
    optional int32 expertTypeId = 1;
    optional string name = 2;
    optional string expense = 3;
    optional string memo = 4;
    optional int32 expertLevelId = 5;
    optional int32 expertServiceTimeId = 6;
}

message ActualFeeItemParams {
    optional int32 feeTypeId = 1;
    optional string expense = 2;
    optional string memo = 3;
}

message InternalAttendance {
   optional int32 departmentId = 1;
   optional int32 costCenterId = 2;
   optional int32 internalCount = 3;
   optional string estimatedCost = 4;
}

message ExternalAttendance {
   optional int32 customerId = 1;
   optional int32 customerCount = 2;
}

message CreateMarketActivityParams {
    int32 createdUserId = 1;
    string name = 2;
    optional string link = 3;
    google.protobuf.Timestamp date1 = 4;
    google.protobuf.Timestamp date2 = 5;
    int32 typeId = 6;
    int32 participationTypeId = 7;
    int32 natureId = 8;
    optional string memo = 9;
    optional int32 regionId = 10;
    optional int32 provinceId = 11;
    optional int32 cityId = 12;
    optional int32 districtId = 13;
    optional string address = 14;
    optional int32 departmentId = 15;
    optional int32 primaryUserId = 16;
    optional string expenseBudget = 17;
    optional string budgetCode = 18;
    optional int32 status = 19;
    repeated AttachmentParams attachFiles = 20;
    repeated FeeItemParams feeItems = 21;
    repeated ExpertFeeItemParams expertFeeItems = 22;
    repeated int32 businessProductIds = 23;
    optional int32 fundingSourceId = 24;
    repeated InternalAttendance internalAttendances = 25;
    repeated ExternalAttendance externalAttendances = 26;
    optional string executionCode = 27;
    optional string closureCode = 28;
    optional string confirmExpense = 29;
    repeated InternalAttendance internalRegistrations = 30;
    repeated ExternalAttendance externalRegistrations = 31;
    optional int32 costCenterId = 32;
    repeated ActualFeeItemParams actualFeeItems = 33;
    repeated ActualExpertFeeItemParams actualExpertFeeItems = 34;

    
}

message UpdateMarketActivityParams {
    int32 id = 1;
    optional string name = 2;
    optional string link = 3;
    optional google.protobuf.Timestamp date1 = 4;
    optional google.protobuf.Timestamp date2 = 5;
    optional int32 typeId = 6;
    optional int32 participationTypeId = 7;
    optional int32 natureId = 8;
    optional string memo = 9;
    optional int32 regionId = 10;
    optional int32 provinceId = 11;
    optional int32 cityId = 12;
    optional int32 districtId = 13;
    optional string address = 14;
    optional int32 departmentId = 15;
    optional int32 primaryUserId = 16;
    optional string expenseBudget = 17;
    optional string budgetCode = 18;
    optional int32 status = 19;
    repeated AttachmentParams createdAttachFiles = 20;
    repeated FeeItemParams feeItems = 21;
    repeated ExpertFeeItemParams expertFeeItems = 22;
    repeated int32 deletedAttachFileIds = 23;
    int32 updatedUserId = 24;
    repeated int32 businessProductIds = 25;
    optional int32 fundingSourceId = 26;
    repeated InternalAttendance internalAttendances = 27;
    repeated ExternalAttendance externalAttendances = 28;
    optional string executionCode = 29;
    optional string closureCode = 30;
    optional string confirmExpense = 31;
    repeated InternalAttendance internalRegistrations = 32;
    repeated ExternalAttendance externalRegistrations = 33;
    optional int32 costCenterId = 34;
    repeated ActualFeeItemParams actualFeeItems = 35;
    repeated ActualExpertFeeItemParams actualExpertFeeItems = 36;
}

message DeleteMarketActivityParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message MarketActivityData {
    int32 id = 1;
}

message MarketActivityResult {
    bool success = 1;
    message Result {
        MarketActivityData data = 1;
    }
    optional Result result = 2;
}

message SendApprovalMarketActivityParams {
    int32 id = 1;
    int32 updatedUserId = 2;
    optional int32 approvalStatus = 3;
}



message RequestResult {
    bool success = 1;
}