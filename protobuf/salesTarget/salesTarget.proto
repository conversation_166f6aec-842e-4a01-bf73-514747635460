syntax = "proto3";

package salesTarget;

service SalesTarget {
    rpc batchCreate(BatchCreateParams) returns (BatchCreateResult) {};

}

message CreateParams {
    string Key = 1;
    string salesTeamUnitRole = 2;
    int32 year = 3;
    string month = 4;
    string amount = 5;
    int32 qty = 6;
    string eyeProductGroupName = 7;
}

message BatchCreateParams {
    repeated CreateParams salesTargets = 1;
}

message BatchCreateResult {
    bool success = 1;
    message Result {
        repeated int32 ids = 1;
    }
    optional Result result = 2;
}