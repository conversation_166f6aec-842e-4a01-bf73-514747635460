syntax = "proto3";

package eyeServiceOrderAttachFile;

service EyeServiceOrderAttachFile {
    rpc create(CreateEyeServiceOrderAttachFileParams) returns (RequestResult) {};
    rpc delete(DeleteEyeServiceOrderAttachFileParams) returns (RequestResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message CreateEyeServiceOrderAttachFileParams {
    int32 eyeServiceOrderId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message DeleteEyeServiceOrderAttachFileParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message RequestResult {
    bool success = 1;
}