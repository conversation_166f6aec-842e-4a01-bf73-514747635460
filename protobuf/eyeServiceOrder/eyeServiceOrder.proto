syntax = "proto3";

package eyeServiceOrder;

import "google/protobuf/timestamp.proto";

service EyeServiceOrder {
    rpc create(CreateEyeServiceOrderParams) returns (EyeServiceOrderResult) {};
    rpc update(UpdateEyeServiceOrderParams) returns (EyeServiceOrderResult) {};
    rpc requestExtension(RequestEyeServiceOrderExtensionParams) returns (RequestResult) {};
    rpc cancel(CancelEyeServiceOrderParams) returns (RequestResult) {};
    rpc confirm(ConfirmEyeServiceOrderParams) returns (RequestResult) {};
    rpc requestRecordLog(RequestUpdateEyeFixedAssetRentalParams) returns (RequestResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message EyeFixedAssetRentalRecord {
        optional google.protobuf.Timestamp installDate = 1;
        google.protobuf.Timestamp date1 = 2;
        google.protobuf.Timestamp date2 = 3;
        optional google.protobuf.Timestamp dismantleDate = 4;
        optional string remark = 5;

        // relations
        repeated int32 eyeFixedAssetIds = 6;
}

message EyeServiceOrderItem {
    int32 materialId = 1;
    optional string sn = 2;
    optional string udi = 3;
    int32 qty = 4;
    optional string memo = 5;
}

message CreateEyeServiceOrderParams {
    optional int32 eyeWarrantyContractId = 1;
    int32 eyeServiceOrderTypeId = 2;
    int32 regionId = 3;
    optional int32 companyId = 4;
    optional int32 customerId = 5;
    optional int32 userId = 6;
    int32 priority = 7;
    int32 status = 8;
    google.protobuf.Timestamp estimatedDate = 9;
    optional string contactPerson = 10;
    optional string contactPhone = 11;
    optional string description = 12;
    optional string memo = 13;
    optional int32 provinceId = 14;
    optional int32 cityId = 15;
    optional int32 districtId = 16;
    optional string address = 17;
    int32 assigneeDeptId = 18;
    optional int32 assigneeUserId = 19;
    optional int32 approverUserId = 20;
    optional int32 businessId = 21;
    optional int32 dispatcherUserId = 22;
    optional int32 costCenterId = 23;
    int32 approvalStatus = 24;
    optional int32 eyeFixedAssetRentalObjectId = 25;
    optional int32 eyeFixedAssetRentalGoalId = 26;

    repeated EyeServiceOrderItem items = 27;
    repeated EyeFixedAssetRentalRecord eyeFixedAssetRentalRecords= 28;

    int32 createdUserId = 29;
    optional int32 installationCustomerId = 30;
    optional string installationCustomerContactPerson = 31;
    optional string installationCustomerContactPhone = 32;
    optional string installationCustomerAddress = 33;
    optional int32 installationCustomerEyeWarrantyContractId = 34;
    repeated AttachmentParams attachments = 35;

}

message UpdateEyeServiceOrderParams {
    int32 id = 1;
    optional int32 eyeServiceOrderTypeId = 2;
    optional int32 priority = 3;
    optional int32 status = 4;
    optional google.protobuf.Timestamp estimatedDate = 5;
    optional string contactPerson = 6;
    optional string contactPhone = 7;
    optional string description = 8;
    optional string memo = 9;
    optional int32 provinceId = 10;
    optional int32 cityId = 11;
    optional int32 districtId = 12;
    optional string address = 13;
    optional int32 assigneeUserId = 14;
    optional int32 approverUserId = 15;
    optional int32 businessId = 16;
    optional int32 dispatcherUserId = 17;
    optional int32 costCenterId = 18;
    optional int32 approvalStatus = 19;
    optional int32 eyeFixedAssetRentalObjectId = 20;
    optional int32 eyeFixedAssetRentalGoalId = 21;
    int32 updatedUserId = 22;
    optional int32 installationCustomerId = 23;
    optional string installationCustomerContactPerson = 24;
    optional string installationCustomerContactPhone = 25;
    optional string installationCustomerAddress = 26;
    optional int32 installationCustomerEyeWarrantyContractId = 27;
    repeated AttachmentParams createdAttachments = 28;
    repeated int32 deletedAttachmentIds = 29;
}

message EyeServiceOrderData {
    int32 id = 1;
}

message EyeServiceOrderResult {
    bool success = 1;
    message Result {
        EyeServiceOrderData data = 1;
    }
    optional Result result = 2;
}

message RequestEyeServiceOrderExtensionParams {
    int32 id = 1;
    google.protobuf.Timestamp extendDate1 = 2;
    google.protobuf.Timestamp extendDate2 = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}

message RequestUpdateEyeFixedAssetRentalParams {
    int32 id = 1;
    google.protobuf.Timestamp newDate1 = 2;
    google.protobuf.Timestamp newDate2 = 3;
    string newSn = 4;
    optional string memo = 5;
    google.protobuf.Timestamp date1 = 6;
    google.protobuf.Timestamp date2 = 7;
    string sn = 8;
    int32 createdUserId = 9;
}

message CancelEyeServiceOrderParams {
    int32 id = 1;
    int32 updatedUserId = 2;
}

message ConfirmEyeServiceOrderParams {
    int32 id = 1;
    int32 updatedUserId = 2;
}

message RequestResult {
    bool success = 1;
}