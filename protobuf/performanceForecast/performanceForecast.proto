syntax = "proto3";

package performanceForecast;

service PerformanceForecast {
    rpc bulkUpdate(BulkUpdatePerformanceForecastParams) returns (BulkPerformanceForecastResult) {};
    rpc bulkCreateByYear(BulkCreateByYearParams) returns (RequestResult) {};
}

message ForecastBusinessParams {
    int32 businessId = 1;
    string estimatedUntaxedAmount = 2;
}

message BulkUpdatePerformanceForecastParams {
    repeated UpdatePerformanceForecastParams inputs = 1;
}

message UpdatePerformanceForecastParams {
    int32 salesTeamUnitId = 1;
    int32 year = 2;
    int32 month = 3;
    string amountEarlyMonth = 4;
    string amountMidMonth = 5;
    string amountLateMonth = 6;
    int32 updatedUserId = 7;
    repeated ForecastBusinessParams forecastBusinesses = 8;
}

message BulkPerformanceForecastResult {
    bool success = 1;
    message Result {
        repeated int32 ids = 1;
    }
    optional Result result = 2;
}

message BulkCreateByYearParams {
    int32 year = 1;
}

message RequestResult {
    bool success = 1;
}
