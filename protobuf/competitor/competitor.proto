
syntax = "proto3";

package competitor;

service Competitor {
    rpc create(CreateCompetitorParams) returns (CompetitorResult) {};
    rpc update(UpdateCompetitorParams) returns (CompetitorResult) {};
    rpc delete(DeleteCompetitorParams) returns (CompetitorResult) {};
}

message CreateCompetitorParams {
    optional int32 salesTeamGroupId = 1;
    string name = 2;
    optional string advantage = 3;
    optional string disadvantage = 4;
    optional string strategy = 5;
    optional string brand = 6;
    optional string model = 7;
    optional string agents = 8;
    optional string memo = 9;
    optional int32 regionId = 10;
    repeated int32 businessProductIds = 11;
    int32 createdUserId = 12;
}

message UpdateCompetitorParams {
    int32 id = 1;
    optional string name = 2;
    optional string advantage = 3;
    optional string disadvantage = 4;
    optional string strategy = 5;
    optional string brand = 6;
    optional string model = 7;
    optional string agents = 8;
    optional string memo = 9;
    repeated int32 businessProductIds = 10;
    int32 updatedUserId = 11;
}

message CompetitorData {
    int32 id = 1;
}

message CompetitorResult {
    bool success = 1;
    message Result {
        CompetitorData data = 1;
    }
    optional Result result = 2;
}

message DeleteCompetitorParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}