syntax = "proto3";

package bidEquipment;

import "google/protobuf/timestamp.proto";

service BidEquipment {
    rpc create(CreateBidEquipmentParams) returns (BidEquipmentResult) {};
    rpc update(UpdateBidEquipmentParams) returns (BidEquipmentResult) {};
    rpc delete(DeleteBidEquipmentParams) returns (BidEquipmentResult) {};
}



message CreateBidEquipmentParams {
    optional string code = 1;
    optional string name = 2;
    int32 createdUserId = 3;
}

message UpdateBidEquipmentParams {
    int32 id = 1;
    optional string code = 2;
    optional string name = 3;
    int32 updatedUserId = 4;
}

message DeleteBidEquipmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message BidData {
    int32 id = 1;
}

message BidEquipmentResult {
    bool success = 1;
    message Result {
        BidData data = 1;
    }
    optional Result result = 2;
}
