syntax = "proto3";

package bidAttachment;

service BidAttachment {
    rpc create(CreateBidAttachmentParams) returns (BidAttachmentUpdateResult) {};
    rpc bulkCreate(BulkCreateBidAttachmentParams) returns (BidBulkAttachmentCreateResult) {};
    rpc update(UpdateBidAttachmentParams) returns (BidAttachmentUpdateResult) {};
    rpc delete(DeleteBidAttachmentParams) returns (BidAttachmentUpdateResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}


message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message CreateBidAttachmentParams {
    int32 bidId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}

message BulkCreateBidAttachmentParams {
    int32 bidId = 1;
    repeated AttachmentParams attachments = 2;
    int32 createdUserId = 3;
}


message UpdateBidAttachmentParams {
    int32 id = 1;
    optional string name= 2;
    optional string memo= 3;
    int32 updatedUserId = 4;
}

message DeleteBidAttachmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message AttachmentData {
    repeated int32 ids = 1;
}

message BidAttachmentData {
    int32 id = 1;
}

message BidAttachmentUpdateResult {
    bool success = 1;
    message Result {
        BidAttachmentData data = 1;
    }
    optional Result result = 2;
}

message BidBulkAttachmentCreateResult {
    bool success = 1;
    message Result {
        AttachmentData data = 1;
    }
    optional Result result = 2;
}