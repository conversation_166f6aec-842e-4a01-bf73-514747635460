syntax = "proto3";

package bid;

import "google/protobuf/timestamp.proto";

service Bid {
    rpc create(CreateBidParams) returns (BidResult) {};
    rpc update(UpdateBidParams) returns (BidResult) {};
    rpc delete(DeleteBidParams) returns (BidResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message CreateBidParams {
    int32 regionId = 1;
    int32 customerId = 2;
    optional string name = 3;
    google.protobuf.Timestamp bidDate = 4;
    optional string publicLinkUrl = 5;
    optional string content = 6;
    optional string result = 7;
    optional string notes = 8;
    repeated int32 equipmentIds = 9;
    repeated AttachmentParams attachments = 10;
    int32 createdUserId = 11;
    google.protobuf.Timestamp publishDate = 12;
    google.protobuf.Timestamp registrationDate = 13;
}

message UpdateBidParams {
    int32 id = 1;
    optional int32 customerId = 2;
    optional string name = 3;
    google.protobuf.Timestamp bidDate = 4;
    optional string publicLinkUrl = 5;
    optional string content = 6;
    optional string result = 7;
    optional string notes = 8;
    repeated int32 equipmentIds = 9;
    int32 updatedUserId = 10;
    google.protobuf.Timestamp publishDate = 11;
    google.protobuf.Timestamp registrationDate = 12;
}

message DeleteBidParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message BidData {
    int32 id = 1;
}

message BidResult {
    bool success = 1;
    message Result {
        BidData data = 1;
    }
    optional Result result = 2;
}
