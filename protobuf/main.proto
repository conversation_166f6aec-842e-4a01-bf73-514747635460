syntax = "proto3";

import public "demo/demo.proto";
// customer
import public "customer/customer.proto";
import public "customer/customerArea.proto";
import public "customer/customerGroup.proto";
import public "customer/customerProperty.proto";
import public "customer/customerPropertyType.proto";
import public "customer/customerType.proto";
import public "customer/customerCertificate.proto";
import public "customer/customerCertificateAttachment.proto";
import public "customer/customerAttachment.proto";
import public "customer/customerEquipment.proto";
import public "customer/customerEquipmentAttachment.proto";
//business
import public "business/business.proto";
import public "business/businessMemo.proto";
import public "business/businessOpportunity.proto";
import public "business/businessProduct.proto";
import public "business/businessProperty.proto";
import public "business/businessPropertyType.proto";
import public "business/businessStatus.proto";
import public "business/businessType.proto";
//visit
import public "visit/visit.proto";
import public "visit/visitAction.proto";
import public "visit/visitGoal.proto";
import public "visit/visitMemo.proto";
import public "visit/visitProperty.proto";
import public "visit/visitPropertyType.proto";
import public "visit/visitTimePeriod.proto";
import public "visit/visitType.proto";
import public "visit/visitCheckIn.proto";
//contactPerson
import public "contactPerson/contactPerson.proto";
import public "contactPerson/contactPersonType.proto";
import public "contactPerson/contactPersonAttachment.proto";
//competitor
import public "competitor/competitor.proto";
//eyeQuotationOrder
import public "eyeQuotationOrder/eyeQuotationOrder.proto";
import public "eyeQuotationOrder/eyeQuotationOrderAttachFile.proto";
//eyeServiceOrder
import public "eyeServiceOrder/eyeServiceOrder.proto";
import public "eyeServiceOrder/eyeServiceOrderAttachFile.proto";
//eyeServiceOrderWorkDiary
import public "eyeServiceOrderWorkDiary/eyeServiceOrderWorkDiary.proto";
import public "eyeServiceOrderWorkDiary/eyeServiceOrderWorkDiaryAttachFile.proto";
//salesTarget
import public "salesTarget/salesTarget.proto";
//marketActivity
import public "marketActivity/marketActivity.proto";
// bid
import public "bid/bid.proto";
import public "bid/bidAttachment.proto";
import public "bid/bidEquipment.proto";
//distributorAuthorization
import public "distributorAuthorization/distributorAuthorization.proto";
import public "distributorAuthorization/distributorAuthorizationAttachment.proto";
//performanceForecast
import public "performanceForecast/performanceForecast.proto";
// weeklyWorkReport
import public "weeklyWorkReport/weeklyRecord.proto";
import public "weeklyWorkReport/weeklyWorkReport.proto";
import public "weeklyWorkReport/weeklyWorkReportAttachment.proto";