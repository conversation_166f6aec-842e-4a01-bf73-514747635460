syntax = "proto3";

package distributorAuthorization;

import "google/protobuf/timestamp.proto";

service DistributorAuthorization {
    rpc create(CreateDistributorAuthorizationParams) returns (DistributorAuthorizationResult) {};
    rpc update(UpdateDistributorAuthorizationParams) returns (DistributorAuthorizationResult) {};
    rpc createBPM(CreateDistributorAuthorizationBPMParams) returns (DistributorAuthorizationResult) {};
    rpc cancel(CancelDistributorAuthorizationParams) returns (RequestResult) {};
}

message CreateDistributorAuthorizationParams {
    int32 companyId = 1;
    int32 customerId= 2;
    int32 dealerId= 3;
    string projectName = 4;
    optional string projectCode = 5;
    optional string planCode = 6;
    optional string memo = 7;
    optional google.protobuf.Timestamp startDate = 8;
    optional google.protobuf.Timestamp endDate = 9;
    repeated int32 authorizationProductIds = 10;
    int32 createdUserId = 11;
}

message UpdateDistributorAuthorizationParams {
    int32 id = 1;
    optional int32 companyId = 2;
    optional int32 customerId = 3;
    int32 dealerId= 4;
    optional string projectName = 5;
    optional string projectCode= 6;
    optional string planCode= 7;
    optional string memo= 8;
    optional  google.protobuf.Timestamp startDate = 9;
    optional  google.protobuf.Timestamp endDate = 10;
    repeated int32 authorizationProductIds = 11;
    optional string bpmInstanceId = 12;
    optional int32 approvalStatus = 13;
    int32 updatedUserId = 14;
}

message CancelDistributorAuthorizationParams {
    int32 distributorAuthorizationId = 1;
}

message CreateDistributorAuthorizationBPMParams {
    int32 distributorAuthorizationId = 1;
}

message DistributorAuthorizationData {
    int32 id = 1;
}

message DistributorAuthorizationResult {
    bool success = 1;
    message Result {
        DistributorAuthorizationData data = 1;
    }
    optional Result result = 2;
}

message RequestResult {
    bool success = 1;
}