syntax = "proto3";

package visit;

service VisitMemo {
    rpc create(CreateVisitMemoParams) returns (VisitMemoResult) {};
    rpc update(UpdateVisitMemoParams) returns (VisitMemoResult) {};
    rpc delete(DeleteVisitMemoParams) returns (VisitMemoResult) {};
}

message CreateVisitMemoParams {
    optional string title = 1;
    optional string content = 2;
    optional int32 visitId = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateVisitMemoParams {
    int32 id = 1;
    optional string title = 2;
    optional string content = 3;
    optional int32 visitId = 4;
    int32 updatedUserId = 5;
}

message VisitMemoData {
    int32 id = 1;
}

message VisitMemoResult {
    bool success = 1;
    message Result {
        VisitMemoData data = 1;
    }
    optional Result result = 2;
}

message DeleteVisitMemoParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}