syntax = "proto3";

package visit;

service VisitGoal {
    rpc create(CreateVisitGoalParams) returns (VisitGoalResult) {};
    rpc update(UpdateVisitGoalParams) returns (VisitGoalResult) {};
}

message CreateVisitGoalParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateVisitGoalParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message VisitGoalData {
    int32 id = 1;
}

message VisitGoalResult {
    bool success = 1;
    message Result {
        VisitGoalData data = 1;
    }
    optional Result result = 2;
}