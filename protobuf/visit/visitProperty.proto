syntax = "proto3";

package visit;

service VisitProperty {
    rpc create(CreateVisitPropertyParams) returns (VisitPropertyResult) {};
    rpc update(UpdateVisitPropertyParams) returns (VisitPropertyResult) {};
}

message CreateVisitPropertyParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 salesTeamGroupId = 3;
    int32 typeId = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message UpdateVisitPropertyParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    optional int32 typeId = 4;
    int32 updatedUserId = 5;
}

message VisitPropertyData {
    int32 id = 1;
}

message VisitPropertyResult {
    bool success = 1;
    message Result {
        VisitPropertyData data = 1;
    }
    optional Result result = 2;
}