syntax = "proto3";

package visit;

service VisitPropertyType {
    rpc create(CreateVisitPropertyTypeParams) returns (VisitPropertyTypeResult) {};
    rpc update(UpdateVisitPropertyTypeParams) returns (VisitPropertyTypeResult) {};
}

message CreateVisitPropertyTypeParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateVisitPropertyTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message VisitPropertyTypeData {
    int32 id = 1;
}

message VisitPropertyTypeResult {
    bool success = 1;
    message Result {
        VisitPropertyTypeData data = 1;
    }
    optional Result result = 2;
}