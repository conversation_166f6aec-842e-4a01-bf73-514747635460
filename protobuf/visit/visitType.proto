syntax = "proto3";

package visit;

service VisitType {
    rpc create(CreateVisitTypeParams) returns (VisitTypeResult) {};
    rpc update(UpdateVisitTypeParams) returns (VisitTypeResult) {};
}

message CreateVisitTypeParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateVisitTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message VisitTypeData {
    int32 id = 1;
}

message VisitTypeResult {
    bool success = 1;
    message Result {
        VisitTypeData data = 1;
    }
    optional Result result = 2;
}