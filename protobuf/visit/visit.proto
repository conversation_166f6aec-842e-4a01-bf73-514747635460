syntax = "proto3";

package visit;

import "google/protobuf/timestamp.proto";

service Visit {
    rpc create(CreateVisitParams) returns (VisitResult) {};
    rpc update(UpdateVisitParams) returns (VisitResult) {};
    rpc bulkUpdate(BulkUpdateVisitParams) returns (BulkVisitResult) {};
    rpc delete(DeleteVisitParams) returns (VisitResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message CreateVisitParams {
    int32 salesTeamGroupId = 1;
    optional int32 salesTeamId = 2;
    optional string title = 3;
    optional string content = 4;
    optional string visitAddress = 5;
    google.protobuf.Timestamp visitDate = 6;
    optional string priorityOrder = 7;
    string status = 8;
    optional int32 typeId = 9;
    optional int32 businessId = 10;
    optional int32 actionId = 11;
    optional string actionContent = 12;
    optional int32 customerId = 13;
    optional int32 salesTeamUnitId = 14;
    optional int32 primaryUserId = 15;
    optional bool hasAssistedVisitSupervisor = 16;
    optional int32 assistedVisitSupervisorId = 17;
    optional int32 visitPlaceId = 18;
    optional string visitResult = 19;

    // relations
    repeated int32 primaryContactPersonIds = 20;
    repeated int32 propertyIds = 21;
    repeated int32 timePeriodIds = 22;
    repeated int32 mentionProductIds = 23;
    repeated int32 rentProductIds = 24;
    optional int32 regionId = 25;
    int32 createdUserId = 26;
    repeated AttachmentParams attachFiles = 27;
}

message UpdateVisitParams {
    int32 id = 1;
    optional string title = 2;
    optional string content = 3;
    optional string visitAddress = 4;
    optional google.protobuf.Timestamp visitDate = 5;
    optional string priorityOrder = 6;
    optional string status = 7;
    optional int32 typeId = 8;
    optional int32 businessId = 9;
    optional int32 actionId = 10;
    optional string actionContent = 11;
    optional int32 customerId = 12;
    optional int32 salesTeamId = 13;
    optional int32 salesTeamUnitId = 14;
    optional int32 primaryUserId = 15;
    optional bool hasAssistedVisitSupervisor = 16;
    optional int32 assistedVisitSupervisorId = 17;
    optional int32 visitPlaceId = 18;
    optional string visitResult = 19;

    // relations
    repeated int32 primaryContactPersonIds = 20;
    repeated int32 propertyIds = 21;
    repeated int32 timePeriodIds = 22;
    repeated int32 mentionProductIds = 23;
    repeated int32 rentProductIds = 24;
    int32 updatedUserId = 25;
    repeated AttachmentParams createdAttachFiles = 26;
    repeated int32 deletedAttachFileIds = 27;
}

message BulkUpdateVisitParams {
    repeated UpdateVisitParams visits = 1;
}

message VisitData {
    int32 id = 1;
}

message BulkVisitResult {
    bool success = 1;
    message Result {
        repeated int32 ids = 1;
    }
    optional Result result = 2;
}

message VisitResult {
    bool success = 1;
    message Result {
        VisitData data = 1;
    }
    optional Result result = 2;
}

message DeleteVisitParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}