syntax = "proto3";

package visit;

service VisitAction {
    rpc create(CreateVisitActionParams) returns (VisitActionResult) {};
    rpc update(UpdateVisitActionParams) returns (VisitActionResult) {};
}

message CreateVisitActionParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateVisitActionParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message VisitActionData {
    int32 id = 1;
}

message VisitActionResult {
    bool success = 1;
    message Result {
        VisitActionData data = 1;
    }
    optional Result result = 2;
}