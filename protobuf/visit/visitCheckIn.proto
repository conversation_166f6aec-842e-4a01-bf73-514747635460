syntax = "proto3";

package visit;

service VisitCheckIn {
    rpc create(CreateVisitCheckInParams) returns (VisitCheckInResult) {};
    rpc update(UpdateVisitCheckInParams) returns (VisitCheckInResult) {};
}

message CreateVisitCheckInParams {
    int32 createdUserId = 1;
    int32 visitId = 2;
    float lat = 3;
    float lng = 4;
    optional string content = 5;
}

message UpdateVisitCheckInParams {
    int32 id = 1;
    int32 updatedUserId = 2;
    optional string content = 3;
}

message VisitCheckInData {
    int32 id = 1;
}

message VisitCheckInResult {
    bool success = 1;
    message Result {
        VisitCheckInData data = 1;
    }
    optional Result result = 2;
}
