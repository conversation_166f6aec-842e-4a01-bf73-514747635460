syntax = "proto3";

package visit;

service VisitTimePeriod {
    rpc create(CreateVisitTimePeriodParams) returns (VisitTimePeriodResult) {};
    rpc update(UpdateVisitTimePeriodParams) returns (VisitTimePeriodResult) {};
}

message CreateVisitTimePeriodParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateVisitTimePeriodParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message VisitTimePeriodData {
    int32 id = 1;
}

message VisitTimePeriodResult {
    bool success = 1;
    message Result {
        VisitTimePeriodData data = 1;
    }
    optional Result result = 2;
}