syntax = "proto3";

package eyeServiceOrderWorkDiary;

import "google/protobuf/timestamp.proto";

service EyeServiceOrderWorkDiary {
    rpc create(CreateEyeServiceOrderWorkDiaryParams) returns (EyeServiceOrderWorkDiaryResult) {};
    rpc update(UpdateEyeServiceOrderWorkDiaryParams) returns (EyeServiceOrderWorkDiaryResult) {};
}

message EyeServiceOrderWorkDiaryItem {
    int32 materialId = 1;
    optional string sn = 2;
}

message CreateEyeServiceOrderWorkDiaryParams {
    int32 createdUserId = 1;
    int32 eyeServiceOrderId = 2;
    optional int32 eyeFixedAssetRentalRecordId = 3;
    int32 eyeServiceOrderWorkDiaryTypeId = 4;
    int32 status = 5;
    optional google.protobuf.Timestamp date = 6;
    optional int32 hours = 7;
    optional string remark = 8;
    optional google.protobuf.Timestamp estimatedDate = 9;
    optional int32 estimatedHours = 10;
    repeated int32 userIds = 11;
    repeated EyeServiceOrderWorkDiaryItem items = 12;
    optional google.protobuf.Timestamp completionDate = 13;
    optional int32 eyeServiceOrderWorkDiaryMachineFlowId = 14;
    optional int32 logisticsSupplierId = 15;
    optional string logisticsNumber = 16;
}

message UpdateEyeServiceOrderWorkDiaryParams {
    int32 id = 1;
    int32 updatedUserId = 2;
    optional int32 eyeServiceOrderWorkDiaryTypeId = 3;
    optional int32 status = 4;
    optional google.protobuf.Timestamp date = 5;
    optional int32 hours = 6;
    optional string remark = 7;
    optional google.protobuf.Timestamp estimatedDate = 8;
    optional int32 estimatedHours = 9;
    repeated int32 userIds = 10;
    optional google.protobuf.Timestamp completionDate = 11;
    optional int32 eyeServiceOrderWorkDiaryMachineFlowId = 12;
    optional int32 logisticsSupplierId = 13;
    optional string logisticsNumber = 14;
}

message EyeServiceOrderWorkDiaryData {
    int32 id = 1;
}

message EyeServiceOrderWorkDiaryResult {
    bool success = 1;
    message Result {
        EyeServiceOrderWorkDiaryData data = 1;
    }
    optional Result result = 2;
}