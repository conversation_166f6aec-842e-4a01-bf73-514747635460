syntax = "proto3";

package eyeServiceOrderWorkDiaryAttachFile;

service EyeServiceOrderWorkDiaryAttachFile {
    rpc create(CreateEyeServiceOrderWorkDiaryAttachFileParams) returns (EyeServiceOrderWorkDiaryAttachFileUpdateResult) {};
    rpc bulkCreate(BulkCreateEyeServiceOrderWorkDiaryAttachFileParams) returns (BulkCreateEyeServiceOrderWorkDiaryAttachFileResult) {};
    rpc delete(DeleteEyeServiceOrderWorkDiaryAttachFileParams) returns (EyeServiceOrderWorkDiaryAttachFileUpdateResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}


message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message CreateEyeServiceOrderWorkDiaryAttachFileParams {
    int32 eyeServiceOrderWorkDiaryId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}

message BulkCreateEyeServiceOrderWorkDiaryAttachFileParams {
    int32 eyeServiceOrderWorkDiaryId = 1;
    repeated AttachmentParams attachments = 2;
    int32 createdUserId = 3;
}

message DeleteEyeServiceOrderWorkDiaryAttachFileParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message AttachmentData {
    repeated int32 ids = 1;
}

message EyeServiceOrderWorkDiaryAttachFileData {
    int32 id = 1;
}

message EyeServiceOrderWorkDiaryAttachFileUpdateResult {
    bool success = 1;
    message Result {
        EyeServiceOrderWorkDiaryAttachFileData data = 1;
    }
    optional Result result = 2;
}

message BulkCreateEyeServiceOrderWorkDiaryAttachFileResult {
    bool success = 1;
    message Result {
        AttachmentData data = 1;
    }
    optional Result result = 2;
}