syntax = "proto3";

package business;

import "google/protobuf/timestamp.proto";

service Business {
    rpc create(CreateBusinessParams) returns (BusinessResult) {};
    rpc update(UpdateBusinessParams) returns (BusinessResult) {};
    rpc delete(DeleteBusinessParams) returns (BusinessResult) {};
}

message DealProduct {
    int32 dealProductId = 1;
    optional int32 qty = 2;
}

message BudgetProduct {
    int32 budgetProductId = 1;
    optional int32 qty = 2;
}

message User {
    int32 userId = 1;
    optional int32 salesTeamId = 2;
}

message CreateBusinessParams {
    int32 salesTeamGroupId = 1;
    optional int32 typeId = 2;
    optional string title = 3;
    optional string content = 4;
    optional string orderCode = 5;
    optional string eyeQuotationOrderCode = 6;
    optional string budgetAmount = 7;
    optional string dealAmount = 8;
    optional google.protobuf.Timestamp expectedClosedDate = 9;
    optional google.protobuf.Timestamp closedDate = 10;
    optional int32 winningOpportunityId = 11;
    optional int32 buyingOpportunityId = 12;
    optional string losingReason = 13;
    optional string losingImprovementPlan = 14;
    optional int32 statusId = 15;
    optional int32 customerId = 16;
    optional int32 salesTeamId = 17;
    optional int32 salesTeamUnitId = 18;
    optional int32 primaryUserId = 19;
    optional string saleAmount = 20;
    optional int32 salesMethodId = 21;
    optional int32 dealerId = 22;
    optional string customerMemo = 23;

    // relations
    repeated int32 propertyIds = 24;
    repeated int32 primaryContactPersonIds = 25;
    repeated int32 competitorIds = 26;
    repeated DealProduct dealProducts = 27;
    repeated BudgetProduct budgetProducts = 28;
    repeated User users = 29;
    repeated int32 losingReasonIds = 30;

    optional int32 regionId = 31;
    int32 createdUserId = 32;
}

message UpdateBusinessParams {
    int32 id = 1;
    optional int32 typeId = 2;
    optional string title = 3;
    optional string content = 4;
    optional string orderCode = 5;
    optional string eyeQuotationOrderCode = 6;
    optional string budgetAmount = 7;
    optional string dealAmount = 8;
    optional google.protobuf.Timestamp expectedClosedDate = 9;
    optional google.protobuf.Timestamp closedDate = 10;
    optional int32 winningOpportunityId = 11;
    optional int32 buyingOpportunityId = 12;
    optional string losingReason = 13;
    optional string losingImprovementPlan = 14;
    optional int32 statusId = 15;
    optional int32 customerId = 16;
    optional int32 salesTeamId = 17;
    optional int32 salesTeamUnitId = 18;
    optional int32 primaryUserId = 19;
    optional string saleAmount = 20;
    optional int32 salesMethodId = 21;
    optional int32 dealerId = 22;
    optional string customerMemo = 23;

    // relations
    repeated int32 propertyIds = 24;
    repeated int32 primaryContactPersonIds = 25;
    repeated int32 competitorIds = 26;
    repeated DealProduct dealProducts = 27;
    repeated BudgetProduct budgetProducts = 28;
    repeated User users = 29;
    repeated int32 losingReasonIds = 30;

    int32 updatedUserId = 31;
}

message BusinessData {
    int32 id = 1;
}

message BusinessResult {
    bool success = 1;
    message Result {
        BusinessData data = 1;
    }
    optional Result result = 2;
}

message DeleteBusinessParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}