syntax = "proto3";

package business;

service BusinessOpportunity {
    rpc create(CreateBusinessOpportunityParams) returns (BusinessOpportunityResult) {};
    rpc update(UpdateBusinessOpportunityParams) returns (BusinessOpportunityResult) {};
}

message CreateBusinessOpportunityParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional string code = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message UpdateBusinessOpportunityParams {
    int32 id = 1;
    optional int32 salesTeamGroupId = 2;
    optional string name = 3;
    optional int32 viewOrder = 4;
    optional string code = 5;
    int32 updatedUserId = 6;
}

message BusinessOpportunityData {
    int32 id = 1;
}

message BusinessOpportunityResult {
    bool success = 1;
    message Result {
        BusinessOpportunityData data = 1;
    }
    optional Result result = 2;
}