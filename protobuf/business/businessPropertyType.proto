syntax = "proto3";

package business;

service BusinessPropertyType {
    rpc create(CreateBusinessPropertyTypeParams) returns (BusinessPropertyTypeResult) {};
    rpc update(UpdateBusinessPropertyTypeParams) returns (BusinessPropertyTypeResult) {};
}

message CreateBusinessPropertyTypeParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateBusinessPropertyTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message BusinessPropertyTypeData {
    int32 id = 1;
}

message BusinessPropertyTypeResult {
    bool success = 1;
    message Result {
        BusinessPropertyTypeData data = 1;
    }
    optional Result result = 2;
}