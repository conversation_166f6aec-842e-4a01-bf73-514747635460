syntax = "proto3";

package business;

service BusinessMemo {
    rpc create(CreateBusinessMemoParams) returns (BusinessMemoResult) {};
    rpc update(UpdateBusinessMemoParams) returns (BusinessMemoResult) {};
    rpc delete(DeleteBusinessMemoParams) returns (BusinessMemoResult) {};
}

message CreateBusinessMemoParams {
    optional string title = 1;
    optional string content = 2;
    optional int32 businessId = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateBusinessMemoParams {
    int32 id = 1;
    optional string title = 2;
    optional string content = 3;
    optional int32 businessId = 4;
    int32 updatedUserId = 5;
}

message BusinessMemoData {
    int32 id = 1;
}

message BusinessMemoResult {
    bool success = 1;
    message Result {
        BusinessMemoData data = 1;
    }
    optional Result result = 2;
}

message DeleteBusinessMemoParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}