syntax = "proto3";

package business;

service BusinessType {
    rpc create(CreateBusinessTypeParams) returns (BusinessTypeResult) {};
    rpc update(UpdateBusinessTypeParams) returns (BusinessTypeResult) {};
}

message CreateBusinessTypeParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 salesTeamGroupId = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateBusinessTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message BusinessTypeData {
    int32 id = 1;
}

message BusinessTypeResult {
    bool success = 1;
    message Result {
        BusinessTypeData data = 1;
    }
    optional Result result = 2;
}