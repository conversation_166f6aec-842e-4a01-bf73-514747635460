syntax = "proto3";

package business;

service BusinessProduct {
    rpc create(CreateBusinessProductParams) returns (BusinessProductResult) {};
    rpc update(UpdateBusinessProductParams) returns (BusinessProductResult) {};
}

message CreateBusinessProductParams {
    optional int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 typeId = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message UpdateBusinessProductParams {
    int32 id = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional int32 typeId = 4;
    int32 updatedUserId = 5;
}

message BusinessProductData {
    int32 id = 1;
}

message BusinessProductResult {
    bool success = 1;
    message Result {
        BusinessProductData data = 1;
    }
    optional Result result = 2;
}