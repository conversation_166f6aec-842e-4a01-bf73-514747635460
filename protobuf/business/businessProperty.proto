syntax = "proto3";

package business;

service BusinessProperty {
    rpc create(CreateBusinessPropertyParams) returns (BusinessPropertyResult) {};
    rpc update(UpdateBusinessPropertyParams) returns (BusinessPropertyResult) {};
}

message CreateBusinessPropertyParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    int32 typeId = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message UpdateBusinessPropertyParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    optional int32 typeId = 4;
    int32 updatedUserId = 5;
}

message BusinessPropertyData {
    int32 id = 1;
}

message BusinessPropertyResult {
    bool success = 1;
    message Result {
        BusinessPropertyData data = 1;
    }
    optional Result result = 2;
}