syntax = "proto3";

package business;

service BusinessStatus {
    rpc create(CreateBusinessStatusParams) returns (BusinessStatusResult) {};
    rpc update(UpdateBusinessStatusParams) returns (BusinessStatusResult) {};
}

message CreateBusinessStatusParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional string type = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message UpdateBusinessStatusParams {
    int32 id = 1;
    string name = 2;
    optional int32 viewOrder = 3;
    optional string type = 4;
    int32 updatedUserId = 5;
}

message BusinessStatusData {
    int32 id = 1;
}

message BusinessStatusResult {
    bool success = 1;
    message Result {
        BusinessStatusData data = 1;
    }
    optional Result result = 2;
}