syntax = "proto3";

package weeklyWorkReport;

import "google/protobuf/timestamp.proto";

service WeeklyWorkReport {
    rpc create(CreateWeeklyWorkReportParams) returns (WeeklyWorkReportResult) {};
    rpc update(UpdateWeeklyWorkReportParams) returns (WeeklyWorkReportResult) {};
    rpc delete(DeleteWeeklyWorkReportParams) returns (WeeklyWorkReportResult) {};
    rpc bulkUpdate(BulkUpdateWeeklyWorkReportParams) returns (BulkWeeklyWorkReportResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message CreateWeeklyWorkReportParams {
    int32 regionId = 1;
    optional int32 salesTeamId = 2;
    optional int32 salesTeamUnitId = 3;
    optional int32 salesTeamUserId = 4;
    optional int32 weeklyRecordsId = 5;
    optional string fillState = 6;
    optional string state = 7;
    optional string notes = 8;
    optional string thisWeekWorkDescription = 9;
    optional string nextWeekWorkDescription = 10;
    repeated int32 thisWeekVisitIds = 11;
    repeated int32 nextWeekVisitIds = 12;
    repeated AttachmentParams attachments = 13;
    int32 createdUserId = 14;
}

message BulkUpdateWeeklyWorkReportParams {
    repeated UpdateWeeklyWorkReportParams inputs = 1;
}

message UpdateWeeklyWorkReportParams {
    int32 id = 1;
    optional string fillState = 2;
    optional string state = 3;
    optional string notes = 4;
    optional string thisWeekWorkDescription = 5;
    optional string nextWeekWorkDescription = 6;
    optional string rejectedReason = 7;
    repeated int32 thisWeekVisitIds = 8;
    repeated int32 nextWeekVisitIds = 9;
    int32 updatedUserId = 10;
}


message DeleteWeeklyWorkReportParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message WeeklyWorkReportData {
    int32 id = 1;
}

message WeeklyWorkReportResult {
    bool success = 1;
    message Result {
        WeeklyWorkReportData data = 1;
    }
    optional Result result = 2;
}

message BulkWeeklyWorkReportData{ 
   repeated int32 ids = 1;
}

message BulkWeeklyWorkReportResult {
    bool success = 1;
    message Result {
        BulkWeeklyWorkReportData data = 1;
    }
    optional Result result = 2;
}
