syntax = "proto3";

package weeklyWorkReportAttachment;

service WeeklyWorkReportAttachment {
    rpc create(CreateWeeklyWorkReportAttachmentParams) returns (WeeklyWorkReportAttachmentUpdateResult) {};
    rpc bulkCreate(BulkCreateWeeklyWorkReportAttachmentParams) returns (WeeklyWorkReportBulkAttachmentCreateResult) {};
    rpc update(UpdateWeeklyWorkReportAttachmentParams) returns (WeeklyWorkReportAttachmentUpdateResult) {};
    rpc delete(DeleteWeeklyWorkReportAttachmentParams) returns (WeeklyWorkReportAttachmentUpdateResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}


message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message CreateWeeklyWorkReportAttachmentParams {
    int32 weeklyWorkReportId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}

message BulkCreateWeeklyWorkReportAttachmentParams {
    int32 weeklyWorkReportId = 1;
    repeated AttachmentParams attachments = 2;
    int32 createdUserId = 3;
}


message UpdateWeeklyWorkReportAttachmentParams {
    int32 id = 1;
    optional string name= 2;
    optional string memo= 3;
    int32 updatedUserId = 4;
}

message DeleteWeeklyWorkReportAttachmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message AttachmentData {
    repeated int32 ids = 1;
}

message WeeklyWorkReportAttachmentData {
    int32 id = 1;
}

message WeeklyWorkReportAttachmentUpdateResult {
    bool success = 1;
    message Result {
        WeeklyWorkReportAttachmentData data = 1;
    }
    optional Result result = 2;
}

message WeeklyWorkReportBulkAttachmentCreateResult {
    bool success = 1;
    message Result {
        AttachmentData data = 1;
    }
    optional Result result = 2;
}