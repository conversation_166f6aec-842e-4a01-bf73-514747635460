syntax = "proto3";

package weeklyRecord;

import "google/protobuf/timestamp.proto";

service WeeklyRecord {
    rpc create(CreateWeeklyRecordParams) returns (WeeklyRecordResult) {};
}

message CreateWeeklyRecordParams {
    google.protobuf.Timestamp weekStartDate = 1;
    google.protobuf.Timestamp weekEndDate = 2;
}



message WeeklyRecordData {
    int32 id = 1;
}

message WeeklyRecordResult {
    bool success = 1;
    message Result {
        WeeklyRecordData data = 1;
    }
    optional Result result = 2;
}