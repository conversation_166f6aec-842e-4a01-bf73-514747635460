syntax = "proto3";

package customer;

service CustomerProperty {
    rpc create(CreateCustomerPropertyParams) returns (CustomerPropertyResult) {};
    rpc update(UpdateCustomerPropertyParams) returns (CustomerPropertyResult) {};
}

message CreateCustomerPropertyParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 salesTeamGroupId = 3;
    int32 typeId = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message UpdateCustomerPropertyParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    optional int32 salesTeamGroupId = 4;
    optional int32 typeId = 5;
    int32 updatedUserId = 6;
}

message CustomerPropertyData {
    int32 id = 1;
}

message CustomerPropertyResult {
    bool success = 1;
    message Result {
        CustomerPropertyData data = 1;
    }
    optional Result result = 2;
}