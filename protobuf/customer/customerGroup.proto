syntax = "proto3";

package customer;

service CustomerGroup {
    rpc create(CreateCustomerGroupParams) returns (CustomerGroupResult) {};
    rpc update(UpdateCustomerGroupParams) returns (CustomerGroupResult) {};
}

message CreateCustomerGroupParams {
    optional int32 salesTeamGroupId = 1;
    optional string name = 2;
    optional int32 regionId = 3;
    int32 createdUserId = 4;
}

message UpdateCustomerGroupParams {
    int32 id = 1;
    optional int32 salesTeamGroupId = 2;
    optional string name = 3;
    int32 updatedUserId = 4;
}

message CustomerGroupData {
    int32 id = 1;
}

message CustomerGroupResult {
    bool success = 1;
    message Result {
        CustomerGroupData data = 1;
    }
    optional Result result = 2;
}