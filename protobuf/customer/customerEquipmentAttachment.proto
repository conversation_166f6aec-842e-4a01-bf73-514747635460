syntax = "proto3";

package customerEquipmentAttachment;

service CustomerEquipmentAttachment {
    rpc create(CreateCustomerEquipmentAttachmentParams) returns (CustomerEquipmentAttachmentResult) {};
    rpc update(UpdateCustomerEquipmentAttachmentParams) returns (CustomerEquipmentAttachmentResult) {};
    rpc delete(DeleteCustomerEquipmentAttachmentParams) returns (CustomerEquipmentAttachmentResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message CreateCustomerEquipmentAttachmentParams {
    int32 equipmentId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}

message UpdateCustomerEquipmentAttachmentParams {
    int32 id = 1;
    optional string name= 2;
    optional string memo= 3;
    int32 updatedUserId = 4;
}

message DeleteCustomerEquipmentAttachmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message CustomerEquipmentAttachmentData {
    int32 id = 1;
}

message CustomerEquipmentAttachmentResult {
    bool success = 1;
    message Result {
        CustomerEquipmentAttachmentData data = 1;
    }
    optional Result result = 2;
}