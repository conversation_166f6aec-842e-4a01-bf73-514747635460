syntax = "proto3";

package customer;

service CustomerPropertyType {
    rpc create(CreateCustomerPropertyTypeParams) returns (CustomerPropertyTypeResult) {};
    rpc update(UpdateCustomerPropertyTypeParams) returns (CustomerPropertyTypeResult) {};
}

message CreateCustomerPropertyTypeParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 salesTeamGroupId = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateCustomerPropertyTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    optional int32 salesTeamGroupId = 4;
    int32 updatedUserId = 5;
}

message CustomerPropertyTypeData {
    int32 id = 1;
}

message CustomerPropertyTypeResult {
    bool success = 1;
    message Result {
        CustomerPropertyTypeData data = 1;
    }
    optional Result result = 2;
}