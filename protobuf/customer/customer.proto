syntax = "proto3";

package customer;

service Customer {
    rpc create(CreateCustomerParams) returns (CustomerResult) {};
    rpc update(UpdateCustomerParams) returns (CustomerResult) {};
    rpc delete(DeleteCustomerParams) returns (CustomerResult) {};
    rpc createContactPersonRelations(CreateContactPersonRelationParams) returns (CustomerResult) {};
    rpc deleteContactPersonRelations(DeleteContactPersonRelationParams) returns (CustomerResult) {};
}

message CreateCustomerParams {    
    int32 salesTeamGroupId = 1;
    optional int32 groupId = 2;
    optional int32 typeId = 3;
    optional int32 categoryId = 4;
    optional int32 areaId = 5;
    optional string businessCode = 6;
    optional string medicalCode = 7;
    optional string referenceCode = 8;
    optional string name = 9;
    optional string shortName = 10;
    optional string phone = 11;
    optional string mobile = 12;
    optional string email = 13;
    optional string fax = 14;
    optional int32 cityId = 15;
    optional int32 districtId = 16;
    optional string address = 17;
    optional string website = 18;
    optional string memo = 19;
    optional bool isGmp = 20;
    optional int32 parentId = 21;
    optional string creditQuota = 22;
    optional int32 creditPeriodId = 23;
    optional string shippingAddress = 24;
    optional string contactPersonName = 25;
    optional string contactPersonPhone = 26;
    optional string bankAccountCode = 27;
    optional string bankAccountName = 28;
    optional string legalPersonName = 29;
    optional string billingUnitName = 30;
    optional int32 provinceId = 31;
    optional int32 defaultPaymentMethodId = 32;
    optional int32 defaultShippingMethodId = 33;
    optional string navCode = 34;

    // relations
    repeated int32 propertyIds = 35;
    repeated int32 contactPersonIds = 36;
    message PrimaryUserParams {
        int32 userId = 1;
        int32 salesTeamId = 2;
    }
    repeated PrimaryUserParams primaryUsers = 37;
    repeated int32 salesTeamUnitIds = 38;
    int32 regionId = 39;
    int32 createdUserId = 40;
    optional int32 classification = 41;
}

message UpdateCustomerParams {
    int32 id = 1;
    optional int32 salesTeamGroupId = 2;
    optional int32 groupId = 3;
    optional int32 typeId = 4;
    optional int32 categoryId = 5;
    optional int32 areaId = 6;
    optional string businessCode = 7;
    optional string medicalCode = 8;
    optional string referenceCode = 9;
    optional string name = 10;
    optional string shortName = 11;
    optional string phone = 12;
    optional string mobile = 13;
    optional string email = 14;
    optional string fax = 15;
    optional int32 cityId = 16;
    optional int32 districtId = 17;
    optional string address = 18;
    optional string website = 19;
    optional string memo = 20;
    optional bool isGmp = 21;
    optional int32 parentId = 22;
    optional string creditQuota = 23;
    optional int32 creditPeriodId = 24;
    optional string shippingAddress = 25;
    optional string contactPersonName = 26;
    optional string contactPersonPhone = 27;
    optional string bankAccountCode = 28;
    optional string bankAccountName = 29;
    optional string legalPersonName = 30;
    optional string billingUnitName = 31;
    optional int32 provinceId = 32;
    optional int32 defaultPaymentMethodId = 33;
    optional int32 defaultShippingMethodId = 34;
    optional string navCode = 35;

    // relations
    repeated int32 propertyIds = 36;
    repeated int32 contactPersonIds = 37;
    message PrimaryUserParams {
        int32 userId = 1;
        int32 salesTeamId = 2;
    }
    repeated PrimaryUserParams primaryUsers = 38;
    repeated int32 salesTeamUnitIds = 39;
    int32 updatedUserId = 40;
    optional int32 classification = 41;
}

message DeleteCustomerParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message CreateContactPersonRelationParams {
    int32 id = 1;
    repeated int32 contactPersonIds = 2;
}

message DeleteContactPersonRelationParams {
    int32 id = 1;
    repeated int32 contactPersonIds = 2;
}

message CustomerData {
    int32 id = 1;
}

message CustomerResult {
    bool success = 1;
    message Result {
        CustomerData data = 1;
    }
    optional Result result = 2;
}

message InstanceData {
    int32 bpmInstanceId = 1;
}
