syntax = "proto3";

package customerCertificateAttachment;

service CustomerCertificateAttachment {
    rpc create(CreateCustomerCertificateAttachmentParams) returns (CustomerCertificateAttachmentResult) {};
    rpc update(UpdateCustomerCertificateAttachmentParams) returns (CustomerCertificateAttachmentResult) {};
    rpc delete(DeleteCustomerCertificateAttachmentParams) returns (CustomerCertificateAttachmentResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message CreateCustomerCertificateAttachmentParams {
    int32 certificateId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}

message UpdateCustomerCertificateAttachmentParams {
    int32 id = 1;
    optional string name= 2;
    optional string memo= 3;
    int32 updatedUserId = 4;
}

message DeleteCustomerCertificateAttachmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message CustomerCertificateAttachmentData {
    int32 id = 1;
}

message CustomerCertificateAttachmentResult {
    bool success = 1;
    message Result {
        CustomerCertificateAttachmentData data = 1;
    }
    optional Result result = 2;
}