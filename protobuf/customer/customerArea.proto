syntax = "proto3";

package customer;

service CustomerArea {
    rpc create(CreateCustomerAreaParams) returns (CustomerAreaResult) {};
    rpc update(UpdateCustomerAreaParams) returns (CustomerAreaResult) {};
}

message CreateCustomerAreaParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 salesTeamGroupId = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateCustomerAreaParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    optional int32 salesTeamGroupId = 4;
    int32 updatedUserId = 5;
}

message CustomerAreaData {
    int32 id = 1;
}

message CustomerAreaResult {
    bool success = 1;
    message Result {
        CustomerAreaData data = 1;
    }
    optional Result result = 2;
}