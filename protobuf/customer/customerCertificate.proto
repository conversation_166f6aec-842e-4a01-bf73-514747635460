syntax = "proto3";

package customerCertificate;

import "google/protobuf/timestamp.proto";

service CustomerCertificate {
    rpc create(CreateCustomerCertificateParams) returns (CustomerCertificateResult) {};
    rpc update(UpdateCustomerCertificateParams) returns (CustomerCertificateResult) {};
    rpc delete(DeleteCustomerCertificateParams) returns (CustomerCertificateResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message CreateCustomerCertificateParams {
    int32 customerId = 1;
    optional int32 typeId = 2;
    optional string code = 3;
    optional string scope = 4;
    optional google.protobuf.Timestamp effectiveDate = 5;
    optional google.protobuf.Timestamp expiryDate = 6;
    repeated AttachmentParams attachments = 7;
    int32 createdUserId = 8;
}

message UpdateCustomerCertificateParams {
    int32 id = 1;
    optional int32 typeId = 2;
    optional string code = 3;
    optional string scope = 4;
    optional google.protobuf.Timestamp effectiveDate = 5;
    optional google.protobuf.Timestamp expiryDate = 6;
    int32 updatedUserId = 7;
}

message DeleteCustomerCertificateParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message CustomerCertificateData {
    int32 id = 1;
}

message CustomerCertificateResult {
    bool success = 1;
    message Result {
        CustomerCertificateData data = 1;
    }
    optional Result result = 2;
}
