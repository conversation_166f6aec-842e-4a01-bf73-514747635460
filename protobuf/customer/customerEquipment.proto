syntax = "proto3";

package customerEquipment;

import "google/protobuf/timestamp.proto";

service CustomerEquipment {
    rpc create(CreateCustomerEquipmentParams) returns (CustomerEquipmentResult) {};
    rpc update(UpdateCustomerEquipmentParams) returns (CustomerEquipmentResult) {};
    rpc delete(DeleteCustomerEquipmentParams) returns (CustomerEquipmentResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message CreateCustomerEquipmentParams {
    int32 customerId = 1;
    optional int32 competitorId = 2;
    optional int32 manufactureYear = 3;
    optional int32 competitorPrice = 4;
    optional string notes = 5;
    repeated AttachmentParams attachments = 6;
    int32 createdUserId = 7;
}

message UpdateCustomerEquipmentParams {
    int32 id = 1;
    optional int32 competitorId = 2;
    optional int32 manufactureYear = 3;
    optional int32 competitorPrice = 4;
    optional string notes = 5;
    int32 updatedUserId = 6;
}

message DeleteCustomerEquipmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message CustomerEquipmentData {
    int32 id = 1;
}

message CustomerEquipmentResult {
    bool success = 1;
    message Result {
        CustomerEquipmentData data = 1;
    }
    optional Result result = 2;
}
