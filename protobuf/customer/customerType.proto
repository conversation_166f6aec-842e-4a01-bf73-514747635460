syntax = "proto3";

package customer;

service CustomerType {
    rpc create(CreateCustomerTypeParams) returns (CustomerTypeResult) {};
    rpc update(UpdateCustomerTypeParams) returns (CustomerTypeResult) {};
}

message CreateCustomerTypeParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 salesTeamGroupId = 3;
    optional int32 regionId = 4;
    int32 createdUserId = 5;
}

message UpdateCustomerTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    optional int32 salesTeamGroupId = 4;
    int32 updatedUserId = 5;
}

message CustomerTypeData {
    int32 id = 1;
}

message CustomerTypeResult {
    bool success = 1;
    message Result {
        CustomerTypeData data = 1;
    }
    optional Result result = 2;
}