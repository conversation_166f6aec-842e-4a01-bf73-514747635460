syntax = "proto3";

package customerAttachment;

service CustomerAttachment {
    rpc bulkCreate(BulkCreateCustomerAttachmentParams) returns (CustomerBulkAttachmentCreateResult) {};
    rpc update(UpdateCustomerAttachmentParams) returns (CustomerAttachmentUpdateResult) {};
    rpc delete(DeleteCustomerAttachmentParams) returns (CustomerAttachmentUpdateResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message BulkCreateCustomerAttachmentParams {
    int32 customerId = 1;
    repeated AttachmentParams attachments = 2;
    int32 createdUserId = 3;
}

message UpdateCustomerAttachmentParams {
    int32 id = 1;
    optional string name= 2;
    optional string memo= 3;
    int32 updatedUserId = 4;
}

message DeleteCustomerAttachmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message AttachmentData {
    repeated int32 ids = 1;
}

message CustomerBulkAttachmentCreateResult {
    bool success = 1;
    message Result {
        AttachmentData data = 1;
    }
    optional Result result = 2;
}

message CustomerAttachmentData {
    int32 id = 1;
}

message CustomerAttachmentUpdateResult {
    bool success = 1;
    message Result {
        CustomerAttachmentData data = 1;
    }
    optional Result result = 2;
}