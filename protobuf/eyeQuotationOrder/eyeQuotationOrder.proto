syntax = "proto3";

package eyeQuotationOrder;

import "google/protobuf/timestamp.proto";

service EyeQuotationOrder {
    rpc create(CreateEyeQuotationOrderParams) returns (EyeQuotationOrderResult) {};
    rpc requestOfficialSeal(RequestEyeQuotationOrderOfficialSealParams) returns (RequestResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}

message EyeQuotationOrderProductItemWarrantyPrice {
    string internalWarrantyPrice = 1;
    int32 internalWarrantyCurrencyId = 2;
}

message EyeQuotationOrderProductEyeProductItem {
    int32 id = 1;
    int32 qty = 2;
    optional int32 warrantyMonths = 3;
    optional int32 warrantyPeriodTypeId = 4;
    optional EyeQuotationOrderProductItemWarrantyPrice eyeQuotationOrderProductItemWarrantyPrice = 5;
}

message EyeQuotationOrderProduct {
    int32 eyeProductId = 1;
    repeated EyeQuotationOrderProductEyeProductItem eyeProductItems = 2;
    int32 qty = 3;
    optional int32 exchangeRateId = 4;
    string customQuotationPrice = 5;
}

message EyeQuotationOrderPromotion {
    int32 eyePromotionId = 1;
    repeated int32 eyePromotionAddonProductItemIds = 2;
    optional int32 exchangeRateId = 3;
}

message EyeQuotationOrderProductItem {
    int32 materialId = 1;
    int32 qty = 2;
    optional int32 exchangeRateId = 3;
    optional string customQuotationPrice = 4;
    optional int32 customersConsumablePriceId = 5;
}

message EyeQuotationOrderWarrantyItem {
    int32 materialId = 1;
    string sn = 2;
    string udi = 3;
    int32 qty = 4;
}

message EyeQuotationOrderCommissionAmount {
    string commissionAmount = 1;
    optional int32 eyeQuotationOrderCommissionTypeId = 2;
}

message CreateEyeQuotationOrderParams {
    int32 regionId = 1;
    optional int32 eyeServiceOrderId = 2;
    int32 invoicingCustomerId = 3;
    int32 customerId = 4;
    optional string contactPerson = 5;
    optional string contactPhone = 6;
    optional int32 provinceId = 7;
    optional int32 cityId = 8;
    optional int32 districtId = 9;
    optional string address = 10;
    int32 deptId = 11;
    int32 userId = 12;
    int32 currencyId = 13;
    string standardAmount = 14;
    string localStandardAmount = 15;
    string discountRate = 16;
    string discountAmount = 17;
    string extraDiscountAmount = 18;
    string realAmount = 19;
    string recommendedAmount = 20;
    string realDiscountAmount = 21;
    string realDiscountRate = 22;
    int32 taxRateId = 23;
    string untaxedAmount = 24;
    string commissionAmount = 25;
    string discountRateWithoutCommission = 26;
    int32 creditPeriodId = 27;
    google.protobuf.Timestamp expectDeliveryDate = 28;
    optional string description = 29;
    optional string biddingPrice = 30;
    optional int32 eyeQuotationOrderTypeId = 31;
    optional bool onlyMaterials = 32;
    // 保固合約
    int32 warrantyBuyType = 33;
    // 商機
    repeated int32 businessIds = 34;
    // 眼科商品
    repeated EyeQuotationOrderProduct eyeQuotationOrderProducts = 35;
    // 眼科優惠
    repeated EyeQuotationOrderPromotion eyeQuotationOrderPromotions = 36;
    // 單選料號
    repeated EyeQuotationOrderProductItem eyeQuotationOrderProductItems = 37;
    // 保固品項
    repeated EyeQuotationOrderWarrantyItem eyeQuotationOrderWarrantyItems = 38;
    int32 createdUserId = 39;
    int32 companyId = 40;
    optional int32 costCenterId = 41;
    optional string commissionRate = 42;
    optional int32 financialCompanyId = 43;
    optional int32 eyeQuotationOrderCommissionTypeId = 44;
    // 研究費
    repeated EyeQuotationOrderCommissionAmount eyeQuotationOrderCommissionAmounts = 45;
    optional int32 warehouseId = 46;
    optional int32 salesTeamUnitId = 47;
    optional string paymentInfo = 48;
    // 附件
    repeated AttachmentParams attachFiles = 49;
}

message EyeQuotationOrderData {
    int32 id = 1;
}

message EyeQuotationOrderResult {
    bool success = 1;
    message Result {
        EyeQuotationOrderData data = 1;
    }
    optional Result result = 2;
}

message RequestEyeQuotationOrderOfficialSealParams {
    int32 id = 1;
    int32 createdUserId = 2;
}

message RequestResult {
    bool success = 1;
}