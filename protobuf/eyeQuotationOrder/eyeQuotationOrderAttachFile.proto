syntax = "proto3";

package eyeQuotationOrderAttachFile;

service EyeQuotationOrderAttachFile {
    rpc create(CreateEyeQuotationOrderAttachFileParams) returns (RequestResult) {};
    rpc delete(DeleteEyeQuotationOrderAttachFileParams) returns (RequestResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message CreateEyeQuotationOrderAttachFileParams {
    int32 eyeQuotationOrderId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    optional int32 regionId = 5;
    int32 createdUserId = 6;
}

message DeleteEyeQuotationOrderAttachFileParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message RequestResult {
    bool success = 1;
}