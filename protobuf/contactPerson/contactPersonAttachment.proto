syntax = "proto3";

package contactPersonAttachment;

service ContactPersonAttachment {
    rpc bulkCreate(BulkCreateContactPersonAttachmentParams) returns (ContactPersonBulkAttachmentCreateResult) {};
    rpc create(CreateContactPersonAttachmentParams) returns (ContactPersonAttachmentUpdateResult) {};
    rpc update(UpdateContactPersonAttachmentParams) returns (ContactPersonAttachmentUpdateResult) {};
    rpc delete(DeleteContactPersonAttachmentParams) returns (ContactPersonAttachmentUpdateResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}


message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message BulkCreateContactPersonAttachmentParams {
    int32 contactPeopleId = 1;
    repeated AttachmentParams attachments = 2;
    int32 createdUserId = 3;
}

message CreateContactPersonAttachmentParams {
    int32 contactPeopleId = 1;
    FileInfo file = 2;
    string name = 3;
    optional string memo = 4;
    int32 createdUserId = 5;
}


message UpdateContactPersonAttachmentParams {
    int32 id = 1;
    optional string name= 2;
    optional string memo= 3;
    int32 updatedUserId = 4;
}

message DeleteContactPersonAttachmentParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message AttachmentData {
    repeated int32 ids = 1;
}

message ContactPersonBulkAttachmentCreateResult {
    bool success = 1;
    message Result {
        AttachmentData data = 1;
    }
    optional Result result = 2;
}

message ContactPersonAttachmentData {
    int32 id = 1;
}

message ContactPersonAttachmentUpdateResult {
    bool success = 1;
    message Result {
        ContactPersonAttachmentData data = 1;
    }
    optional Result result = 2;
}