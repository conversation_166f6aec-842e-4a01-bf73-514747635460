syntax = "proto3";

package contactPersonType;

service ContactPersonType {
    rpc create(CreateContactPersonTypeParams) returns (ContactPersonTypeResult) {};
    rpc update(UpdateContactPersonTypeParams) returns (ContactPersonTypeResult) {};
    rpc delete(DeleteContactPersonParams) returns (ContactPersonTypeResult){}
}

message CreateContactPersonTypeParams {
    string name = 1;
    optional int32 viewOrder = 2;
    int32 createdUserId = 3;
}

message UpdateContactPersonTypeParams {
    int32 id = 1;
    optional string name = 2;
    optional int32 viewOrder = 3;
    int32 updatedUserId = 4;
}

message ContactPersonTypeData {
    int32 id = 1;
}

message ContactPersonTypeResult {
    bool success = 1;
    message Result {
        ContactPersonTypeData data = 1;
    }
    optional Result result = 2;
}

message DeleteContactPersonTypeParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}
