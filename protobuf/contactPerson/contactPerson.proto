syntax = "proto3";

package contactPerson;

service ContactPerson {
    rpc create(CreateContactPersonParams) returns (ContactPersonResult) {};
    rpc update(UpdateContactPersonParams) returns (ContactPersonResult) {};
    rpc delete(DeleteContactPersonParams) returns (ContactPersonResult) {};
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message AttachmentParams {
    FileInfo file = 1;
    string name = 2;
    optional string memo = 3;
}


message CreateContactPersonParams {
    int32 salesTeamGroupId = 1;
    string name = 2;
    optional string nickname = 3;
    optional string gender = 4;
    optional string jobTitle = 5;
    optional string doctorCode = 6;
    optional int32 departmentId = 7;
    optional string phone = 8;
    optional string mobile = 9;
    optional string email = 10;
    optional string fax = 11;
    optional string address = 12;
    optional string website = 13;
    optional string hobby = 14;
    optional string skill = 15;
    optional string memo = 16;
    repeated int32 salesTeamUnitIds = 17;
    repeated int32 primaryUserIds = 18;
    repeated int32 customerIds = 19;
    optional int32 regionId = 20;
    int32 createdUserId = 21;
    optional string citizenCode = 22;
    int32 typeId = 23;
    optional string bankBranch = 24;
    optional string bankCardNumber = 25;
    repeated AttachmentParams attachments = 26;
}

message UpdateContactPersonParams {
    int32 id = 1;
    optional int32 salesTeamGroupId = 2;
    optional string name = 3;
    optional string nickname = 4;
    optional string gender = 5;
    optional string jobTitle = 6;
    optional string doctorCode = 7;
    optional int32 departmentId = 8;
    optional string phone = 9;
    optional string mobile = 10;
    optional string email = 11;
    optional string fax = 12;
    optional string address = 13;
    optional string website = 14;
    optional string hobby = 15;
    optional string skill = 16;
    optional string memo = 17;
    repeated int32 salesTeamUnitIds = 18;
    repeated int32 primaryUserIds = 19;
    repeated int32 customerIds = 20;
    int32 updatedUserId = 21;
    optional string citizenCode = 22;
    optional int32 typeId = 23;
    optional string bankBranch = 24;
    optional string bankCardNumber = 25;
    repeated AttachmentParams attachments = 26;
}

message DeleteContactPersonParams {
    int32 id = 1;
    int32 deletedUserId = 2;
}

message ContactPersonData {
    int32 id = 1;
}

message ContactPersonResult {
    bool success = 1;
    message Result {
        ContactPersonData data = 1;
    }
    optional Result result = 2;
}
