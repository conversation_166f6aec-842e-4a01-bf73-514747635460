import { Inject, Service } from 'typedi';
import { FindOptionsWhere } from 'typeorm';
import { AccessKey } from '@clinico/typeorm-persistence/models/public/accessKey.model';
import { SearchParams } from '../types/accessKey.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { MetadataStorage } from '@/common/metadata/metadataStorage';
import { AccessKeyMetadata } from '@/common/metadata/metadataStorage.type';

@Service()
export class AccessKeyService {
    @Inject('MetadataStorage')
    private metadataStorage: MetadataStorage;

    private accessKeyRepo = ClinicoDataSource.getRepository(AccessKey);

    async search(params: SearchParams): Promise<AccessKey[]> {
        const filters: FindOptionsWhere<AccessKey> = {};
        if (params.name) {
            filters.name = params.name;
        }
        if (params.key) {
            filters.key = params.key;
        }
        filters.deleted = false;

        const result = await this.accessKeyRepo.find({
            where: filters,
        });
        return result;
    }

    async saveAllAccesskeysToMetadataStorage(): Promise<void> {
        const accessKeys = await this.search({});
        for (const accessKey of accessKeys) {
            this.metadataStorage.collectAccessKeyMetadata(accessKey);
        }
    }

    async findOneByMetadataStorage(
        key: string,
    ): Promise<AccessKeyMetadata | null> {
        return (
            this.metadataStorage.accessKeyMetadatas.find(
                (el) => el.key === key,
            ) ?? null
        );
    }
}
