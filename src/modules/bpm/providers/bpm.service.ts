import { Service } from 'typedi';
import { bpmAxios } from '@/common/helpers/bpm.axios.helper';
import { CreateFormInstanceParams, FormInstanceData } from '../types/bpm.type';

@Service()
export class BPMService {
    async createFormInstance(
        params: CreateFormInstanceParams,
    ): Promise<FormInstanceData> {
        const endpoint = `/internal/form/instances`;
        const { data } = await bpmAxios.post<any>(endpoint, params);

        return data.result.data;
    }
}
