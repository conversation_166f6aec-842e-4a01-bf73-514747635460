import { Service } from 'typedi';
import { bpmAxios } from '@/common/helpers/bpm.axios.helper';
import { CreateFormInstanceParams, FormInstanceData } from '../types/bpm.type';

type UploadFile = {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    buffer: Buffer;
    sizs: number;
};

@Service()
export class BPMService {
    async createFormInstance(
        params: CreateFormInstanceParams,
    ): Promise<FormInstanceData> {
        // 如果有檔案需要上傳，先上傳檔案
        if (params.files && params.files.length > 0) {
            const FormData = require('form-data');
            const formData = new FormData();

            // 將每個檔案添加到 FormData
            params.files.forEach((file) => {
                formData.append('files', file.content, {
                    filename: file.filename,
                    contentType: file.mimetype,
                });
            });

            try {
                const uploadEndpoint = `/attachment/upload`;
                const uploadResponse = await bpmAxios.post(uploadEndpoint, formData, {
                    headers: {
                        ...formData.getHeaders(),
                    },
                });
                console.log('檔案上傳成功:', uploadResponse.data);
            } catch (error) {
                console.error('檔案上傳失敗:', error);
                throw error;
            }
        }

        // 創建表單實例
        const endpoint = `/internal/form/instances`;
        const { data } = await bpmAxios.post<any>(endpoint, params);

        return data.result.data;
    }
}
