import { Service } from 'typedi';
import { bpmAxios } from '@/common/helpers/bpm.axios.helper';
import { CreateFormInstanceParams, FormInstanceData } from '../types/bpm.type';

type UploadFile = {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    buffer: Buffer;
    sizs: number;
};

@Service()
export class BPMService {
    async createFormInstance(
        params: CreateFormInstanceParams,
    ): Promise<FormInstanceData> {
        // const endpoint = `/internal/form/instances`;
        // const { data } = await bpmAxios.post<any>(endpoint, params);
        const endpoint = `/attachment/upload`;
        const { data } = await bpmAxios.post<any>(endpoint, 
            params.files.map((file) => ({
                fieldname: 'test',
                originalname: 'test.txt',
                encoding: file.encoding,
                mimetype: file.mimetype,
                buffer: file.content,
                sizs: file.content.length,
            })),

        );

        return data.result.data;
    }
}
