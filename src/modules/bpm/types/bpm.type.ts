import { ReadStream } from 'fs-capacitor';

export enum EnumBPMForm {
    QuotationOrderCN = 'CN_Quotation',
    QuotationOrderTW = 'TW_Quotation',
    FixedAssetRentalCN = 'CN_FixedAssetRental',
    FixedAssetRentalTW = 'TW_FixedAssetRental',
    FixedAssetRentalExtendCN = 'CN_FixedAssetRentalExtend',
    FixedAssetRentalExtendTW = 'TW_FixedAssetRentalExtend',
    QuotationOfficialSealCN = 'CN_QuotationOfficialSeal',
    QuotationOfficialSealTW = 'TW_QuotationOfficialSeal',
    ServiceOrderCN = 'CN_ServiceOrder',
    ServiceOrderTW = 'TW_ServiceOrder',
    FixedAssetRentalUpdateCN = 'CN_FixedAssetRentalUpdate',
    FixedAssetRentalUpdateTW = 'TW_FixedAssetRentalUpdate',
    MarketActivityCN = 'CN_MarketActivity',
    MarketActivityTW = 'TW_MarketActivity',
    DistributorAuthorizationTW = 'TW_DistributorAuthorization',
    DistributorAuthorizationCN = 'CN_DistributorAuthorization',
    ExhibitionConsumptionCN = 'CN_ExhibitionConsumption', //展示耗用
    ExhibitionConsumptionTW = 'TW_ExhibitionConsumption', //展示耗用
    ScrapCN = 'CN_Scrap', //存貨報廢
    ScrapTW = 'TW_Scrap', //存貨報廢
    RequisitionCN = 'CN_Requisition', //國內請購
    RequisitionTW = 'TW_Requisition', //國內請購
    ForeignRequisitionCN = 'CN_ForeignRequisition', //國外請購
    ForeignRequisitionTW = 'TW_ForeignRequisition', //國外請購
    PurchaseCN = 'CN_Purchase', //採購
    PurchaseTW = 'TW_Purchase', //採購
    InventoryToFixedAssetsCN = 'CN_InventoryToFixedAssets', //庫存轉固定資產
    InventoryToFixedAssetsTW = 'TW_InventoryToFixedAssets', //庫存轉固定資產
}

export enum EnumInstanceStatus {
    Waiting = 0,
    Completed = 1,
    Canceled = 2,
    Rejected = 3,
    Reconsidered = 4,
}

export type CreateFormInstanceParams = {
    formCode: string;

    values?: {
        /** 外部資訊(BPM呈現使用) */
        data: any;
        /** BPM流程客制參數 */
        bpmOptions?: any;
        isGeneralManager?: boolean;
        isCOO?: boolean;
        isCEO?: boolean;
    };

    companyCode?: string;

    departmentCode?: string;

    userCode: string;

    attachmentS3Keys?: string[];

    createdUserCode: string;

    files: FileInfo[];
};

export type FormInstanceData = {
    id: number;
    formId: number;
    processInstanceId?: string;
    values?: string;
    startDate: Date;
    finishDate?: Date;
    ownerCompanyCode: string;
    ownerDepartmentCode: string;
    ownerUserCode: string;
    status: EnumInstanceStatus;
    createdUserCode: string;
    deleted: boolean;
    createdAt?: Date;
    updatedAt?: Date;
};

export type FileInfo = {
    filename: string;
    mimetype: string;
    encoding: string;
    content: Buffer;
};
