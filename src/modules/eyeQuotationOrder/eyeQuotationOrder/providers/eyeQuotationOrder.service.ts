import { Inject, Service } from 'typedi';
import {
    Between,
    EntityManager,
    FindOptionsWhere,
    ILike,
    In,
    LessThanOrEqual,
    MoreThanOrEqual,
} from 'typeorm';
import _ from 'lodash';
import Joi from 'joi';
import { CommonService } from '@/common/providers/common.service';
import {
    EnumEyeQuotationOrderStatus,
    EnumWarrantyBuyType,
    EyeQuotationOrder,
} from '@clinico/typeorm-persistence/models/public/eyeQuotationOrder.model';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
    DeleteParams,
    RequestOfficialSealParams,
    UpdateParams,
    InvalidatedParams,
} from '../types/eyeQuotationOrder.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import Big from 'big.js';
import { CodeService } from '@/modules/code/providers/code.service';
import { EnumCodeType } from '@/modules/code/types/code.interface';
import { EyeQuotationOrderProductService } from '@/modules/eyeQuotationOrder/eyeQuotationOrderProduct/providers/eyeQuotationOrderProduct.service';
import { EyeQuotationOrderPromotionService } from '@/modules/eyeQuotationOrder/eyeQuotationOrderPromotion/providers/eyeQuotationOrderPromotion.service';
import { Customer } from '@clinico/typeorm-persistence/models/salesRepWorkstation/customer.model';
import { EyeWarrantyContract } from '@clinico/typeorm-persistence/models/maintenance/eyeWarrantyContract.model';
import { CurrencyService } from '@/modules/currency/providers/currency.service';
import { EyeProductItemService } from '@/modules/eyeProduct/eyeProductItem/providers/eyeProductItem.service';
import { EyePromotionAddonProductItemService } from '@/modules/eyePromotion/eyePromotionAddonProductItem/providers/eyePromotionAddonProductItem.service';
import { EyeQuotationOrderBPMService } from './eyeQuotationOrder.bpm.service';
import { EyeQuotationOrderProductItemService } from '../../eyeQuotationOrderProductItem/providers/eyeQuotationOrderProductItem.service';
import { EyeQuotationOrderWarrantyItemService } from '../../eyeQuotationOrderWarrantyItem/providers/eyeQuotationOrderWarrantyItem.service';
import { CreateParams as EyeQuotationOrderWarrantyItemCreateParams } from '@/modules/eyeQuotationOrder/eyeQuotationOrderWarrantyItem/types/eyeQuotationOrderWarrantyItem.type';
import { TaxRateService } from '@/modules/taxRate/providers/taxRate.service';
import { ExchangeRateService } from '@/modules/exchangeRate/providers/exchangeRate.service';
import { RegionService } from '@/modules/region/providers/region.service';
import { FinanceHelper } from '@/common/helpers/finance.helper';
import { ValidateService } from '@/common/validates/validate.service';
import { EyeQuotationOrderProductItem } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrderProductItem.model';
import { CreditPeriodService } from '@/modules/creditPeriod/providers/creditPeriod.service';
import moment from 'moment';
import { EyeQuotationOrderBusinessService } from '../../eyeQuotationOrderBusiness/providers/eyeQuotationOrderBusiness.service';
import { EyeQuotationOrderTypeService } from '../../eyeQuotationOrderType/providers/eyeQuotationOrderType.service';
import { EnumBPMForm, FormInstanceData } from '@/modules/bpm/types/bpm.type';
import { EnumEyeQuotationOrderType } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrderType.model';
import { EyeQuotationOrderCommissionAmountService } from '../../eyeQuotationOrderCommissionAmount/providers/eyeQuotationOrderCommissionAmount.service';
import { CustomersConsumablePriceService } from '@/modules/customer/customersConsumablePrice/providers/customersConsumablePrice.service';
import { MaterialTypeService } from '@/modules/material/materialType/providers/materialType.service';
import { MaterialService } from '@/modules/material/material/providers/material.service';
import { EyeQuotationOrderAttachFileService } from '../../eyeQuotationOrderAttachFile/providers/eyeQuotationOrderAttachFile.service';

@Service()
export class EyeQuotationOrderService extends CommonService<EyeQuotationOrder> {
    @Inject()
    private codeService: CodeService;
    @Inject()
    private taxRateService: TaxRateService;
    @Inject()
    private regionService: RegionService;
    @Inject()
    private exchangeRateService: ExchangeRateService;
    @Inject()
    private eyeQuotationOrderBPMService: EyeQuotationOrderBPMService;
    @Inject()
    private currencyService: CurrencyService;
    @Inject()
    private eyeProductItemService: EyeProductItemService;
    @Inject()
    private eyePromotionAddonProductItemService: EyePromotionAddonProductItemService;
    @Inject()
    private eyeQuotationOrderBusinessService: EyeQuotationOrderBusinessService;
    @Inject()
    private eyeQuotationOrderProductService: EyeQuotationOrderProductService;
    @Inject()
    private eyeQuotationOrderPromotionService: EyeQuotationOrderPromotionService;
    @Inject()
    private eyeQuotationOrderProductItemService: EyeQuotationOrderProductItemService;
    @Inject()
    private eyeQuotationOrderWarrantyItemService: EyeQuotationOrderWarrantyItemService;
    @Inject()
    private validateService: ValidateService;
    @Inject()
    private creditPeriodService: CreditPeriodService;
    @Inject()
    private eyeQuotationOrderTypeService: EyeQuotationOrderTypeService;
    @Inject()
    private eyeQuotationOrderCommissionAmountService: EyeQuotationOrderCommissionAmountService;
    @Inject()
    private customersConsumablePriceService: CustomersConsumablePriceService;
    @Inject()
    private materialTypeService: MaterialTypeService;
    @Inject('MaterialService')
    private materialService: MaterialService;
    @Inject()
    private eyeQuotationOrderAttachFileService: EyeQuotationOrderAttachFileService;

    private eyeQuotationOrderRepo =
        ClinicoDataSource.getRepository(EyeQuotationOrder);

    get commonDataNotFoundMessage(): string {
        return 'EyeQuotationOrder not found';
    }

    async findOneByManager(
        manager: EntityManager,
        id: number,
    ): Promise<EyeQuotationOrder> {
        return await manager.findOneOrFail(EyeQuotationOrder, {
            where: {
                id: id,
            },
            relations: {
                eyeQuotationOrderProducts: true,
                eyeQuotationOrderPromotions: true,
                eyeQuotationOrderProductItems: true,
                eyeQuotationOrderWarrantyItems: true,
            },
        });
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<EyeQuotationOrder>> {
        const filters: FindOptionsWhere<EyeQuotationOrder> = {};
        const customerFilters: FindOptionsWhere<Customer> = {};
        const invoicingCustomerFilters: FindOptionsWhere<Customer> = {};
        const eyeWarrantyContractFilters: FindOptionsWhere<EyeWarrantyContract> =
            {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.regionIds) {
            filters.regionId = In(params.regionIds);
        }
        if (params.companyIds) {
            filters.companyId = In(params.companyIds);
        }
        if (params.eyeServiceOrderId) {
            filters.eyeServiceOrderId = params.eyeServiceOrderId;
        }
        if (params.code) {
            filters.code = ILike(`%${params.code}%`);
        }
        if (params.invoicingCustomerId) {
            filters.invoicingCustomerId = params.invoicingCustomerId;
        }
        if (params.customerId) {
            filters.customerId = params.customerId;
        }
        if (params.contactPerson) {
            filters.contactPerson = params.contactPerson;
        }
        if (params.currencyId) {
            filters.currencyId = params.currencyId;
        }
        if (params.deptId) {
            filters.deptId = params.deptId;
        }
        if (params.userId) {
            filters.userId = params.userId;
        }
        if (params.createdUserId) {
            filters.createdUserId = params.createdUserId;
        }
        if (!_.isNil(params.status)) {
            filters.status = params.status;
        }
        if (params.orderCode) {
            filters.orderCode = ILike(`%${params.orderCode}%`);
        }
        if (params.bpmInstanceId) {
            filters.bpmInstanceId = params.bpmInstanceId;
        }
        if (params.expectDeliveryDate1) {
            filters.expectDeliveryDate = MoreThanOrEqual(
                params.expectDeliveryDate1,
            );
        }
        if (params.expectDeliveryDate2) {
            filters.expectDeliveryDate = LessThanOrEqual(
                params.expectDeliveryDate2,
            );
        }
        if (params.expectDeliveryDate1 && params.expectDeliveryDate2) {
            filters.expectDeliveryDate = Between(
                params.expectDeliveryDate1,
                params.expectDeliveryDate2,
            );
        }
        if (params.expectPaymentDate1) {
            filters.expectPaymentDate = MoreThanOrEqual(
                params.expectPaymentDate1,
            );
        }
        if (params.expectPaymentDate2) {
            filters.expectPaymentDate = LessThanOrEqual(
                params.expectPaymentDate2,
            );
        }
        if (params.expectPaymentDate1 && params.expectPaymentDate2) {
            filters.expectPaymentDate = Between(
                params.expectPaymentDate1,
                params.expectPaymentDate2,
            );
        }
        if (params.warrantyBuyType) {
            filters.warrantyBuyType = params.warrantyBuyType;
        }
        if (params.provinceId) {
            filters.provinceId = params.provinceId;
        }
        if (params.cityId) {
            filters.cityId = params.cityId;
        }
        if (params.districtId) {
            filters.districtId = params.districtId;
        }
        if (params.creditPeriodId) {
            filters.creditPeriodId = params.creditPeriodId;
        }
        if (params.eyeQuotationOrderTypeId) {
            filters.eyeQuotationOrderTypeId = params.eyeQuotationOrderTypeId;
        }
        if (params.eyeQuotationOrderTypeIds) {
            filters.eyeQuotationOrderTypeId = In(
                params.eyeQuotationOrderTypeIds,
            );
        }
        if (params.createDate1) {
            filters.createdAt = MoreThanOrEqual(params.createDate1);
        }
        if (params.createDate2) {
            filters.createdAt = LessThanOrEqual(params.createDate2);
        }
        if (params.createDate1 && params.createDate2) {
            filters.createdAt = Between(params.createDate1, params.createDate2);
        }
        filters.deleted = false;

        // customer
        if (params.customerCode) {
            customerFilters.code = ILike(`%${params.customerCode}%`);
        }
        if (params.customerName) {
            customerFilters.name = ILike(`%${params.customerName}%`);
        }
        if (params.invoicingCustomerCode) {
            invoicingCustomerFilters.code = ILike(
                `%${params.invoicingCustomerCode}%`,
            );
        }
        if (params.invoicingCustomerName) {
            invoicingCustomerFilters.name = ILike(
                `%${params.invoicingCustomerName}%`,
            );
        }
        if (!_.isEmpty(customerFilters)) {
            customerFilters.deleted = false;
        }

        // eyeWarrantyContract
        if (params.eyeWarrantyContractCode) {
            eyeWarrantyContractFilters.code = ILike(
                `%${params.eyeWarrantyContractCode}%`,
            );
        }
        if (!_.isEmpty(eyeWarrantyContractFilters)) {
            eyeWarrantyContractFilters.deleted = false;
        }

        const data = await this.eyeQuotationOrderRepo.findAndCount({
            where: {
                ...filters,
                customer: customerFilters,
                invoicingCustomer: invoicingCustomerFilters,
                eyeServiceOrder: !_.isEmpty(eyeWarrantyContractFilters)
                    ? {
                          eyeWarrantyContract: eyeWarrantyContractFilters,
                      }
                    : {},
            },
            relations: {
                eyeQuotationOrderProducts: true,
                eyeQuotationOrderPromotions: true,
                user: true,
                createdUser: true,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(params: CreateParams): Promise<EyeQuotationOrder> {
        const taxRate = await this.taxRateService.findOneOrError(
            params.taxRateId,
        );
        const region = await this.regionService.findOneOrError(params.regionId);
        const localCurrency = await this.currencyService.findOneOrError(
            region.localCurrencyId,
        );
        const creditPeriod = await this.creditPeriodService.findOneOrError(
            params.creditPeriodId,
        );

        const eyeQuotationOrderToCreate = this.eyeQuotationOrderRepo.create({
            ...params,
            taxRate: taxRate.rate,
            localCurrencyId: localCurrency.id,
            expectPaymentDate: moment(params.expectDeliveryDate).add(
                creditPeriod.days,
                'days',
            ),
            status: EnumEyeQuotationOrderStatus.Processing,
            eyeQuotationOrderProducts: [],
            eyeQuotationOrderPromotions: [],
            eyeQuotationOrderProductItems: [],
            eyeQuotationOrderWarrantyItems: [],
            eyeQuotationOrderCommissionAmounts: [],
        });

        const createdEyeQuotationOrder = await ClinicoDataSource.transaction(
            async (manager) => {
                // Generate Code
                eyeQuotationOrderToCreate.code =
                    await this.codeService.nextCode(
                        {
                            type: EnumCodeType.EyeQuotationOrder,
                            regionId: params.regionId,
                        },
                        manager,
                    );

                const eyeQuotationOrder = await manager.save(
                    eyeQuotationOrderToCreate,
                );
                await this.mutate(manager, eyeQuotationOrder, params);

                const savedEyeQuotationOrder = await this.findOneByManager(
                    manager,
                    eyeQuotationOrder.id,
                );
                await this.validate(savedEyeQuotationOrder, params);
                return eyeQuotationOrder;
            },
        );
        if (params.attachFiles) {
            for (const attachFile of params.attachFiles) {
                await this.eyeQuotationOrderAttachFileService.create({
                    ...attachFile,
                    eyeQuotationOrderId: createdEyeQuotationOrder.id,
                    createdUserId: params.createdUserId,
                });
            }
        }

        const eyeQuotationOrderType =
            await this.eyeQuotationOrderTypeService.findOne(
                createdEyeQuotationOrder.eyeQuotationOrderTypeId,
            );

        const eyeQuotationOrderForBPM =
            await this.eyeQuotationOrderBPMService.findOne(
                createdEyeQuotationOrder.id,
            );

        //存貨銷售價
        const inventoryPrice = await this.getInventoryPrice(
            eyeQuotationOrderForBPM,
        );

        //毛利率: 銷售相關報價才需要計算毛利率
        let grossProfitMargin: string | undefined;
        let warrantyPrice: string | undefined;
        if (
            eyeQuotationOrderType?.code &&
            [
                EnumEyeQuotationOrderType.Sale,
                EnumEyeQuotationOrderType.FemtosecondSale,
                EnumEyeQuotationOrderType.Repair,
                EnumEyeQuotationOrderType.FemtosecondRepair,
            ].includes(eyeQuotationOrderType.code)
        ) {
            let cost = Big(inventoryPrice)
                .plus(eyeQuotationOrderForBPM.commissionAmount)
                .toFixed(Big.DP);
            //扣除超过一年以上运维合同的对内保修费
            warrantyPrice = await this.getWarrantyPrice(
                eyeQuotationOrderForBPM,
            );
            cost = Big(cost).plus(warrantyPrice).toFixed(Big.DP);

            //毛利率
            grossProfitMargin = FinanceHelper.calcGrossProfitMargin({
                costOfGoodsSoldAmount: cost, // 供应商存货价 + 研究费汇总 + 超期对内保修费
                revenueAmount: eyeQuotationOrderForBPM.untaxedAmount, // 未税金额
            });
        }

        await this.update({
            id: createdEyeQuotationOrder.id,
            grossMargin: grossProfitMargin,
            inventoryPrice,
            warrantyPrice,
            updatedUserId: params.createdUserId,
        });

        let bpmResult: FormInstanceData | null = null;
        switch (eyeQuotationOrderType?.code) {
            case EnumEyeQuotationOrderType.ExhibitionConsumption: //展示耗用
                bpmResult =
                    await this.eyeQuotationOrderBPMService.createForExhibitionConsumption(
                        createdEyeQuotationOrder.id,
                    );
                break;
            case EnumEyeQuotationOrderType.Scrap: //存貨報廢
                bpmResult =
                    await this.eyeQuotationOrderBPMService.createForScrap(
                        createdEyeQuotationOrder.id,
                    );
                break;
            case EnumEyeQuotationOrderType.Requisition: //國內請購
                bpmResult =
                    await this.eyeQuotationOrderBPMService.createForRequisition(
                        createdEyeQuotationOrder.id,
                    );
                break;
            case EnumEyeQuotationOrderType.ForeignRequisition: //國外請購
                bpmResult =
                    await this.eyeQuotationOrderBPMService.createForForeignRequisition(
                        createdEyeQuotationOrder.id,
                    );
                break;
            case EnumEyeQuotationOrderType.InventoryToFixedAssets: //庫存轉固定資產
                bpmResult =
                    await this.eyeQuotationOrderBPMService.createForInventoryToFixedAssets(
                        createdEyeQuotationOrder.id,
                    );
                break;
            default:
                bpmResult =
                    await this.eyeQuotationOrderBPMService.createForEyeQuotationOrder(
                        createdEyeQuotationOrder.id,
                        params.attachFiles,
                    );
        }

        if (bpmResult) {
            await this.eyeQuotationOrderBPMService.updateBPMInstanceId(
                createdEyeQuotationOrder.id,
                bpmResult.id.toString(),
            );
        }

        return await this.findOneOrError(createdEyeQuotationOrder.id);
    }

    async mutate(
        manager: EntityManager,
        eyeQuotationOrder: EyeQuotationOrder,
        params: CreateParams,
    ): Promise<void> {
        const warrantyItemsForCreate: Omit<
            EyeQuotationOrderWarrantyItemCreateParams,
            'eyeQuotationOrderId'
        >[] = [];

        // ISSUE: http://asking.clinico.com.tw/issues/97306
        // 單位攤體折扣金額
        let discountUnitPrice = '0';
        if (params.realDiscountAmount) {
            const eyeQuotationOrderProductItemIds =
                params.eyeQuotationOrderProducts
                    .map((data) => data.eyeProductItems)
                    .flat()
                    .map((item) => item.id);

            const productItems = await this.eyeProductItemService.search({
                ids: eyeQuotationOrderProductItemIds,
            });

            const eyePromotionAddonProductMaterials = productItems.rows.map(
                (item) => ({
                    materialId: item.materialId,
                    qty: item.qty,
                }),
            );
            const eyeQuotationOrderProductMaterials =
                params.eyeQuotationOrderProductItems.map((item) => ({
                    materialId: item.materialId,
                    qty: item.qty,
                }));

            discountUnitPrice =
                await this.eyeQuotationOrderProductItemService.calculateStallDiscount(
                    {
                        items: eyePromotionAddonProductMaterials.concat(
                            eyeQuotationOrderProductMaterials,
                        ),
                        realDiscountAmount: params.realDiscountAmount,
                    },
                );
        }

        // 眼科報價單商品
        if (params.eyeQuotationOrderProducts) {
            for (const product of params.eyeQuotationOrderProducts) {
                const exchangeRate =
                    await this.exchangeRateService.findRateById(
                        product.exchangeRateId,
                    );

                await this.eyeQuotationOrderProductService.create(
                    {
                        eyeQuotationOrder: eyeQuotationOrder,
                        eyeProductId: product.eyeProductId,
                        qty: product.qty,
                        eyeProductItems: product.eyeProductItems.map(
                            (data) => ({
                                ...data,
                                discountUnitPrice,
                            }),
                        ),
                        exchangeRate: exchangeRate,
                        customQuotationPrice: product.customQuotationPrice,
                    },
                    manager,
                );
            }
        }

        // 眼科報價單促銷方案
        if (params.eyeQuotationOrderPromotions) {
            for (const promotion of params.eyeQuotationOrderPromotions) {
                const exchangeRate =
                    await this.exchangeRateService.findRateById(
                        promotion.exchangeRateId,
                    );

                await this.eyeQuotationOrderPromotionService.create(
                    {
                        eyeQuotationOrder: eyeQuotationOrder,
                        eyePromotionId: promotion.eyePromotionId,
                        eyePromotionAddonProductItemIds:
                            promotion.eyePromotionAddonProductItemIds,
                        exchangeRate: exchangeRate,
                    },
                    manager,
                );
            }
        }

        // 眼科報價單單選料號
        if (params.eyeQuotationOrderProductItems) {
            const customersConsumablePriceIds =
                params.eyeQuotationOrderProductItems.map(
                    (data) => data.customersConsumablePriceId ?? 0,
                );

            const customersConsumablePrices =
                await this.customersConsumablePriceService.findByIds(
                    customersConsumablePriceIds,
                );
            for (const productItem of params.eyeQuotationOrderProductItems) {
                const exchangeRate =
                    await this.exchangeRateService.findRateById(
                        productItem.exchangeRateId,
                    );
                const customersConsumablePrice = customersConsumablePrices.find(
                    (data) =>
                        data.customerId == eyeQuotationOrder.customerId &&
                        data.invoicingCustomerId ==
                            eyeQuotationOrder.invoicingCustomerId &&
                        data.materialId == productItem.materialId,
                );
                await this.eyeQuotationOrderProductItemService.create(
                    {
                        eyeQuotationOrderId: eyeQuotationOrder.id,
                        materialId: productItem.materialId,
                        qty: productItem.qty,
                        taxRate: eyeQuotationOrder.taxRate,
                        exchangeRate: exchangeRate,
                        discountUnitPrice,
                        customQuotationPrice: productItem.customQuotationPrice,
                        materialCleanPrice:
                            customersConsumablePrice?.cleanPrice,
                        materialCleanCurrencyId: eyeQuotationOrder.currencyId,
                    },
                    manager,
                );
            }
        }

        // 眼科報價單商機
        if (params.businessIds) {
            for (const businessId of params.businessIds) {
                await this.eyeQuotationOrderBusinessService.create(
                    {
                        eyeQuotationOrder: eyeQuotationOrder,
                        businessId: businessId,
                    },
                    manager,
                );
            }
        }

        // 單買合約
        if (params.warrantyBuyType == EnumWarrantyBuyType.BuySeparately) {
            // 加入保固品項
            for (const warrantyItem of params.eyeQuotationOrderWarrantyItems) {
                warrantyItemsForCreate.push(warrantyItem);
            }
        }
        // 商品變合約
        if (params.warrantyBuyType == EnumWarrantyBuyType.BuyWithProduct) {
            const eyeQuotationOrderProductItems = await manager.find(
                EyeQuotationOrderProductItem,
                {
                    where: {
                        eyeQuotationOrderId: eyeQuotationOrder.id,
                    },
                },
            );
            for (const item of eyeQuotationOrderProductItems) {
                warrantyItemsForCreate.push({
                    materialId: item.materialId,
                    qty: item.qty,
                });
            }
        }

        // 新增合約商品
        for (const warrantyItem of warrantyItemsForCreate) {
            await this.eyeQuotationOrderWarrantyItemService.create(
                {
                    eyeQuotationOrderId: eyeQuotationOrder.id,
                    materialId: warrantyItem.materialId,
                    sn: warrantyItem.sn,
                    udi: warrantyItem.udi,
                    qty: warrantyItem.qty,
                },
                manager,
            );
        }

        // 研究費
        for (const eyeQuotationOrderCommissionAmount of params.eyeQuotationOrderCommissionAmounts ??
            []) {
            await this.eyeQuotationOrderCommissionAmountService.create(
                {
                    eyeQuotationOrder: eyeQuotationOrder,
                    commissionAmount:
                        eyeQuotationOrderCommissionAmount.commissionAmount,
                    eyeQuotationOrderCommissionTypeId:
                        eyeQuotationOrderCommissionAmount.eyeQuotationOrderCommissionTypeId,
                },
                manager,
            );
        }
    }

    async update(params: UpdateParams): Promise<EyeQuotationOrder> {
        const eyeQuotationOrderToUpdate = await this.findOneOrError(params.id);
        const eyeProductGroup = await this.eyeQuotationOrderRepo.save({
            ...eyeQuotationOrderToUpdate,
            ...params,
            id: eyeQuotationOrderToUpdate.id,
        });
        return eyeProductGroup;
    }

    async delete(params: DeleteParams): Promise<void> {
        const eyeQuotationOrderToDelete = await this.findOneOrError(params.id);
        await this.validateForDelete(eyeQuotationOrderToDelete);

        eyeQuotationOrderToDelete.updatedUserId = params.deletedUserId;
        eyeQuotationOrderToDelete.deleted = true;
        await this.eyeQuotationOrderRepo.save(eyeQuotationOrderToDelete);
    }

    async validate(
        eyeQuotationOrder: EyeQuotationOrder,
        params: CreateParams,
    ): Promise<void> {
        // 驗證格式
        const minAmount = 0;
        const maxAmount = 9999999999.99;
        const schema = Joi.object<EyeQuotationOrder>().keys({
            standardAmount: Joi.number().min(minAmount).max(maxAmount),
            realAmount: Joi.number().min(minAmount).max(maxAmount),
            untaxedAmount: Joi.number().max(maxAmount),
        });
        try {
            await schema.validateAsync(eyeQuotationOrder, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }

        // 地區公司驗證
        await this.validateService.regionAndCompany({
            regionId: eyeQuotationOrder.regionId,
            companyId: eyeQuotationOrder.companyId,
        });

        // 地址驗證
        await this.validateService.address({
            provinceId: eyeQuotationOrder.provinceId,
            cityId: eyeQuotationOrder.cityId,
            districtId: eyeQuotationOrder.districtId,
        });

        // 驗算
        // 該幣別的計算支援小數點第幾位
        const currency = await this.currencyService.findOneOrError(
            eyeQuotationOrder.currencyId,
        );
        Big.DP = currency.decimalPlace;

        // 當地幣別原價驗算
        const exchangeRate = await this.exchangeRateService.findRate({
            regionId: eyeQuotationOrder.regionId,
            currency1Id: eyeQuotationOrder.currencyId,
            currency2Id: eyeQuotationOrder.localCurrencyId,
        });
        if (
            !Big(
                FinanceHelper.calcExchangedAmountRound({
                    amount: eyeQuotationOrder.standardAmount,
                    exchangeRate: exchangeRate,
                }),
            ).eq(eyeQuotationOrder.localStandardAmount)
        ) {
            throw new BaseError(`驗算錯誤-當地幣別原價`, 400);
        }

        // 折扣率驗算
        if (
            !Big(
                FinanceHelper.calcDiscountRate({
                    amount: eyeQuotationOrder.standardAmount,
                    discountAmount: eyeQuotationOrder.discountAmount,
                }),
            ).eq(eyeQuotationOrder.discountRate)
        ) {
            throw new BaseError(`驗算錯誤-折扣率`, 400);
        }
        // 研究费比率验算
        if (
            !Big(
                FinanceHelper.calcCommissionRate({
                    amount: eyeQuotationOrder.standardAmount,
                    commissionAmount: eyeQuotationOrder.commissionAmount,
                }),
            ).eq(eyeQuotationOrder.commissionRate)
        ) {
            throw new BaseError(`验算错误-研究费比率`, 400);
        }
        // 實際金額驗算
        if (
            !Big(eyeQuotationOrder.standardAmount)
                .minus(eyeQuotationOrder.discountAmount)
                .minus(eyeQuotationOrder.extraDiscountAmount)
                .eq(eyeQuotationOrder.realAmount)
        ) {
            throw new BaseError(`驗算錯誤-實際金額`, 400);
        }
        // 實際折扣金額驗算
        if (
            !Big(eyeQuotationOrder.standardAmount)
                .minus(eyeQuotationOrder.realAmount)
                .eq(eyeQuotationOrder.realDiscountAmount)
        ) {
            throw new BaseError(`驗算錯誤-實際折扣金額`, 400);
        }
        // 實際折扣率驗算
        const currDiscountRate = (() => {
            if (Big(eyeQuotationOrder.recommendedAmount).eq(0)) {
                return '0';
            }
            return Big(eyeQuotationOrder.realAmount)
                .div(eyeQuotationOrder.recommendedAmount)
                .toFixed(Big.DP);
        })();
        if (!Big(currDiscountRate).eq(eyeQuotationOrder.realDiscountRate)) {
            throw new BaseError(`驗算錯誤-實際折扣率`, 400);
        }
        // 未稅金額驗算
        if (
            !Big(
                FinanceHelper.calcUntaxedAmount({
                    amount: eyeQuotationOrder.realAmount,
                    taxRate: eyeQuotationOrder.taxRate,
                }),
            ).eq(eyeQuotationOrder.untaxedAmount)
        ) {
            throw new BaseError(`驗算錯誤-未稅金額`, 400);
        }
        // 不含佣金折扣率驗算
        const currDiscountRateWithoutCommission = (() => {
            if (Big(eyeQuotationOrder.standardAmount).eq(0)) {
                return '0';
            }
            return Big(eyeQuotationOrder.realAmount)
                .minus(eyeQuotationOrder.commissionAmount)
                .div(eyeQuotationOrder.standardAmount)
                .toFixed(Big.DP);
        })();
        if (
            !Big(currDiscountRateWithoutCommission).eq(
                eyeQuotationOrder.discountRateWithoutCommission,
            )
        ) {
            throw new BaseError(`驗算錯誤-不含佣金折扣率`, 400);
        }

        // 驗證商品項目/優惠加贈項目是否在該商品/優惠底下
        const eyeProductIds = eyeQuotationOrder.eyeQuotationOrderProducts.map(
            (el) => el.eyeProductId,
        );
        const eyeProductItems = await this.eyeProductItemService.findByIds(
            eyeQuotationOrder.eyeQuotationOrderProductItems
                .map((el) => el.eyeProductItemId)
                .filter(Number),
        );
        const eyePromotionIds =
            eyeQuotationOrder.eyeQuotationOrderPromotions.map(
                (el) => el.eyePromotionId,
            );
        const eyePromotionItems =
            await this.eyePromotionAddonProductItemService.findByIds(
                eyeQuotationOrder.eyeQuotationOrderProductItems
                    .map((el) => el.eyePromotionAddonProductItemId)
                    .filter(Number),
            );
        for (const item of eyeProductItems) {
            if (!eyeProductIds.includes(item.eyeProductId)) {
                throw new BaseError(`商品項目不在該商品底下`, 400);
            }
        }
        for (const item of eyePromotionItems) {
            if (!eyePromotionIds.includes(item.eyePromotionId)) {
                throw new BaseError(`加贈項目不在該優惠底下`, 400);
            }
        }
        //儀器類別
        const eyeProductItemTypes = await this.materialTypeService.search({
            code: '1',
            regionIds: [eyeQuotationOrder.regionId],
        });
        //組配料件
        const eyeProductItemMaterials = await this.materialService.findByIds(
            eyeProductItems.map((el) => el.materialId),
        );
        //儀器類別的料件
        const instruments = eyeProductItemMaterials.filter((el) =>
            eyeProductItemTypes.rows
                .map((el) => el.id)
                .includes(el.materialTypeId),
        );
        for (const item of instruments) {
            if (_.isNull(item.internalWarrantyPrice)) {
                throw new BaseError(`${item.code}: 请配置对内保修费`, 400);
            }
        }

        // 驗證保固
        if (
            eyeQuotationOrder.warrantyBuyType ==
                EnumWarrantyBuyType.BuyWithProduct &&
            !_.isEmpty(params.eyeQuotationOrderWarrantyItems)
        ) {
            throw new BaseError(
                `挑選商品、優惠、單選料號時，無法與「單買保固」一起選購`,
                400,
            );
        }
        if (
            eyeQuotationOrder.warrantyBuyType ==
                EnumWarrantyBuyType.BuySeparately &&
            _.isEmpty(params.eyeQuotationOrderWarrantyItems)
        ) {
            throw new BaseError(`單買保固時，應填入保固對象商品`, 400);
        }
    }

    async validateForDelete(
        eyeQuotationOrder: EyeQuotationOrder,
    ): Promise<void> {
        throw new BaseError(
            '本報價單狀態為(處理中、完成、拒絕、取消)，無法刪除',
            400,
        );
    }

    async requestOfficialSeal(params: RequestOfficialSealParams) {
        const bpmResult =
            await this.eyeQuotationOrderBPMService.createForRequestOfficialSeal(
                params.eyeQuotationOrderId,
            );

        await this.eyeQuotationOrderBPMService.updateBPMOfficialSealInstanceId(
            params.eyeQuotationOrderId,
            bpmResult.id.toString(),
        );

        return true;
    }

    async getBPMFormCodeByEyeQuotationOrderTypeId(
        eyeQuotationOrderTypeId: number,
        regionId?: number,
    ) {
        const eyeQuotationOrderType =
            await this.eyeQuotationOrderTypeService.findOne(
                eyeQuotationOrderTypeId,
            );

        switch (eyeQuotationOrderType?.code) {
            case EnumEyeQuotationOrderType.ExhibitionConsumption: //展耗
                return regionId == 1
                    ? EnumBPMForm.ExhibitionConsumptionTW
                    : EnumBPMForm.ExhibitionConsumptionCN;
            case EnumEyeQuotationOrderType.Scrap: //報廢
                return regionId == 1
                    ? EnumBPMForm.ScrapTW
                    : EnumBPMForm.ScrapCN;
            case EnumEyeQuotationOrderType.Requisition: //國內請購
                return regionId == 1
                    ? EnumBPMForm.RequisitionTW
                    : EnumBPMForm.RequisitionCN;
            case EnumEyeQuotationOrderType.ForeignRequisition: //國外請購
                return regionId == 1
                    ? EnumBPMForm.ForeignRequisitionTW
                    : EnumBPMForm.ForeignRequisitionCN;
            case EnumEyeQuotationOrderType.InventoryToFixedAssets: //庫存轉固定資產
                return regionId == 1
                    ? EnumBPMForm.InventoryToFixedAssetsTW
                    : EnumBPMForm.InventoryToFixedAssetsCN;
            default:
                return regionId == 1
                    ? EnumBPMForm.QuotationOrderTW
                    : EnumBPMForm.QuotationOrderCN;
        }
    }

    //存貨銷售價
    private async getInventoryPrice(
        eyeQuotationOrderForBPM: EyeQuotationOrder,
    ) {
        let totalInventoryPrice = Big(0);
        const productCosts: Record<string, string> = {};

        //組配
        for (const product of eyeQuotationOrderForBPM.eyeQuotationOrderProducts) {
            let itemInventoryPrice = Big(0);
            for (const item of product.eyeQuotationOrderProductItems) {
                const inventoryPrice = item.materialInventoryPrice ?? 0;
                itemInventoryPrice = itemInventoryPrice.plus(
                    Big(inventoryPrice).times(item.unitQty),
                );
            }
            totalInventoryPrice = totalInventoryPrice.plus(
                itemInventoryPrice.times(product.qty),
            );
            productCosts[product.id] = itemInventoryPrice.toFixed(Big.DP);
        }

        //單品項
        eyeQuotationOrderForBPM.eyeQuotationOrderProductItems
            .filter((data) => !data.eyeQuotationOrderProductId)
            .forEach((item) => {
                const inventoryPrice = item.materialInventoryPrice ?? 0;
                totalInventoryPrice = totalInventoryPrice.plus(
                    Big(inventoryPrice).times(item.qty), // 成本金額 = 成本單價 * 報價數量
                );
                console.log(
                    `${totalInventoryPrice}=${inventoryPrice} * ${item.unitQty} * ${item.qty}`,
                );
            });

        //美金報價
        if (eyeQuotationOrderForBPM.currency?.code === 'USD') {
            const exchangeRate = await this.exchangeRateService.findRate({
                regionId: eyeQuotationOrderForBPM.regionId,
                currency1Id: eyeQuotationOrderForBPM.localCurrencyId,
                currency2Id: eyeQuotationOrderForBPM.currencyId,
            });
            // 人民幣轉美金
            const totalInventoryPriceInUSD =
                FinanceHelper.calcExchangedAmountRound({
                    amount: totalInventoryPrice.toFixed(Big.DP),
                    exchangeRate: exchangeRate,
                });
            totalInventoryPrice = Big(totalInventoryPriceInUSD);
        }

        return totalInventoryPrice.toFixed(Big.DP);
    }

    async test(id: number): Promise<any> {
        const eyeQuotationOrderForBPM =
            await this.eyeQuotationOrderBPMService.findOne(id);
        //存貨銷售價
        const inventoryPrice = await this.getInventoryPrice(
            eyeQuotationOrderForBPM,
        );
        let cost = Big(inventoryPrice)
            .plus(eyeQuotationOrderForBPM.commissionAmount)
            .toFixed(Big.DP);
        //扣除超过一年以上运维合同的对内保修费
        const warrantyPrice = await this.getWarrantyPrice(
            eyeQuotationOrderForBPM,
        );
        cost = Big(cost).plus(warrantyPrice).toFixed(Big.DP);
        //毛利率
        const grossProfitMargin = FinanceHelper.calcGrossProfitMargin({
            costOfGoodsSoldAmount: cost, // 供应商存货价 + 研究费汇总 + 超期对内保修费
            revenueAmount: eyeQuotationOrderForBPM.untaxedAmount, // 未税金额
        });

        return {
            grossProfitMargin,
            inventoryPrice,
        };
    }

    //超过一年以上运维合同的对内保修费用
    private async getWarrantyPrice(eyeQuotationOrderForBPM: EyeQuotationOrder) {
        let totalWarrantyPrice = 0;

        // 逐一處理每個商品
        for (const product of eyeQuotationOrderForBPM.eyeQuotationOrderProducts) {
            let warrantyPrice = 0;
            // 逐一處理商品下的每個 item
            for (const item of product.eyeQuotationOrderProductItems) {
                if (item.warrantyMonths && item.warrantyMonths > 12) {
                    const monthsOver = item.warrantyMonths - 12;

                    let internalWarrantyPrice = Big(0);
                    for (const warrantyPrice of item.eyeQuotationOrderProductItemWarrantyPrices ??
                        []) {
                        // warrantyPrice.internalWarrantyPrice 為null or undefined 時，回傳錯誤
                        if (_.isNil(warrantyPrice.internalWarrantyPrice)) {
                            throw new BaseError(
                                `${item.materialCode}: 请配置对内保修费`,
                                400,
                            );
                        }
                        if (
                            warrantyPrice.internalWarrantyCurrencyId ===
                            eyeQuotationOrderForBPM.currencyId
                        ) {
                            internalWarrantyPrice = internalWarrantyPrice.plus(
                                new Big(
                                    warrantyPrice.internalWarrantyPrice ?? 0,
                                ),
                            );
                        } else {
                            const exchangeRate =
                                await this.exchangeRateService.findRate({
                                    regionId: eyeQuotationOrderForBPM.regionId,
                                    currency1Id:
                                        warrantyPrice.internalWarrantyCurrencyId,
                                    currency2Id:
                                        eyeQuotationOrderForBPM.currencyId,
                                });

                            const internalWarrantyPriceInTargetCurrency =
                                FinanceHelper.calcExchangedAmount({
                                    amount:
                                        warrantyPrice.internalWarrantyPrice ??
                                        0,
                                    exchangeRate,
                                });

                            internalWarrantyPrice = internalWarrantyPrice.plus(
                                new Big(internalWarrantyPriceInTargetCurrency),
                            );
                        }
                    }

                    warrantyPrice = Big(internalWarrantyPrice)
                        .times(monthsOver / 12)
                        .times(item.unitQty)
                        .toNumber();
                }
            }
            warrantyPrice = warrantyPrice * product.qty;
            totalWarrantyPrice += warrantyPrice;
        }

        return Big(totalWarrantyPrice)
            .round(0, Big.roundHalfUp)
            .toFixed(Big.DP);
    }

    async invalidated(params: InvalidatedParams): Promise<boolean> {
        await ClinicoDataSource.transaction(async (manager) => {
            const eyeQuotationOrder = await manager.findOneByOrFail(
                EyeQuotationOrder,
                {
                    id: params.id,
                },
            );
            eyeQuotationOrder.status = EnumEyeQuotationOrderStatus.Invalidated;
            await manager.save(eyeQuotationOrder);
        });
        return true;
    }
}
