import { Inject, Service } from 'typedi';
import {
    EnumEyeQuotationOrderOfficialSealStatus,
    EnumEyeQuotationOrderStatus,
    EyeQuotationOrder,
} from '@clinico/typeorm-persistence/models/public/eyeQuotationOrder.model';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import _ from 'lodash';
import httpStatus from 'http-status';
import {
    EnumBPMForm,
    EnumInstanceStatus,
    FormInstanceData,
} from '@/modules/bpm/types/bpm.type';
import { NotificationService } from '@/modules/notification/providers/notification.service';
import { EnumNotificationType } from '@clinico/typeorm-persistence/models/public/notification.model';
import Big from 'big.js';
import { OrganizationService } from '@/modules/organization/providers/organization.service';
import { DeptService } from '@/modules/dept/providers/dept.service';
import { BPMService } from '@/modules/bpm/providers/bpm.service';
import { Supervisor } from '@/modules/organization/types/organization.type';
import { BusinessService } from '@/modules/business/business/providers/business.service';
import { BusinessStatusService } from '@/modules/business/businessStatus/providers/businessStatus.service';
import { EnumBusinessStatusType } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessStatus.model';
import { FinanceHelper } from '@/common/helpers/finance.helper';
import { gql, GraphQLClient } from 'graphql-request';
import { EnumEyeQuotationOrderType } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrderType.model';
import { UserService } from '@/modules/user/providers/user.service';
import { EyeQuotationOrderMailer } from './eyeQuotationOrder.mailer';
import { EyeQuotationOrderCommissionAmount } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrderCommissionAmount.model';
import {
    WebhookParams,
    WebhookResult,
} from '../types/eyeQuotationOrder.controller.type';
import { EyeQuotationOrderProductItem } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrderProductItem.model';
import { EyeQuotationOrderWarrantyItem } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrderWarrantyItem.model';
import { FileUpload } from 'graphql-upload';
import { FileHelper } from '@/common/helpers/file.helper';
import { ReadStream } from 'fs-capacitor';

@Service()
export class EyeQuotationOrderBPMService {
    @Inject()
    private bpmService: BPMService;
    @Inject()
    private notificationService: NotificationService;
    @Inject()
    private organizationService: OrganizationService;
    @Inject()
    private deptService: DeptService;
    @Inject()
    private businessService: BusinessService;
    @Inject()
    private businessStatusService: BusinessStatusService;
    @Inject()
    private userService: UserService;
    @Inject()
    private eyeQuotationOrderMailer: EyeQuotationOrderMailer;

    private eyeQuotationOrderRepo =
        ClinicoDataSource.getRepository(EyeQuotationOrder);
    private eyeQuotationOrderProductItemsRepo = ClinicoDataSource.getRepository(
        EyeQuotationOrderProductItem,
    );
    private eyeQuotationOrderWarrantyItemsRepo =
        ClinicoDataSource.getRepository(EyeQuotationOrderWarrantyItem);

    get commonDataNotFoundMessage(): string {
        return 'EyeQuotationOrder not found';
    }

    async findOne(id: number): Promise<EyeQuotationOrder> {
        const eyeQuotationOrders = await this.eyeQuotationOrderRepo.find({
            where: {
                id: id,
            },
            relations: {
                customer: {
                    type: true,
                },
                invoicingCustomer: true,
                user: {
                    department: true,
                },
                dept: {
                    company: true,
                },
                createdUser: true,
                currency: true,
                eyeQuotationOrderProducts: {
                    eyeProduct: true,
                    eyeQuotationOrderProductItems: {
                        material: true,
                        eyeQuotationOrderProductItemWarrantyPrices: true,
                    },
                },
                eyeQuotationOrderPromotions: true,
                warrantyPeriodType: true,
                eyeQuotationOrderBusinesses: {
                    business: {
                        businessesUsers: {
                            user: {
                                department: true,
                            },
                        },
                    },
                },
                eyeQuotationOrderType: true,
            },
        });
        const eyeQuotationOrderProductItems =
            await this.eyeQuotationOrderProductItemsRepo.find({
                where: {
                    eyeQuotationOrderId: id,
                },
            });
        const eyeQuotationOrderWarrantyItems =
            await this.eyeQuotationOrderWarrantyItemsRepo.find({
                where: {
                    eyeQuotationOrderId: id,
                },
            });

        if (eyeQuotationOrders.length !== 1) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }

        eyeQuotationOrders[0].eyeQuotationOrderProductItems =
            eyeQuotationOrderProductItems;
        eyeQuotationOrders[0].eyeQuotationOrderWarrantyItems =
            eyeQuotationOrderWarrantyItems;

        return eyeQuotationOrders[0];
    }

    async createForEyeQuotationOrder(
        id: number,
        files?: {
            file: Promise<FileUpload>;
            name: string;
            memo?: string;
        }[],
    ): Promise<FormInstanceData> {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const { isGeneralManager } = await this.assignByAmount(
            eyeQuotationOrderForBPM,
        );

        const officeManager = await this.organizationService.directSupervisor({
            companyId: eyeQuotationOrderForBPM.companyId,
            deptId: eyeQuotationOrderForBPM.deptId,
        });

        let regionalManager: Supervisor | null = null;
        regionalManager = await this.organizationService.regionalSupervisor({
            companyId: eyeQuotationOrderForBPM.companyId,
            deptId: eyeQuotationOrderForBPM.deptId,
        });

        // 部級主管
        let businessManager: Supervisor | null = null;
        if (eyeQuotationOrderForBPM.regionId == 2) {
            const depts = await this.deptService.search({
                companyId: eyeQuotationOrderForBPM.companyId,
                code: 'V', //总经理室
            });

            const mainDept = depts.rows.find(
                (dept) => dept.code == 'V', //总经理室
            );

            if (mainDept) {
                businessManager =
                    await this.organizationService.departmentSupervisor({
                        companyId: eyeQuotationOrderForBPM.companyId,
                        deptId: eyeQuotationOrderForBPM.deptId,
                        mainDeptId: mainDept.id,
                    });
            }
        }

        //總經理
        let generalManager: Supervisor | null = null;
        const isAssigned = await this.assignByProfitForGeneralManager(
            eyeQuotationOrderForBPM,
        );
        if (
            eyeQuotationOrderForBPM.regionId == 2 &&
            (isGeneralManager || isAssigned)
        ) {
            generalManager = { deptCode: 'V', userCode: 'E0001-1' };
        }

        // 財務主管
        let financeManager: Supervisor | null = null;
        const isFinanceManager = await this.assignByProfitForFinanceManager(
            eyeQuotationOrderForBPM,
        );
        if (isFinanceManager) {
            financeManager = { deptCode: 'A', userCode: 'L1584-1' };
        }

        let initiator: Supervisor[] = [];
        let salesOrSecretaries: Supervisor[] = [];
        //銷售：起單人＆秘書 -> 秘書
        if (
            eyeQuotationOrderForBPM.eyeQuotationOrderType.code &&
            [
                EnumEyeQuotationOrderType.Sale,
                EnumEyeQuotationOrderType.FemtosecondSale,
            ].includes(eyeQuotationOrderForBPM.eyeQuotationOrderType.code)
        ) {
            initiator = initiator.concat([
                {
                    userCode: eyeQuotationOrderForBPM.user.code,
                    deptCode: eyeQuotationOrderForBPM.user.department.code,
                },
            ]);

            salesOrSecretaries = salesOrSecretaries.concat([
                {
                    deptCode: 'E-A-1',
                    userCode: 'C0393',
                },
                {
                    deptCode: 'E-A-1',
                    userCode: 'C0325',
                },
                {
                    deptCode: 'E-A-1',
                    userCode: 'C0418',
                },
            ]);
        } else if (
            eyeQuotationOrderForBPM.eyeQuotationOrderType.code &&
            [
                EnumEyeQuotationOrderType.Repair,
                EnumEyeQuotationOrderType.FemtosecondRepair,
            ].includes(eyeQuotationOrderForBPM.eyeQuotationOrderType.code)
        ) {
            //維修：不需要工程師審核 #117130
            salesOrSecretaries = [];

            //維修：不需簽到財務與總經理
            financeManager = null;
            generalManager = null;
        }
        const initiatorSecretaries = await this.getSecretaries(
            eyeQuotationOrderForBPM,
        );

        const bpmOptions = {
            initiator: initiator.concat(
                initiatorSecretaries.map(
                    (secretary) =>
                        <Supervisor>{
                            userCode: secretary.userCode,
                            deptCode: secretary.deptCode,
                        },
                ),
            ), //起單人＆秘書
            officeManager: officeManager?.userCode ? officeManager : null, //辦事處主管or直屬主管
            secretaries: salesOrSecretaries,
            regionalManager: regionalManager?.userCode ? [regionalManager] : [], //大區主管
            businessManager: businessManager?.userCode ? [businessManager] : [], //部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //總經理
            financialManager: financeManager?.userCode ? [financeManager] : [], //財務主管
            // step6 & step7 關卡 cost_price 可看人員
            permissions: {
                enable: {
                    cost_price: {
                        step6: {
                            userCodes: ['L1584', 'L1584-1'],
                        },
                        step7: {
                            userCodes: ['L0528', 'L0528-W', 'L1901', 'L1901-1'],
                        },
                    },
                },
            },
        };

        // 打BPM新增
        try {
            const bpmFiles: {
                filename: string;
                mimetype: string;
                encoding: string;
                content: Buffer;
            }[] = [];
            if (files) {
                for (const file of files) {
                    const data = await FileHelper.getFile(file.file);

                    // 將檔案內容轉換為 Buffer
                    let fileBuffer: Buffer;
                    if (Buffer.isBuffer(data.body)) {
                        // 如果已經是 Buffer（GRPCFileUpload 情況）
                        fileBuffer = data.body;
                    } else {
                        // 如果是 ReadStream（FileUpload 情況），從 stream 讀取
                        const chunks: Buffer[] = [];
                        for await (const chunk of data.body) {
                            chunks.push(chunk);
                        }
                        fileBuffer = Buffer.concat(chunks);
                    }

                    bpmFiles.push({
                        filename: file.name,
                        mimetype: data.mimetype,
                        encoding: data.encoding,
                        content: fileBuffer
                    });
                }
            }

            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.QuotationOrderTW
                        : EnumBPMForm.QuotationOrderCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: await this.getEyeQuotationOrderById(id),
                    bpmOptions,
                    isGeneralManager: isGeneralManager || undefined,
                    isCEO: financeManager || generalManager ? true : false,
                },
                files: bpmFiles,
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOrder.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async createForRequisition(id: number): Promise<FormInstanceData | null> {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const initiator: Supervisor = {
            userCode: eyeQuotationOrderForBPM.user.code,
            deptCode: eyeQuotationOrderForBPM.user.department.code,
        };

        //辦事處主管or直屬主管
        let officeManager: Supervisor | null = null;
        // 大區 or 部級主管
        let businessManager: Supervisor | null = null;
        //總經理
        let generalManager: Supervisor | null = null;
        let cooManager: Supervisor | null = null;
        const isCOO = false;
        let isCEO = false;

        /*
         * 2,500 以下         部門主管
         * 2,501 - 6,000     大區經理 / 事業單位主管 / 財務主管 / 市場部主管 / 科林工厂
         * 6,001 - 7,500     營運長
         * 7,501 - 12,500    總經理
         * 12,501 以上        執行長 / 代理人
         */
        const amount = new Big(eyeQuotationOrderForBPM.inventoryPrice); //存貨銷售價
        // 2,500 以下
        if (amount.gte(0) || amount.gte(2500)) {
            officeManager = await this.organizationService.directSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
        }
        // 2,501 - 6,000
        if ((amount.gte(2501) && amount.lte(6000)) || amount.gte(6000)) {
            businessManager = await this.organizationService.businessSupervisor(
                {
                    companyId: eyeQuotationOrderForBPM.companyId,
                    deptId: eyeQuotationOrderForBPM.deptId,
                },
            );
        }
        // 6,001 - 7,500
        if ((amount.gte(6001) && amount.lte(7500)) || amount.gte(7500)) {
            //COO
            cooManager = await this.organizationService.cooSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
            if (!cooManager) {
                const depts = await this.deptService.search({
                    companyId: eyeQuotationOrderForBPM.companyId,
                    code: 'V', //总经理室
                });
                const mainDept = depts.rows.find(
                    (dept) => dept.code == 'V', //总经理室
                );
                if (mainDept) {
                    cooManager =
                        await this.organizationService.departmentManager({
                            companyId: eyeQuotationOrderForBPM.companyId,
                            deptId: mainDept.id,
                        });
                }
            }
        }
        // 7,501 - 12,500
        if ((amount.gte(7501) && amount.lte(12500)) || amount.gte(12500)) {
            const depts = await this.deptService.search({
                companyId: eyeQuotationOrderForBPM.companyId,
                code: 'V', //总经理室
            });
            const mainDept = depts.rows.find(
                (dept) => dept.code == 'V', //总经理室
            );
            if (mainDept) {
                generalManager =
                    await this.organizationService.departmentManager({
                        companyId: eyeQuotationOrderForBPM.companyId,
                        deptId: mainDept.id,
                    });
            }
        }
        // 12,501 以上
        if (amount.gte(12501)) {
            isCEO = true;
        }

        const bpmOptions = {
            initiator: [initiator], //起單人
            officeManager: officeManager?.userCode ? [officeManager] : [], //辦事處主管or直屬主管 (B9)
            businessManager: businessManager?.userCode ? [businessManager] : [], //大區主管 or 部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //总经理
            cooManager: cooManager?.userCode ? [cooManager] : [], //COO
        };

        // 打BPM新增
        try {
            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.RequisitionTW
                        : EnumBPMForm.RequisitionCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: await this.getEyeQuotationOrderById(id),
                    bpmOptions,
                    isCOO,
                    isCEO,
                },
                files: [],
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOrder.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async createForForeignRequisition(
        id: number,
    ): Promise<FormInstanceData | null> {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const initiator: Supervisor = {
            userCode: eyeQuotationOrderForBPM.user.code,
            deptCode: eyeQuotationOrderForBPM.user.department.code,
        };

        //辦事處主管or直屬主管
        const officeManager: Supervisor | null = null;
        // 大區 or 部級主管
        let businessManager: Supervisor | null = null;
        //總經理
        let generalManager: Supervisor | null = null;
        let cooManager: Supervisor | null = null;
        const isCOO = false;
        let isCEO = false;

        /*
         * 5000 以下         大區經理 / 事業單位主管 / 財務主管 / 市場部主管 / 科林工厂
         * 5,001 - 10,000     營運長
         * 10,001 - 30,000    總經理
         * 30,001 以上        執行長 / 代理人
         */
        const amount = new Big(eyeQuotationOrderForBPM.inventoryPrice); //存貨銷售價
        // 5000 以下
        if (amount.gte(0) || amount.gte(5000)) {
            businessManager = await this.organizationService.businessSupervisor(
                {
                    companyId: eyeQuotationOrderForBPM.companyId,
                    deptId: eyeQuotationOrderForBPM.deptId,
                },
            );
        }
        // 5,001 - 10,000
        if ((amount.gte(5001) && amount.lte(10000)) || amount.gte(10000)) {
            //COO
            cooManager = await this.organizationService.cooSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
            if (!cooManager) {
                const depts = await this.deptService.search({
                    companyId: eyeQuotationOrderForBPM.companyId,
                    code: 'V', //总经理室
                });
                const mainDept = depts.rows.find(
                    (dept) => dept.code == 'V', //总经理室
                );
                if (mainDept) {
                    cooManager =
                        await this.organizationService.departmentManager({
                            companyId: eyeQuotationOrderForBPM.companyId,
                            deptId: mainDept.id,
                        });
                }
            }
        }
        // 10,001 - 30,000
        if ((amount.gte(10001) && amount.lte(30000)) || amount.gte(30000)) {
            const depts = await this.deptService.search({
                companyId: eyeQuotationOrderForBPM.companyId,
                code: 'V', //总经理室
            });
            const mainDept = depts.rows.find(
                (dept) => dept.code == 'V', //总经理室
            );
            if (mainDept) {
                generalManager =
                    await this.organizationService.departmentManager({
                        companyId: eyeQuotationOrderForBPM.companyId,
                        deptId: mainDept.id,
                    });
            }
        }
        // 30,001 以上
        if (amount.gte(30001)) {
            isCEO = true;
        }

        const bpmOptions = {
            initiator: [initiator], //起單人
            businessManager: businessManager?.userCode ? [businessManager] : [], //大區主管 or 部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //总经理
            cooManager: cooManager?.userCode ? [cooManager] : [], //COO
        };

        // 打BPM新增
        try {
            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.ForeignRequisitionTW
                        : EnumBPMForm.ForeignRequisitionCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: await this.getEyeQuotationOrderById(id),
                    bpmOptions,
                    isCOO,
                    isCEO,
                },
                files: [],
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOrder.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async createForInventoryToFixedAssets(
        id: number,
    ): Promise<FormInstanceData | null> {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const initiator: Supervisor = {
            userCode: eyeQuotationOrderForBPM.user.code,
            deptCode: eyeQuotationOrderForBPM.user.department.code,
        };

        //辦事處主管or直屬主管
        const officeManager: Supervisor | null =
            await this.organizationService.directSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
        // 大區 or 部級主管
        const businessManager: Supervisor | null =
            await this.organizationService.businessSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });

        //總經理
        let generalManager: Supervisor | null = null;
        const depts = await this.deptService.search({
            companyId: eyeQuotationOrderForBPM.companyId,
            code: 'V', //总经理室
        });
        const mainDept = depts.rows.find(
            (dept) => dept.code == 'V', //总经理室
        );
        if (mainDept) {
            generalManager = await this.organizationService.departmentManager({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: mainDept.id,
            });
        }

        const bpmOptions = {
            initiator: [initiator], //起單人
            officeManager: officeManager?.userCode ? [officeManager] : [],
            businessManager: businessManager?.userCode ? [businessManager] : [], //大區主管 or 部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //总经理
        };

        // 打BPM新增
        try {
            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.InventoryToFixedAssetsTW
                        : EnumBPMForm.InventoryToFixedAssetsCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: await this.getEyeQuotationOrderById(id),
                    bpmOptions,
                    isCEO: true,
                },
                files: [],
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOrder.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async createForExhibitionConsumption(
        id: number,
    ): Promise<FormInstanceData | null> {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const initiator: Supervisor = {
            userCode: eyeQuotationOrderForBPM.user.code,
            deptCode: eyeQuotationOrderForBPM.user.department.code,
        };

        //辦事處主管or直屬主管
        let officeManager: Supervisor | null = null;
        // 大區 or 部級主管
        let businessManager: Supervisor | null = null;
        //總經理
        let generalManager: Supervisor | null = null;
        let cooManager: Supervisor | null = null;
        const isCOO = false;
        let isCEO = false;

        /*
         * 2,500 以下         部門主管
         * 2,501 - 6,000     大區經理 / 事業單位主管 / 財務主管 / 市場部主管
         * 6,001 - 7,500     營運長
         * 7,501 - 12,500    總經理
         * 12,501 以上        執行長 / 代理人
         */
        const amount = new Big(eyeQuotationOrderForBPM.inventoryPrice); //存貨銷售價
        // 2,500 以下
        if (amount.gte(0) || amount.gte(2500)) {
            officeManager = await this.organizationService.directSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
        }
        // 2,501 - 6,000
        if ((amount.gte(2501) && amount.lte(6000)) || amount.gte(6000)) {
            businessManager = await this.organizationService.businessSupervisor(
                {
                    companyId: eyeQuotationOrderForBPM.companyId,
                    deptId: eyeQuotationOrderForBPM.deptId,
                },
            );
        }
        // 6,001 - 7,500
        if ((amount.gte(6001) && amount.lte(7500)) || amount.gte(7500)) {
            //COO
            cooManager = await this.organizationService.cooSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
            if (!cooManager) {
                const depts = await this.deptService.search({
                    companyId: eyeQuotationOrderForBPM.companyId,
                    code: 'V', //总经理室
                });
                const mainDept = depts.rows.find(
                    (dept) => dept.code == 'V', //总经理室
                );
                if (mainDept) {
                    cooManager =
                        await this.organizationService.departmentManager({
                            companyId: eyeQuotationOrderForBPM.companyId,
                            deptId: mainDept.id,
                        });
                }
            }
        }
        // 7,501 - 12,500
        if ((amount.gte(7501) && amount.lte(12500)) || amount.gte(12500)) {
            const depts = await this.deptService.search({
                companyId: eyeQuotationOrderForBPM.companyId,
                code: 'V', //总经理室
            });
            const mainDept = depts.rows.find(
                (dept) => dept.code == 'V', //总经理室
            );
            if (mainDept) {
                generalManager =
                    await this.organizationService.departmentManager({
                        companyId: eyeQuotationOrderForBPM.companyId,
                        deptId: mainDept.id,
                    });
            }
        }
        // 12,501 以上
        if (amount.gte(12501)) {
            isCEO = true;
        }

        const bpmOptions = {
            initiator: [initiator], //起單人
            officeManager: officeManager?.userCode ? [officeManager] : [], //辦事處主管or直屬主管 (B9)
            businessManager: businessManager?.userCode ? [businessManager] : [], //大區主管 or 部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //总经理
            cooManager: cooManager?.userCode ? [cooManager] : [], //COO
        };

        // 打BPM新增
        try {
            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.ExhibitionConsumptionTW
                        : EnumBPMForm.ExhibitionConsumptionCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: await this.getEyeQuotationOrderById(id),
                    bpmOptions,
                    isCOO,
                    isCEO,
                },
                files: [],
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOrder.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async createForScrap(id: number): Promise<FormInstanceData | null> {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const initiator: Supervisor = {
            userCode: eyeQuotationOrderForBPM.user.code,
            deptCode: eyeQuotationOrderForBPM.user.department.code,
        };

        //辦事處主管or直屬主管
        const officeManager: Supervisor | null =
            await this.organizationService.directSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });
        // 大區 or 部級主管
        const businessManager: Supervisor | null =
            await this.organizationService.businessSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });

        //總經理
        let generalManager: Supervisor | null = null;
        const depts = await this.deptService.search({
            companyId: eyeQuotationOrderForBPM.companyId,
            code: 'V', //总经理室
        });
        const mainDept = depts.rows.find(
            (dept) => dept.code == 'V', //总经理室
        );
        if (mainDept) {
            generalManager = await this.organizationService.departmentManager({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: mainDept.id,
            });
        }

        const bpmOptions = {
            initiator: [initiator], //起單人
            officeManager: officeManager?.userCode ? [officeManager] : [],
            businessManager: businessManager?.userCode ? [businessManager] : [], //大區主管 or 部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //总经理
        };

        // 打BPM新增
        try {
            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.ScrapTW
                        : EnumBPMForm.ScrapCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: await this.getEyeQuotationOrderById(id),
                    bpmOptions,
                },
                files: [],
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOrder.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async createForRequestOfficialSeal(id: number) {
        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(id);

        const { isGeneralManager } = await this.assignByAmount(
            eyeQuotationOrderForBPM,
        );

        const officeManager = await this.organizationService.directSupervisor({
            companyId: eyeQuotationOrderForBPM.companyId,
            deptId: eyeQuotationOrderForBPM.deptId,
        });

        let regionalManager: Supervisor | null = null;
        regionalManager = await this.organizationService.regionalSupervisor({
            companyId: eyeQuotationOrderForBPM.companyId,
            deptId: eyeQuotationOrderForBPM.deptId,
        });

        // 部級主管
        let businessManager: Supervisor | null = null;
        if (eyeQuotationOrderForBPM.regionId == 2) {
            const depts = await this.deptService.search({
                companyId: eyeQuotationOrderForBPM.companyId,
                code: 'V', //总经理室
            });

            const mainDept = depts.rows.find(
                (dept) => dept.code == 'V', //总经理室
            );

            if (mainDept) {
                businessManager =
                    await this.organizationService.departmentSupervisor({
                        companyId: eyeQuotationOrderForBPM.companyId,
                        deptId: eyeQuotationOrderForBPM.deptId,
                        mainDeptId: mainDept.id,
                    });
            }
        }

        //總經理
        let generalManager: Supervisor | null = null;
        const isAssigned = await this.assignByProfitForGeneralManager(
            eyeQuotationOrderForBPM,
        );
        if (
            eyeQuotationOrderForBPM.regionId == 2 &&
            (isGeneralManager || isAssigned)
        ) {
            generalManager = { deptCode: 'V', userCode: 'E0001-1' };
        }

        const initiator: Supervisor = {
            userCode: eyeQuotationOrderForBPM.user.code,
            deptCode: eyeQuotationOrderForBPM.user.department.code,
        };
        const bpmOptions = {
            initiator, //起單人
            officeManager: officeManager?.userCode ? officeManager : null, //辦事處主管or直屬主管
            regionalManager: regionalManager?.userCode ? [regionalManager] : [], //大區主管
            businessManager: businessManager?.userCode ? [businessManager] : [], //部級主管
            generalManager: generalManager?.userCode ? [generalManager] : [], //總經理
        };

        // 打BPM新增
        try {
            const bpmResult = await this.bpmService.createFormInstance({
                formCode:
                    eyeQuotationOrderForBPM.regionId == 1
                        ? EnumBPMForm.QuotationOfficialSealTW
                        : EnumBPMForm.QuotationOfficialSealCN,
                userCode: eyeQuotationOrderForBPM.user.code,
                createdUserCode: eyeQuotationOrderForBPM.createdUser.code,
                values: {
                    data: eyeQuotationOrderForBPM,
                    bpmOptions,
                },
                files: [],
            });

            return bpmResult;
        } catch (err: any) {
            const message = [
                `[BPM.EyeQuotationOfficialSeal.create]`,
                err.message,
            ].join(' ');
            throw new BaseError(message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async updateStatus(
        instanceId: string,
        status: EnumInstanceStatus,
        commissionAmount?: number,
        eyeQuotationOrderCommissionAmounts?: {
            id: number;
            commissionAmount: number;
        }[],
    ): Promise<EyeQuotationOrder> {
        const eyeQuotationOrderForUpdate =
            await this.eyeQuotationOrderRepo.findOneBy({
                bpmInstanceId: instanceId,
                deleted: false,
            });
        if (!eyeQuotationOrderForUpdate) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }
        const eyeQuotationOrder = await ClinicoDataSource.transaction(
            async (manager) => {
                //報價單狀態
                eyeQuotationOrderForUpdate.status = this.mappingStatus(status);
                //研究費
                if (
                    commissionAmount &&
                    !Big(commissionAmount).eq(
                        eyeQuotationOrderForUpdate.commissionAmount,
                    )
                ) {
                    eyeQuotationOrderForUpdate.commissionAmount = Big(
                        commissionAmount,
                    ).toFixed(Big.DP);
                    eyeQuotationOrderForUpdate.commissionRate = Big(
                        FinanceHelper.calcCommissionRate({
                            amount: eyeQuotationOrderForUpdate.standardAmount,
                            commissionAmount:
                                eyeQuotationOrderForUpdate.commissionAmount,
                        }),
                    ).toFixed(Big.DP);

                    //研究費異動，更新毛利率
                    const cost = Big(eyeQuotationOrderForUpdate.inventoryPrice)
                        .plus(eyeQuotationOrderForUpdate.commissionAmount)
                        .plus(eyeQuotationOrderForUpdate.warrantyPrice)
                        .toFixed(Big.DP);

                    const grossProfitMargin =
                        FinanceHelper.calcGrossProfitMargin({
                            costOfGoodsSoldAmount: cost, // 供应商存货价 + 研究费汇总 + 超期对内保修费
                            revenueAmount:
                                eyeQuotationOrderForUpdate.untaxedAmount, // 未税金额
                        });

                    eyeQuotationOrderForUpdate.grossMargin = grossProfitMargin;
                }

                //研究費項目
                if (
                    eyeQuotationOrderCommissionAmounts &&
                    eyeQuotationOrderCommissionAmounts.length
                ) {
                    for (const eyeQuotationOrderCommissionAmount of eyeQuotationOrderCommissionAmounts) {
                        const eyeQuotationOrderCommissionAmountId = Number(
                            eyeQuotationOrderCommissionAmount?.id,
                        );
                        if (
                            Number.isInteger(
                                eyeQuotationOrderCommissionAmountId,
                            )
                        ) {
                            const commissionAmountForUpdate =
                                await manager.findOneBy(
                                    EyeQuotationOrderCommissionAmount,
                                    {
                                        id: eyeQuotationOrderCommissionAmountId,
                                    },
                                );
                            if (commissionAmountForUpdate) {
                                commissionAmountForUpdate.commissionAmount =
                                    Big(
                                        eyeQuotationOrderCommissionAmount.commissionAmount,
                                    ).toFixed(Big.DP);
                            }
                            await manager.save(commissionAmountForUpdate);
                        }
                    }
                }

                return await manager.save(eyeQuotationOrderForUpdate);
            },
        );

        // Send notification
        if (eyeQuotationOrder.status == EnumEyeQuotationOrderStatus.Approved) {
            await this.notificationService.create({
                type: EnumNotificationType.Message,
                userId: eyeQuotationOrder.userId,
                message: `您好，報價單: ${eyeQuotationOrder.code} 已通過`,
                values: {
                    path: `/quotation/detail?id=${eyeQuotationOrder.id}`,
                },
            });
        }
        if (eyeQuotationOrder.status == EnumEyeQuotationOrderStatus.Canceled) {
            await this.notificationService.create({
                type: EnumNotificationType.Message,
                userId: eyeQuotationOrder.userId,
                message: `您好，報價單: ${eyeQuotationOrder.code} 已取消`,
                values: {
                    path: `/quotation/detail?id=${eyeQuotationOrder.id}`,
                },
            });
        }
        if (eyeQuotationOrder.status == EnumEyeQuotationOrderStatus.Rejected) {
            await this.notificationService.create({
                type: EnumNotificationType.Message,
                userId: eyeQuotationOrder.userId,
                message: `您好，報價單: ${eyeQuotationOrder.code} 已被拒絕`,
                values: {
                    path: `/quotation/detail?id=${eyeQuotationOrder.id}`,
                },
            });
        }

        if (
            [
                EnumEyeQuotationOrderStatus.Approved,
                EnumEyeQuotationOrderStatus.Rejected,
            ].includes(eyeQuotationOrder.status)
        ) {
            const createdUser = await this.userService.findOne(
                eyeQuotationOrder.createdUserId,
            );
            if (createdUser?.email) {
                await this.eyeQuotationOrderMailer.sendFinishedNotice({
                    to: [createdUser.email],
                    eyeQuotationOrderId: eyeQuotationOrder.id,
                    eyeQuotationOrderCode: eyeQuotationOrder.code,
                });
            }
        }

        return eyeQuotationOrder;
    }

    async updateOfficialSealStatus(
        instanceId: string,
        status: EnumInstanceStatus,
    ) {
        const eyeQuotationOrderForUpdate =
            await this.eyeQuotationOrderRepo.findOneBy({
                bpmOfficialSealInstanceId: instanceId,
                deleted: false,
            });
        if (!eyeQuotationOrderForUpdate) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }
        eyeQuotationOrderForUpdate.officialSealStatus =
            this.mappingOfficialSealStatus(status);
        const eyeQuotationOrder = await this.eyeQuotationOrderRepo.save(
            eyeQuotationOrderForUpdate,
        );
    }

    async updateBusinessStatus(instanceId: string) {
        const eyeQuotationOrder = await this.eyeQuotationOrderRepo.findOneBy({
            bpmInstanceId: instanceId,
            deleted: false,
        });

        if (eyeQuotationOrder?.businessId) {
            const business = await this.businessService.findOneOrError(
                eyeQuotationOrder.businessId,
            );

            const statuses = await this.businessStatusService.search({
                type: EnumBusinessStatusType.ClosedInWinning,
                salesTeamGroupIds: [business.salesTeamGroupId],
            });

            await this.businessService.update({
                id: business.id,
                statusId: statuses.rows[0].id,
                updatedUserId: eyeQuotationOrder.createdUserId,
            });
        }
    }

    async updateBPMInstanceId(
        id: number,
        instanceId: string,
    ): Promise<EyeQuotationOrder> {
        const eyeQuotationOrderForUpdate =
            await this.eyeQuotationOrderRepo.findOneBy({
                id: id,
                deleted: false,
            });
        if (!eyeQuotationOrderForUpdate) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }
        eyeQuotationOrderForUpdate.bpmInstanceId = instanceId;
        const eyeQuotationOrder = await this.eyeQuotationOrderRepo.save(
            eyeQuotationOrderForUpdate,
        );

        return eyeQuotationOrder;
    }

    async updateBPMOfficialSealInstanceId(
        id: number,
        instanceId: string,
    ): Promise<EyeQuotationOrder> {
        const eyeQuotationOrderForUpdate =
            await this.eyeQuotationOrderRepo.findOneBy({
                id: id,
                deleted: false,
            });
        if (!eyeQuotationOrderForUpdate) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }
        (eyeQuotationOrderForUpdate.officialSealStatus =
            EnumEyeQuotationOrderOfficialSealStatus.Processing),
            (eyeQuotationOrderForUpdate.bpmOfficialSealInstanceId = instanceId);
        const eyeQuotationOrder = await this.eyeQuotationOrderRepo.save(
            eyeQuotationOrderForUpdate,
        );

        return eyeQuotationOrder;
    }

    private mappingStatus(
        InstanceStatus: EnumInstanceStatus,
    ): EnumEyeQuotationOrderStatus {
        switch (InstanceStatus) {
            case EnumInstanceStatus.Canceled:
                return EnumEyeQuotationOrderStatus.Canceled;
            case EnumInstanceStatus.Rejected:
                return EnumEyeQuotationOrderStatus.Rejected;
            case EnumInstanceStatus.Completed:
                return EnumEyeQuotationOrderStatus.Approved;
            case EnumInstanceStatus.Waiting:
            case EnumInstanceStatus.Reconsidered:
            default:
                return EnumEyeQuotationOrderStatus.Processing;
        }
    }

    private mappingOfficialSealStatus(
        InstanceStatus: EnumInstanceStatus,
    ): EnumEyeQuotationOrderOfficialSealStatus {
        switch (InstanceStatus) {
            case EnumInstanceStatus.Canceled:
                return EnumEyeQuotationOrderOfficialSealStatus.Canceled;
            case EnumInstanceStatus.Rejected:
                return EnumEyeQuotationOrderOfficialSealStatus.Rejected;
            case EnumInstanceStatus.Completed:
                return EnumEyeQuotationOrderOfficialSealStatus.Approved;
            case EnumInstanceStatus.Waiting:
            case EnumInstanceStatus.Reconsidered:
            default:
                return EnumEyeQuotationOrderOfficialSealStatus.Processing;
        }
    }

    /**
    1. 事業部主管
    以下條件滿足一項即成立
    - 客戶為「經銷商」
    - 訂單金額 > 50 萬
    - 折扣率 < 60%
    - LS (研究費) > 0 元

    2. 財務主管
    以下條件滿足一項即成立
    - 訂單金額 > 50 萬
    - 折扣率 < 55%
    - 毛利率 < 30% (暫先不理會)
    - LS (研究費) 佔比 > 5%
    
    3. 總經理
    以下條件滿足一項即成立
    - 折扣率 < 50%
    - 毛利 < 25%
    - LS (研究費) 佔比 > 10%
    */
    private async assignByAmount(eyeQuotationOrderForBPM: EyeQuotationOrder) {
        let customQuotationPrice = Big('0');
        let regionalManagerPrice = Big('0');
        let businessManagerPrice = Big('0');
        let generalManagerPrice = Big('0');

        // 有組配時，才判斷價格區間
        if (eyeQuotationOrderForBPM.eyeQuotationOrderProducts.length) {
            // 商品
            for (const data of eyeQuotationOrderForBPM.eyeQuotationOrderProducts) {
                customQuotationPrice = Big(customQuotationPrice)
                    .add(data.customQuotationPrice ?? 0)
                    .times(data.qty);

                regionalManagerPrice = Big(regionalManagerPrice)
                    .add(data.eyeProduct?.regionalManagerPrice ?? 0)
                    .times(data.qty);

                businessManagerPrice = Big(
                    Big(businessManagerPrice).add(
                        data.eyeProduct?.businessManagerPrice ?? 0,
                    ),
                ).times(data.qty);

                generalManagerPrice = Big(
                    Big(generalManagerPrice).add(
                        data.eyeProduct?.generalManagerPrice ?? 0,
                    ),
                ).times(data.qty);

                // 其中一任一組配符合就要簽核
                return {
                    /** 低於businessManagerPrice需給總經理簽核
                     * - businessManagerPrice ~ generalManagerPrice
                     * - less generalManagerPrice
                     */
                    isGeneralManager:
                        customQuotationPrice.lt(businessManagerPrice),
                };
            }
        }

        return {
            isGeneralManager: false,
        };
    }

    //判斷是否簽核至財務主管 #92520 #103681
    //訂單金額 > 50 萬
    //折扣率 < 55% (移除)
    //毛利率 < 30%
    //LS 費用率 > 5 %
    private async assignByProfitForFinanceManager(
        eyeQuotationOrderForBPM: EyeQuotationOrder,
    ): Promise<boolean> {
        const realAmount = eyeQuotationOrderForBPM.realAmount; //訂單金額
        // const discountRate = eyeQuotationOrderForBPM.discountRate; //折扣率
        const grossProfitMargin = eyeQuotationOrderForBPM.grossMargin; //毛利率
        const lsRatio = FinanceHelper.calcCommissionRate({
            amount: realAmount,
            commissionAmount: eyeQuotationOrderForBPM.commissionAmount,
        }); //LS 費用率

        const isSkip = this.checkSkipEyeQuotationOrderForZero(
            eyeQuotationOrderForBPM,
        );

        if (
            Big(realAmount).gte(500000) || // 訂單金額 > 50 萬
            (!isSkip && Big(grossProfitMargin).lte(0.3)) || // 毛利率 < 30% & 毛利率 > 0
            Big(lsRatio).gte(0.05) // LS 費用率 > 5 %
        ) {
            return true;
        }

        return false;
    }

    //判斷是否簽核至總經理
    //折扣率 < 50% (移除)
    //毛利 < 25%
    //LS 費用率 > 10 %
    private async assignByProfitForGeneralManager(
        eyeQuotationOrderForBPM: EyeQuotationOrder,
    ): Promise<boolean> {
        const realAmount = eyeQuotationOrderForBPM.realAmount; //訂單金額
        const grossProfitMargin = eyeQuotationOrderForBPM.grossMargin; //毛利率
        const lsRatio = FinanceHelper.calcCommissionRate({
            amount: realAmount,
            commissionAmount: eyeQuotationOrderForBPM.commissionAmount,
        }); //LS 費用率

        const isSkip = this.checkSkipEyeQuotationOrderForZero(
            eyeQuotationOrderForBPM,
        );

        if (
            Big(lsRatio).gte(0.1) || // LS 費用率 > 10%
            (!isSkip && Big(grossProfitMargin).lte(0.25)) // 毛利 < 25% & 毛利 > 0
        ) {
            return true;
        }

        return false;
    }

    // true:忽略
    // false:簽核
    private checkSkipEyeQuotationOrderForZero(
        eyeQuotationOrderForBPM: EyeQuotationOrder,
    ) {
        // 单类型为【一般维修】和【飞秒维修】＆【总单价格=0】
        if (
            [
                EnumEyeQuotationOrderType.Repair,
                EnumEyeQuotationOrderType.FemtosecondRepair,
            ].includes(eyeQuotationOrderForBPM.eyeQuotationOrderType.code) &&
            Big(eyeQuotationOrderForBPM.realAmount).eq(0)
        ) {
            return true;
        }
        return false;
    }

    // 判斷毛利率是否簽核
    async assignByGrossMargin(params: WebhookParams): Promise<WebhookResult> {
        const bpmOptions = params.values?.bpmOptions;
        const appendFormData = params?.appendFormData;

        if (!appendFormData?.editor?.commissionAmount) {
            return { appendFormData: {} };
        }

        const eyeQuotationOrderForUpdate =
            await this.eyeQuotationOrderRepo.findOneByOrFail({
                bpmInstanceId: params.formInstanceId.toString(),
                deleted: false,
            });

        // 生成BPM專用的報價單資料
        const eyeQuotationOrderForBPM = await this.findOne(
            eyeQuotationOrderForUpdate.id,
        );

        //毛利率: 銷售相關報價才需要計算毛利率
        if (
            !eyeQuotationOrderForBPM?.eyeQuotationOrderType?.code &&
            [
                EnumEyeQuotationOrderType.Sale,
                EnumEyeQuotationOrderType.FemtosecondSale,
                EnumEyeQuotationOrderType.Repair,
                EnumEyeQuotationOrderType.FemtosecondRepair,
            ].includes(eyeQuotationOrderForBPM?.eyeQuotationOrderType?.code)
        ) {
            return { appendFormData: {} };
        }

        eyeQuotationOrderForBPM.commissionAmount = Big(
            appendFormData.editor.commissionAmount,
        ).toFixed(Big.DP);

        const { isGeneralManager } = await this.assignByAmount(
            eyeQuotationOrderForBPM,
        );

        const cost = Big(eyeQuotationOrderForBPM.inventoryPrice)
            .plus(eyeQuotationOrderForBPM.commissionAmount)
            .plus(eyeQuotationOrderForBPM.warrantyPrice)
            .toFixed(Big.DP);

        //毛利率
        const grossProfitMargin = FinanceHelper.calcGrossProfitMargin({
            costOfGoodsSoldAmount: cost, // 供应商存货价 + 研究费汇总 + 超期对内保修费
            revenueAmount: eyeQuotationOrderForBPM.untaxedAmount, // 未税金额
        });

        eyeQuotationOrderForBPM.grossMargin = grossProfitMargin;

        //總經理
        let generalManager: Supervisor | null = null;
        const isAssigned = await this.assignByProfitForGeneralManager(
            eyeQuotationOrderForBPM,
        );
        if (
            eyeQuotationOrderForBPM.regionId == 2 &&
            (isGeneralManager || isAssigned)
        ) {
            generalManager = { deptCode: 'V', userCode: 'E0001-1' };
        }

        // 財務主管
        let financeManager: Supervisor | null = null;
        const isFinanceManager = await this.assignByProfitForFinanceManager(
            eyeQuotationOrderForBPM,
        );
        if (isFinanceManager) {
            financeManager = { deptCode: 'A', userCode: 'L1584-1' };
        }

        return {
            appendFormData: {
                isCEO: financeManager || generalManager ? true : false,
                isGeneralManager: isGeneralManager || undefined,
                bpmOptions: {
                    ...bpmOptions,
                    generalManager: generalManager ? [generalManager] : [],
                    financeManager: financeManager ? [financeManager] : [],
                },
            },
        };
    }

    private async getEyeQuotationOrderById(id: number) {
        const query = gql`
        {
          eyeQuotationOrderInternal (id:${id}) {
            onlyMaterials
            city {
                id
                name
                __typename
            }
            businessId
            district {
                id
                name
                __typename
            }
            eyeQuotationOrderBusinesses {
                business {
                id
                code
                title
                businessesUsers {
                    id
                    user {
                    id
                    name
                    __typename
                    }
                    salesTeam {
                    id
                    name
                    __typename
                    }
                    __typename
                }
                __typename
                }
                __typename
            }
            creditPeriod {
                id
                name
                __typename
            }
            eyeQuotationOrderType {
                code
                name
                id
                __typename
            }
            bpmUrl
            localStandardAmount
            officialSealBpmUrl
            officialSealStatus
            id
            code
            userId
            customerId
            invoicingCustomerId
            eyeServiceOrderId
            bpmInstanceId
            exchangeRate
            warrantyMonths
            warrantyPeriodTypeId
            expectDeliveryDate
            expectPaymentDate
            warrantyBuyType
            orderCode
            realAmount
            inventoryPrice
            untaxedAmount
            contactPerson
            contactPhone
            regionId
            status
            address
            currencyId
            standardAmount
            discountRate
            discountAmount
            extraDiscountAmount
            recommendedAmount
            realDiscountAmount
            realDiscountRate
            taxRate
            commissionAmount
            discountRateWithoutCommission
            commissionRate
            grossMargin
            paymentInfo
            status
            description
            createdAt
            status
            createdAt
            eyeQuotationOrderCommissionAmounts {
                id
                commissionAmount
                eyeQuotationOrderCommissionType {
                    id
                    name
                    __typename
                }
                __typename
            }
            eyeQuotationOrderCommissionType {
                id
                name
                __typename
            }
            region {
                id
                name
                localCurrency {
                id
                name
                __typename
                }
                __typename
            }
            eyeQuotationOrderProductItems {
                id
                customQuotationPrice
                materialId
                materialCode
                materialModel
                materialSpec
                materialUnit
                materialName
                qty
                unitPrice
                unitPriceVat
                eyeQuotationOrderProductId
                eyeQuotationOrderPromotionId
                discountedSellingPrice
                materialSellingPrice
                materialSellingCurrencyId
                materialCostPrice
                materialCostCurrency {
                id
                code
                name
                __typename
                }
                materialInventoryPrice
                materialInventoryCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialCleanPrice
                materialCleanCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialOptionSalePrice
                materialOptionSaleCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialBundleSalePrice
                materialBundleSaleCurrency {
                    id
                    code
                    name
                    __typename
                }
                warrantyMonths
                warrantyPeriodTypeId
                eyeWarrantyPeriodType {
                name
                id
                __typename
                }
                material {
                minimumOrderQuantity
                optionSalePrice
                bundleSalePrice
                costPrice
                costCurrency {
                    id
                    code
                    name
                    __typename
                }
                type {
                    id
                    name
                    code
                    __typename
                }
                images {
                    url
                    name
                    __typename
                }
                __typename
                }
                __typename
            }
            eyeServiceOrder {
                id
                code
                __typename
            }
            eyeQuotationOrderProductItemWarrantyPrices {
                internalWarrantyCurrency {
                id
                code
                name
                __typename
                }
                internalWarrantyPrice
                id
                eyeQuotationOrderProductItemId
                __typename
            }
            customer {
                id
                code
                name
                __typename
            }
            currency {
                id
                name
                code
                __typename
            }
            dept {
                id
                code
                name
                costCenter {
                id
                name
                code
                __typename
                }
                company {
                id
                name
                __typename
                }
                __typename
            }
            costCenter {
                id
                name
                code
                __typename
            }
            financialCompany {
                id
                name
                code
                __typename
            }
            invoicingCustomer {
                id
                code
                name
                __typename
            }
            eyeServiceOrder {
                eyeWarrantyContract {
                approvalCode
                __typename
                }
                __typename
            }
            createdUser {
                id
                code
                name
                __typename
            }
            user {
                id
                code
                name
                __typename
            }
            eyeQuotationOrderProducts {
                id
                name
                qty
                model
                brand
                unitPriceVat
                customQuotationPrice
                eyeProduct {
                id
                eyeProductType {
                    id
                    regionId
                    name
                    createdAt
                    updatedAt
                    __typename
                }
                name
                salePrice
                status
                description
                currencyId
                model
                regionId
                brand
                createdAt
                updatedAt
                attachments {
                    url
                    name
                    __typename
                }
                generalManagerPrice
                dsmPrice
                regionalManagerPrice
                businessManagerPrice
                eyeProductTypeId
                eyeProductItems {
                    id
                    eyeProductId
                    eyeProductItemTypeId
                    eyeProductItemType {
                    name
                    __typename
                    }
                    qty
                    isOptional
                    createdAt
                    material {
                    id
                    code
                    name
                    model
                    spec
                    unit
                    salePrice
                    costPrice
                    type {
                        id
                        name
                        code
                        __typename
                    }
                    customersConsumablePrice {
                        cleanPrice
                        id
                        material {
                        id
                        name
                        code
                        __typename
                        }
                        __typename
                    }
                    externalWarrantyCurrency {
                        id
                        name
                        code
                        __typename
                    }
                    externalWarrantyPrice
                    costCurrency {
                        id
                        code
                        name
                        __typename
                    }
                    internalWarrantyCurrency {
                        id
                        code
                        name
                        __typename
                    }
                    internalWarrantyPrice
                    optionSaleCurrency {
                        id
                        code
                        name
                        __typename
                    }
                    optionSalePrice
                    bundleSalePrice
                    warrantyPeriodTypeId
                    eyeWarrantyPeriodType {
                        name
                        __typename
                    }
                    images {
                        url
                        name
                        __typename
                    }
                    countingUnit {
                        id
                        name
                        __typename
                    }
                    __typename
                    }
                    __typename
                }
                eyePromotions {
                    id
                    name
                    isAddon
                    discountRate
                    discountAmount
                    createdAt
                    date1
                    regionId
                    updatedAt
                    eyePromotionAddonProductItems {
                    id
                    qty
                    isOptional
                    material {
                        id
                        unit
                        __typename
                    }
                    __typename
                    }
                    __typename
                }
                __typename
                }
                items {
                id
                customQuotationPrice
                materialName
                materialCode
                unitPriceVat
                materialUnit
                materialSellingPrice
                materialCostPrice
                materialCostCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialInventoryPrice
                materialInventoryCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialCleanPrice
                materialCleanCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialOptionSalePrice
                materialOptionSaleCurrency {
                    id
                    code
                    name
                    __typename
                }
                materialBundleSalePrice
                materialBundleSaleCurrency {
                    id
                    code
                    name
                    __typename
                }
                discountedSellingPrice
                qty
                unitQty
                warrantyMonths
                warrantyPeriodTypeId
                eyeWarrantyPeriodType {
                    name
                    __typename
                }
                eyeProductItemId
                eyeProductItem {
                    isOptional
                    material {
                    minimumOrderQuantity
                    costPrice
                    optionSalePrice
                    bundleSalePrice
                    costCurrency {
                        id
                        name
                        code
                        __typename
                    }
                    type {
                        id
                        name
                        code
                        __typename
                    }
                    images {
                        url
                        name
                        __typename
                    }
                    __typename
                    }
                    __typename
                }
                __typename
                }
                __typename
            }
            eyeQuotationOrderPromotions {
                id
                name
                eyePromotionId
                discountRate
                discountAmount
                currencyId
                eyePromotion {
                id
                name
                isAddon
                discountRate
                discountAmount
                currencyId
                eyePromotionAddonProductItems {
                    id
                    qty
                    isOptional
                    material {
                    id
                    name
                    unit
                    __typename
                    }
                    __typename
                }
                __typename
                }
                items {
                id
                materialName
                materialCode
                unitPriceVat
                materialUnit
                eyePromotionAddonProductItemId
                qty
                __typename
                }
                __typename
            }
            eyeQuotationOrderWarrantyItems {
                id
                eyeQuotationOrderId
                materialId
                materialCode
                materialModel
                materialSpec
                materialUnit
                materialName
                sn
                udi
                qty
                memo
                __typename
            }
            user {
                name
                __typename
            }
            eyeWarrantyPeriodType {
                id
                code
                name
                __typename
            }
            biddingPrice
            __typename
            }
        }`;

        const endpoint = `http://localhost:${process.env.APP_PORT}/graphql`;
        const client = new GraphQLClient(endpoint);
        const data: any = await client.request(query);
        return data.eyeQuotationOrderInternal;
    }

    private async getSecretaries(
        eyeQuotationOrderForBPM: EyeQuotationOrder,
    ): Promise<Supervisor[]> {
        switch (eyeQuotationOrderForBPM.eyeQuotationOrderType.code) {
            case EnumEyeQuotationOrderType.Repair:
            case EnumEyeQuotationOrderType.FemtosecondRepair:
                return [
                    {
                        userCode: 'C0938',
                        deptCode: 'E-A-1',
                    },
                ];
            case EnumEyeQuotationOrderType.Sale:
            case EnumEyeQuotationOrderType.FemtosecondSale:
                return await this.getSalesSecretaries(eyeQuotationOrderForBPM);
        }
        return [];
    }

    private async getSalesSecretaries(
        eyeQuotationOrderForBPM: EyeQuotationOrder,
    ): Promise<Supervisor[]> {
        const regionalManager =
            await this.organizationService.regionalSupervisor({
                companyId: eyeQuotationOrderForBPM.companyId,
                deptId: eyeQuotationOrderForBPM.deptId,
            });

        switch (regionalManager?.deptCode) {
            case 'E-R-HB1': //眼科-华北一区
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C0418',
                    },
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'Z0061',
                    },
                ];
            case 'E-R-HB2': //眼科-华北二区
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C0325',
                    },
                ];
            case 'E-R-HD': //眼科-华东区
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C0393',
                    },
                ];
            case 'E-R-HN1': //眼科-华南区
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C0512',
                    },
                ];
            case 'E-R-XB': //眼科-西北区
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C1058',
                    },
                ];
            case 'E-R-XN': //眼科-西南区
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C0325',
                    },
                ];
            case 'E0JK': //眼科-眼健康
                return [
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'C0418',
                    },
                    {
                        deptCode: regionalManager.deptCode,
                        userCode: 'Z0061',
                    },
                ];
            case 'E-KAM': //眼科-大客户管理处
            case 'M-20-1': //手术设备事业部-销售处
            case 'M-20-1-1': //手术设备事业部-销售处-东区
            case 'M-20-1-2': //手术设备事业部-销售处-西区
            case 'M-20-1-3': //手术设备事业部-销售处-南区
            case 'M-20-1-4': //手术设备事业部-销售处-北区
                return [
                    {
                        deptCode: 'HR-5',
                        userCode: 'C1319',
                    },
                ];
        }
        return [];
    }
}
