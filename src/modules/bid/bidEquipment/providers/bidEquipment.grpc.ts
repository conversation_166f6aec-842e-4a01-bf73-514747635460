import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import moment from 'moment';
import Container from 'typedi';
import {
    CreateParams,
    UpdateParams,
    CreateResult,
    UpdateResult,
} from '../types/bidEquipment.grpc.type';
import { DeleteParams } from '../types/bidEquipment.type';
import { BidEquipmentService } from './bidEquipment.service';
import { BidEquipmentRepository } from './bidEquipment.repository';

const bidEquipmentService = Container.get(BidEquipmentService);
const bidEquipmentRepo = Container.get(BidEquipmentRepository);
export const BidEquipment = {
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;
        const bidEquipment = await bidEquipmentService.create({
            ...params,
        });
        ctx.res = Helpers.Json.success(<CreateResult>{
            id: bidEquipment.id,
        });
    },
    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const bidEquipment = await bidEquipmentService.update({
            ...params,
            code: params.code ?? <any>null,
            name: params.name ?? <any>null,
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{
            id: bidEquipment.id,
        });
    },

    delete: async (ctx: Context<any>) => {
        const params = <DeleteParams>ctx.req;
        await bidEquipmentRepo.delete(params);
        ctx.res = Helpers.Json.success();
    },
};
