import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    FieldResolver,
    Mutation,
    Resolver,
    Root,
    Query,
    ID,
} from 'type-graphql';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { UserService } from '@/modules/user/providers/user.service';
import { BidEquipmentService } from './bidEquipment.service';
import { AuthorizationProduct as BidEquipment } from '@/common/graphql/model/impl/salesRepWorkstation/authorizationProduct.impl';
import {
    BidEquipmentCreateInput,
    BidEquipmentSearchArgs,
    BidEquipmentSearchResult,
    BidEquipmentUpdateInput,
} from '../types/bidEquipment.gql.type';
import { BidEquipmentRepository } from './bidEquipment.repository';

@Service()
@Resolver((of) => BidEquipment)
export class BidEquipmentResolver {
    @Inject()
    private bidEquipmentRepo: BidEquipmentRepository;
    @Inject()
    private bidEquipmentService: BidEquipmentService;
    @Inject()
    private userService: UserService;

    @UserAuthInterceptor()
    @Query(() => BidEquipmentSearchResult)
    async bidEquipments(
        @Args() params: BidEquipmentSearchArgs,
    ): Promise<BidEquipmentSearchResult> {
        const result = await this.bidEquipmentRepo.search({
            ...params.filters,
            ...params,
        });
        return <BidEquipmentSearchResult>result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BidEquipment)
    async createBidEquipment(
        @Arg('input') input: BidEquipmentCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BidEquipment> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidEquipmentService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BidEquipment)
    async updateBidEquipment(
        @Arg('input') input: BidEquipmentUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BidEquipment> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidEquipmentService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => Boolean)
    async deleteBidEquipment(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidEquipmentRepo.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }
}
