import { Inject, Service } from 'typedi';
import {
    Between,
    FindOptionsRelations,
    FindOptionsWhere,
    ILike,
    In,
    LessThanOrEqual,
    MoreThanOrEqual,
} from 'typeorm';
import Joi from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import {
    CreateParams,
    UpdateParams,
    SearchParams,
    DeleteParams,
} from '../types/bidEquipment.type';
import { CommonSearchResult } from '@/common/types/common.type';
import { AuthorizationProduct as BidEquipment } from '@clinico/typeorm-persistence/models/salesRepWorkstation/authorizationProduct.model';

@Service()
export class BidEquipmentRepository extends CommonService<BidEquipment> {
    private bidEquipmentRepo = ClinicoDataSource.getRepository(BidEquipment);

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<BidEquipment>> {
        const filters: FindOptionsWhere<BidEquipment> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.code) {
            filters.code = ILike(`%${params.code}%`);
        }

        if (params.name) {
            filters.name = ILike(`%${params.name}%`);
        }

        filters.deleted = false;
        const data = await this.bidEquipmentRepo.findAndCount({
            where: filters,
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(params: CreateParams): Promise<BidEquipment> {
        const bidEquipmentToCreate = this.bidEquipmentRepo.create({
            ...params,
        });
        await this.validate(bidEquipmentToCreate);
        const bidEquipment = await this.bidEquipmentRepo.save(
            bidEquipmentToCreate,
        );

        return bidEquipment;
    }

    async update(params: UpdateParams): Promise<BidEquipment> {
        const bidEquipmentToUpdate = await this.findOneOrError(params.id);
        const bidEquipment = await this.bidEquipmentRepo.save({
            ...bidEquipmentToUpdate,
            ...params,
            id: bidEquipmentToUpdate.id,
        });
        return bidEquipment;
    }

    async delete(params: DeleteParams): Promise<void> {
        const bidEquipmentToDelete = await this.findOneOrError(params.id);
        bidEquipmentToDelete.deleted = true;
        await this.bidEquipmentRepo.save(bidEquipmentToDelete);
    }

    async validate(bidEquipment: BidEquipment): Promise<void> {
        const schema = Joi.object<BidEquipment>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(bidEquipment, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
