import { Inject, Service } from 'typedi';
import Jo<PERSON> from 'joi';
import { BaseError } from '@clinico/base-error';
import { CreateParams, UpdateParams } from '../types/bidEquipment.type';
import { BidEquipmentRepository } from './bidEquipment.repository';
import { AuthorizationProduct as BidEquipment } from '@clinico/typeorm-persistence/models/salesRepWorkstation/authorizationProduct.model';

@Service()
export class BidEquipmentService {
    @Inject()
    private bidEquipmentRepo: BidEquipmentRepository;

    async create(params: CreateParams): Promise<BidEquipment> {
        const bidEquipmentToCreate = await this.bidEquipmentRepo.create({
            ...params,
        });

        return bidEquipmentToCreate;
    }

    async update(params: UpdateParams): Promise<BidEquipment> {
        const bidEquipment = await this.bidEquipmentRepo.update({
            ...params,
        });
        return bidEquipment;
    }

    async validate(bidEquipment: BidEquipment): Promise<void> {
        const schema = Joi.object<BidEquipment>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(bidEquipment, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
