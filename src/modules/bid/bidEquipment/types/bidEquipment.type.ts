import {
    CommonSearchParams,
    CommonCreateParams,
    CommonUpdateParams,
    CommonDeletedParams,
} from '@/common/types/common.type';

export type SearchParams = CommonSearchParams & {
    code?: string;
    name?: string;
};
export type CreateParams = CommonCreateParams & {
    code?: string;
    name?: string;
};

export type UpdateParams = CommonUpdateParams & {
    code?: string;
    name?: string;
};

export type DeleteParams = CommonDeletedParams;
