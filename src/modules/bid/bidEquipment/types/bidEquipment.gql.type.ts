import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { AuthorizationProduct as BidEquipment } from '@/common/graphql/model/impl/salesRepWorkstation/authorizationProduct.impl';

@InputType()
class BidEquipmentSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field({ nullable: true })
    code?: string;

    @Field({ nullable: true })
    name?: string;
}

@ArgsType()
export class BidEquipmentSearchArgs extends CommonSearchArgs(
    BidEquipmentSearchInput,
) {}

@InputType()
export class BidEquipmentCreateInput {
    @Field({ nullable: true })
    code?: string;

    @Field()
    name?: string;
}

@InputType()
export class BidEquipmentUpdateInput {
    @Field((type) => ID, { nullable: true })
    id: number;

    @Field({ nullable: true })
    code?: string;

    @Field({ nullable: true })
    name?: string;
}

@ObjectType()
export class BidEquipmentSearchResult extends PaginatedSearchResult(
    BidEquipment,
) {}
