import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    ObjectType,
} from 'type-graphql';
import { DateResolver } from 'graphql-scalars';
import {
    AttachmentCreateInput,
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { Bid } from '@/common/graphql/model/impl/salesRepWorkstation/bid.impl';
import { EnumBidResult } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/bid.model';

@InputType()
class BidSearchInput {
    @Field((type) => ID, { nullable: true })
    id: number;

    @Field((type) => ID, { nullable: true, description: '客戶' })
    customerId: number;

    @Field({ nullable: true, description: '客戶code' })
    customerCode?: string;

    @Field({ nullable: true, description: '客戶名稱' })
    customerName?: string;

    @Field({ nullable: true, description: '編碼' })
    code?: string;

    @Field({ nullable: true, description: '項目名稱' })
    name: string;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '投標時間(起)',
    })
    bidDate1?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '投標時間(迄)',
    })
    bidDate2?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '發佈時間(起)',
    })
    publishDate1: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '發佈時間(迄)',
    })
    publishDate2: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '報名開始時間(起)',
    })
    registrationDate1: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '報名開始時間(迄)',
    })
    registrationDate2: Date;

    @Field({ nullable: true, description: '公開資訊連結' })
    publicLinkUrl: string;

    @Field({ nullable: true, description: '項目內容' })
    content: string;

    @Field((type) => EnumBidResult, { nullable: true, description: '結果' })
    result: EnumBidResult;

    @Field({ nullable: true, description: '備註' })
    notes: string;
    // relations
    @Field((type) => [AttachmentCreateInput], { nullable: true })
    attachments?: AttachmentCreateInput[];

    @Field((type) => [ID], { nullable: true, description: '投標設備' })
    equipmentIds?: number[];
}

@ArgsType()
export class BidSearchArgs extends CommonSearchArgs(BidSearchInput) {}

@InputType()
export class BidCreateInput {
    @Field((type) => ID)
    regionId: number;

    @Field((type) => ID, { nullable: false, description: '客戶' })
    customerId: number;

    @Field({ nullable: false, description: '項目名稱' })
    name: string;

    @Field((type) => DateResolver, {
        nullable: false,
        description: '投標開始時間',
    })
    bidDate: Date;

    @Field((type) => DateResolver, { nullable: true, description: '發佈時間' })
    publishDate: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '報名開始時間',
    })
    registrationDate: Date;

    @Field({ nullable: true, description: '公開資訊連結' })
    publicLinkUrl: string;

    @Field({ nullable: false, description: '項目內容' })
    content: string;

    @Field({ description: '結果' })
    result: EnumBidResult;

    @Field({ nullable: true, description: '備註' })
    notes: string;
    // relations
    @Field((type) => [AttachmentCreateInput], { nullable: true })
    attachments?: AttachmentCreateInput[];

    @Field((type) => [ID], { nullable: true, description: '投標設備' })
    equipmentIds?: number[];
}

@InputType()
export class BidUpdateInput {
    @Field((type) => ID)
    id: number;

    @Field((type) => ID, { nullable: true, description: '客戶' })
    customerId?: number;

    @Field({ nullable: true, description: '項目名稱' })
    name?: string;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '投標開始時間',
    })
    bidDate?: Date;

    @Field((type) => DateResolver, { nullable: true, description: '發佈時間' })
    publishDate?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '報名開始時間',
    })
    registrationDate?: Date;

    @Field({ nullable: true, description: '公開資訊連結' })
    publicLinkUrl?: string;

    @Field({ nullable: true, description: '項目內容' })
    content?: string;

    @Field({ description: '結果' })
    result?: EnumBidResult;

    @Field({ nullable: true, description: '備註' })
    notes?: string;

    @Field((type) => [ID], { nullable: true, description: '投標設備' })
    equipmentIds?: number[];
}

@ObjectType()
export class BidSearchResult extends PaginatedSearchResult(Bid) {}
