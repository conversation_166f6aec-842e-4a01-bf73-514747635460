import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import {
    CommonSearchParams,
    CommonCreateParams,
    CommonUpdateParams,
    CommonDeletedParams,
} from '@/common/types/common.type';
import { EnumBidResult } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bid.model';
import { FileUpload } from '@clinico/graphql-upload';

export type SearchParams = CommonSearchParams & {
    customerId?: number;
    customerName?: string;
    customerCode?: string;
    code?: string;
    name?: string;
    bidDate1?: Date;
    bidDate2?: Date;
    publishDate1?: Date;
    publishDate2?: Date;
    registrationDate1?: Date;
    registrationDate2?: Date;
    publicLinkUrl?: string;
    content?: string;
    result?: EnumBidResult;
    notes?: string;
    equipmentIds?: number[];
};

export type CreateParams = CommonCreateParams & {
    regionId: number;
    customerId: number;
    name?: string;
    bidDate?: Date;
    publishDate?: Date;
    registrationDate?: Date;
    publicLinkUrl?: string;
    content?: string;
    result?: EnumBidResult;
    notes?: string;
    // relations
    attachments?: {
        file: Promise<FileUpload> | GRPCFileUpload;
        name: string;
    }[];
    equipmentIds?: number[];
};

export type CreateDataParams = CommonCreateParams & {
    regionId: number;
    customerId: number;
    code?: string;
    name?: string;
    bidDate?: Date;
    publishDate?: Date;
    registrationDate?: Date;
    publicLinkUrl?: string;
    content?: string;
    result?: EnumBidResult;
    notes?: string;
    // relations
    attachments?: {
        file: Promise<FileUpload> | GRPCFileUpload;
        name: string;
    }[];
    equipmentIds?: number[];
};

export type UpdateParams = CommonUpdateParams & {
    customerId?: number;
    name?: string;
    bidDate?: Date;
    publishDate?: Date;
    registrationDate?: Date;
    publicLinkUrl?: string;
    content?: string;
    result?: EnumBidResult;
    notes?: string;
    // relations
    equipmentIds?: number[];
};

export type DeleteParams = CommonDeletedParams;
