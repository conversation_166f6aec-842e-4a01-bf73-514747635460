import { CommonUpdateParams } from '@/common/types/common.type';
import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import { FileUpload } from '@clinico/graphql-upload';
import { Timestamp } from '@/common/gens/google/protobuf/timestamp';
import { EnumBidResult } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bid.model';
export type CreateParams = {
    createdUserId: number;
    regionId: number;
    customerId: number;
    name?: string;
    bidDate: Timestamp;
    publishDate?: Timestamp;
    registrationDate?: Timestamp;
    publicLinkUrl?: string;
    content: string;
    result: EnumBidResult;
    notes?: string;
    // relations
    attachments?: {
        file: Promise<FileUpload> | GRPCFileUpload;
        name: string;
    }[];
    equipmentIds?: number[];
};

export type UpdateParams = CommonUpdateParams & {
    customerId?: number;
    name?: string;
    bidDate?: Timestamp;
    publishDate?: Timestamp;
    registrationDate?: Timestamp;
    publicLinkUrl?: string;
    content?: string;
    result?: EnumBidResult;
    notes?: string;
    // relations
    equipmentIds?: number[];
};

export type DeleteParams = {
    id: number;
    deletedUserId: number;
};

export type CreateResult = {
    id: number;
};

export type UpdateResult = {
    id: number;
};
