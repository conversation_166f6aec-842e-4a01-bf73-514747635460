import { Inject, Service } from 'typedi';
import { EntityManager } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CreateParams, UpdateParams } from '../types/bid.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import _ from 'lodash';
import { CodeService } from '@/modules/code/providers/code.service';
import { EnumCodeType } from '@/modules/code/types/code.interface';
import { Bid } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bid.model';
import { BidRepository } from './bid.repository';
import { BidsEquipment } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bidsEquipment.model';
import { BidAttachmentService } from '../../bidAttachment/providers/bidAttachment.service';

@Service()
export class BidService {
    @Inject()
    private codeService: CodeService;
    @Inject()
    private bidRepo: BidRepository;
    @Inject()
    private bidAttachmentService: BidAttachmentService;

    async create(params: CreateParams): Promise<Bid> {
        const bidToCreate = await this.bidRepo.create({
            ...params,
        });

        const id = await ClinicoDataSource.transaction(async (manager) => {
            // Generate Code
            const attorneyCode = await this.codeService.nextCode(
                {
                    type: EnumCodeType.Bid,
                    regionId: params.regionId,
                },
                manager,
            );

            bidToCreate.code = attorneyCode;
            //附件
            if (params.attachments) {
                for (const attachment of params.attachments) {
                    await this.bidAttachmentService.create({
                        ...attachment,
                        bidId: bidToCreate.id,
                        regionId: params.regionId,
                        createdUserId: params.createdUserId,
                    });
                }
            }

            const bid = await manager.save(bidToCreate);
            await this.mutate(manager, bid, params);
            await this.validate(bid, manager);
            return bid.id;
        });
        return await this.bidRepo.findOneOrError(id);
    }

    async update(params: UpdateParams): Promise<Bid> {
        const existedBid = await this.bidRepo.findOneOrError(params.id);
        const updatedBid = await this.bidRepo.update({
            ...params,
            id: existedBid.id,
        });
        const id = await ClinicoDataSource.transaction(async (manager) => {
            await this.mutate(manager, updatedBid, params);
            return updatedBid.id;
        });
        return await this.bidRepo.findOneOrError(id);
    }

    async validate(bid: Bid, manager: EntityManager): Promise<void> {
        const schema = Joi.object<Bid>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(bid, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }

    async mutate(
        manager: EntityManager,
        bid: Bid,
        params: CreateParams | UpdateParams,
    ) {
        // 投標設備關聯
        if (Array.isArray(params.equipmentIds)) {
            const bidsEquipment = await manager.find(BidsEquipment, {
                where: {
                    bidId: bid.id,
                },
            });
            await manager.remove(bidsEquipment);
            for (const equipmentId of params.equipmentIds) {
                await manager.save(BidsEquipment, {
                    bidId: bid.id,
                    bidEquipmentsId: equipmentId,
                });
            }
        }
    }
}
