import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    <PERSON>rg<PERSON>,
    Query,
    Resolver,
    FieldResolver,
    Root,
    Mutation,
    Arg,
    Ctx,
    ID,
} from 'type-graphql';
import { URLResolver } from 'graphql-scalars';
import {
    BidCreateInput,
    BidSearchArgs,
    BidSearchResult,
    BidUpdateInput,
} from '../types/bid.gql.type';
import { BidService } from './bid.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { Bid } from '@/common/graphql/model/impl/salesRepWorkstation/bid.impl';
import { BidRepository } from './bid.repository';
import { UserService } from '@/modules/user/providers/user.service';
import { BidAttachmentRepository } from '../../bidAttachment/providers/bidAttachment.repository';
import { BidEquipmentRepository } from '../../bidEquipment/providers/bidEquipment.repository';
import { AuthorizationProduct as BidEquipment } from '@/common/graphql/model/impl/salesRepWorkstation/authorizationProduct.impl';
import { BidAttachment } from '@/common/graphql/model/impl/salesRepWorkstation/bidAttachment.impl';
import { CustomerService } from '@/modules/customer/customer/providers/customer.service';
import { Customer } from '@/common/graphql/model/impl/salesRepWorkstation/customer.impl';
@Service()
@Resolver((of) => Bid)
export class BidResolver {
    @Inject()
    private bidRepo: BidRepository;
    @Inject()
    private bidService: BidService;
    @Inject()
    private bidAttachmentRepo: BidAttachmentRepository;
    @Inject()
    private bidEquipmentRepo: BidEquipmentRepository;
    @Inject()
    private userService: UserService;
    @Inject()
    private customerService: CustomerService;

    @UserAuthInterceptor('bid.read')
    @Query(() => BidSearchResult)
    async bids(@Args() params: BidSearchArgs): Promise<BidSearchResult> {
        const result = await this.bidRepo.search({
            ...params.filters,
            ...params,
        });
        return <BidSearchResult>result;
    }

    @UserAuthInterceptor('bid.create')
    @Mutation((returns) => Bid, { name: 'createBid' })
    async create(
        @Arg('input') input: BidCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Bid> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor('bid.update')
    @Mutation((returns) => Bid, { name: 'updateBid' })
    async update(
        @Arg('input') input: BidUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Bid> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor('bid.delete')
    @Mutation((returns) => Boolean, { name: 'deleteBid' })
    async delete(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        await this.bidRepo.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async createdUser(@Root() bid: Bid): Promise<User | null> {
        const result = await this.userService.findOne(bid.createdUserId);
        return result;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async updatedUser(@Root() bid: Bid): Promise<User | null> {
        const result = await this.userService.findOne(bid.updatedUserId);
        return result;
    }
    @FieldResolver((returns) => [BidEquipment])
    async equipments(@Root() bid: Bid): Promise<BidEquipment[]> {
        const result = await this.bidEquipmentRepo.findByIds(
            bid.bidsEquipment.map((el) => el.bidEquipmentsId),
        );
        return result;
    }

    @FieldResolver((returns) => [BidAttachment])
    async attachments(@Root() bid: Bid): Promise<BidAttachment[]> {
        const result = await this.bidAttachmentRepo.search({
            bidId: bid.id,
        });
        return result.rows;
    }

    @FieldResolver((returns) => Customer, { nullable: true })
    async customer(@Root() bid: Bid): Promise<Customer | null> {
        const result = await this.customerService.findOne(bid.customerId);
        return result;
    }
}
