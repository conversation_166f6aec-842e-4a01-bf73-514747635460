import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import Container from 'typedi';
import {
    CreateParams,
    CreateResult,
    DeleteParams,
    UpdateParams,
    UpdateResult,
} from '../types/bid.grpc.type';
import { BidService } from './bid.service';
import { BidRepository } from './bid.repository';
import moment from 'moment';

const bidService = Container.get(BidService);
const bidRepo = Container.get(BidRepository);

export const Bid = {
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;
        const bid = await bidService.create({
            ...params,
            bidDate: moment.unix(params.bidDate.seconds).toDate(),
            publishDate:
                params.publishDate && params.publishDate.seconds != 0
                    ? moment.unix(params.publishDate.seconds).toDate()
                    : undefined,
            registrationDate:
                params.registrationDate && params.registrationDate.seconds != 0
                    ? moment.unix(params.registrationDate.seconds).toDate()
                    : undefined,
        });
        ctx.res = Helpers.Json.success(<CreateResult>{ id: bid.id });
    },

    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const bid = await bidService.update({
            ...params,
            customerId: params.customerId ?? <any>null,
            name: params.name ?? <any>null,
            // date
            bidDate:
                params.bidDate && params.bidDate.seconds != 0
                    ? moment.unix(params.bidDate.seconds).toDate()
                    : <any>null,
            publishDate:
                params.publishDate && params.publishDate.seconds != 0
                    ? moment.unix(params.publishDate.seconds).toDate()
                    : <any>null,
            registrationDate:
                params.registrationDate && params.registrationDate.seconds != 0
                    ? moment.unix(params.registrationDate.seconds).toDate()
                    : <any>null,
            publicLinkUrl: params.publicLinkUrl ?? <any>null,
            content: params.content ?? <any>null,
            result: params.result ?? <any>null,
            notes: params.notes ?? <any>null,
            equipmentIds: params.equipmentIds ?? <any>[],
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{ id: bid.id });
    },
    delete: async (ctx: Context<any>) => {
        const params = <DeleteParams>ctx.req;
        await bidRepo.delete(params);
        ctx.res = Helpers.Json.success();
    },
};
