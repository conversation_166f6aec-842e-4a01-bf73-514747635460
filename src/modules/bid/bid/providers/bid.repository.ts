import { Inject, Service } from 'typedi';
import _ from 'lodash';
import {
    Between,
    EntityManager,
    FindOptionsRelations,
    FindOptionsWhere,
    ILike,
    In,
    LessThanOrEqual,
    MoreThanOrEqual,
} from 'typeorm';
import Joi from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { Bid } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bid.model';
import {
    CreateParams,
    UpdateParams,
    SearchParams,
    DeleteParams,
    CreateDataParams,
} from '../types/bid.type';
import { CommonSearchResult } from '@/common/types/common.type';
import { Customer } from '@clinico/typeorm-persistence/models/salesRepWorkstation/customer.model';

@Service()
export class BidRepository extends CommonService<Bid> {
    private bidRepo = ClinicoDataSource.getRepository(Bid);

    async search(params: SearchParams): Promise<CommonSearchResult<Bid>> {
        const filters: FindOptionsWhere<Bid> = {};
        const customerFilters: FindOptionsWhere<Customer> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.code) {
            filters.code = ILike(`%${params.code}%`);
        }

        if (params.customerId) {
            filters.customerId = params.customerId;
        }
        if (params.name) {
            filters.name = ILike(`%${params.name}%`);
        }

        if (params.bidDate1) {
            filters.bidDate = MoreThanOrEqual(params.bidDate1);
        }
        if (params.bidDate2) {
            filters.bidDate = LessThanOrEqual(params.bidDate2);
        }
        if (params.bidDate1 && params.bidDate2) {
            filters.bidDate = Between(params.bidDate1, params.bidDate2);
        }

        if (params.publishDate1) {
            filters.publishDate = MoreThanOrEqual(params.publishDate1);
        }
        if (params.publishDate2) {
            filters.publishDate = LessThanOrEqual(params.publishDate2);
        }
        if (params.publishDate1 && params.publishDate2) {
            filters.publishDate = Between(
                params.publishDate1,
                params.publishDate2,
            );
        }

        if (params.registrationDate1) {
            filters.registrationDate = MoreThanOrEqual(
                params.registrationDate1,
            );
        }
        if (params.registrationDate2) {
            filters.registrationDate = LessThanOrEqual(
                params.registrationDate2,
            );
        }
        if (params.registrationDate1 && params.registrationDate2) {
            filters.registrationDate = Between(
                params.registrationDate1,
                params.registrationDate2,
            );
        }

        if (params.result) {
            filters.result = params.result;
        }
        if (params.notes) {
            filters.notes = ILike(`%${params.notes}%`);
        }
        if (params.equipmentIds && params.equipmentIds.length) {
            filters.bidsEquipment = {
                bidEquipmentsId: In(params.equipmentIds),
                bidEquipment: {
                    deleted: false,
                },
            };
        }
        if (params.customerCode) {
            customerFilters.code = ILike(`%${params.customerCode}%`);
        }
        if (params.customerName) {
            customerFilters.name = ILike(`%${params.customerName}%`);
        }

        filters.deleted = false;

        const data = await this.bidRepo.findAndCount({
            where: {
                ...filters,
                customer: !_.isEmpty(customerFilters) ? customerFilters : {},
            },
            relations: {
                bidsEquipment: true,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(params: CreateDataParams): Promise<Bid> {
        const bidToCreate = this.bidRepo.create({
            ...params,
        });
        await this.validate(bidToCreate);
        const bid = await this.bidRepo.save(bidToCreate);

        return bid;
    }

    async update(params: UpdateParams): Promise<Bid> {
        return await this.bidRepo.save(params);
    }

    async delete(params: DeleteParams): Promise<void> {
        const bidToDelete = await this.findOneOrError(params.id);
        bidToDelete.updatedUserId = params.deletedUserId;
        bidToDelete.deleted = true;
        await this.bidRepo.save(bidToDelete);
    }

    async validate(bid: Bid): Promise<void> {
        const schema = Joi.object<Bid>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(bid, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
