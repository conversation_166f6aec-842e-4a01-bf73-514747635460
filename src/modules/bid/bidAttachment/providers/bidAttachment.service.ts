import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import {
    BulkCreateParams,
    CreateParams,
    UpdateParams,
} from '../types/bidAttachment.type';
import { BaseError } from '@clinico/base-error';
import { Helpers } from '@clinico/clinico-node-framework';
import { AWSHelper } from '@/common/helpers/AWS.helper';
import { UploadService } from '@/modules/upload/providers/upload.service';
import { FileHelper } from '@/common/helpers/file.helper';
import { BidAttachmentRepository } from './bidAttachment.repository';
import { BidRepository } from '../../bid/providers/bid.repository';
import { BidAttachment } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bidAttachment.model';

@Service()
export class BidAttachmentService {
    @Inject()
    private uploadService: UploadService;
    @Inject()
    private bidRepo: BidRepository;
    @Inject()
    private bidAttachmentRepo: BidAttachmentRepository;

    async create(params: CreateParams): Promise<BidAttachment> {
        const existedBid = await this.bidRepo.findOneOrError(params.bidId);

        // File upload
        const file = await FileHelper.getFile(params.file);
        const ext = Helpers.File.ext(file.filename);
        const s3Key = AWSHelper.genS3Key({
            s3KeyType: AWSHelper.S3KeyType.BidAttachment,
            filename: params.name,
            ext: ext,
            paths: [existedBid.id],
        });
        await this.uploadService.uploadFile({
            key: s3Key,
            body: file.body,
            encoding: file.encoding,
            contentType: file.mimetype,
        });

        const bidAttachment = await this.bidAttachmentRepo.create({
            ...params,
            name: file.filename,
            extension: ext,
            s3Key: s3Key,
        });

        return bidAttachment;
    }

    async bulkCreate(params: BulkCreateParams): Promise<BidAttachment[]> {
        const results: BidAttachment[] = [];
        for (const attachment of params.attachments) {
            const result = await this.create({
                bidId: params.bidId,
                file: attachment.file,
                name: attachment.name,
                createdUserId: params.createdUserId,
            });
            results.push(result);
        }
        return results;
    }

    async update(params: UpdateParams): Promise<BidAttachment> {
        const bidAttachment = await this.bidAttachmentRepo.update({
            ...params,
        });
        return bidAttachment;
    }

    async validate(bidAttachment: BidAttachment): Promise<void> {
        const schema = Joi.object<BidAttachment>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(bidAttachment, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
