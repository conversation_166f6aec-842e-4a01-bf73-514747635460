import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { BidAttachment } from '@/common/graphql/model/impl/salesRepWorkstation/bidAttachment.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';
import {
    BidAttachmentSearchArgs,
    BidAttachmentSearchResult,
    BidAttachmentCreateInput,
    BidAttachmentUpdateInput,
    BidAttachmentBulkCreateInput,
} from '../types/bidAttachment.gql.type';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { UserService } from '@/modules/user/providers/user.service';
import { createFileBaseResolver } from '@/common/providers/fileBase.resolver';
import { BidAttachmentRepository } from './bidAttachment.repository';
import { BidAttachmentService } from './bidAttachment.service';

const FileBaseResolver = createFileBaseResolver(BidAttachment);

@Service()
@Resolver((of) => BidAttachment)
export class BidAttachmentResolver extends FileBaseResolver {
    @Inject()
    private bidtAttachmentService: BidAttachmentService;
    @Inject()
    private bidAttachmentRepo: BidAttachmentRepository;
    @Inject()
    private userService: UserService;

    @UserAuthInterceptor()
    @Query(() => BidAttachmentSearchResult)
    async bidAttachments(
        @Args() params: BidAttachmentSearchArgs,
    ): Promise<BidAttachmentSearchResult> {
        const result = await this.bidAttachmentRepo.search({
            ...params.filters,
            ...params,
        });
        return <BidAttachmentSearchResult>result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BidAttachment)
    async createBidAttachment(
        @Arg('input') input: BidAttachmentCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BidAttachment> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidtAttachmentService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => [BidAttachment])
    async bulkCreateBidAttachment(
        @Arg('input') input: BidAttachmentBulkCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BidAttachment[]> {
        const payload = ctx.req['user'] as UserPayload;
        console.log('payload', payload);
        const result = await this.bidtAttachmentService.bulkCreate({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BidAttachment)
    async updateBidAttachment(
        @Arg('input') input: BidAttachmentUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BidAttachment> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidtAttachmentService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => Boolean)
    async deleteBidAttachment(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.bidAttachmentRepo.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async createdUser(
        @Root() bidAttachment: BidAttachment,
    ): Promise<User | null> {
        const result = await this.userService.findOne(
            bidAttachment.createdUserId,
        );
        return result;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async updatedUser(
        @Root() bidAttachment: BidAttachment,
    ): Promise<User | null> {
        const result = await this.userService.findOne(
            bidAttachment.updatedUserId,
        );
        return result;
    }
}
