import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import Container from 'typedi';
import {
    CreateParams,
    UpdateParams,
    CreateResult,
    UpdateResult,
    BulkCreateParams,
    BulkCreateResult,
} from '../types/bidAttachment.grpc.type';
import { DeleteParams } from '../types/bidAttachment.type';
import { BidAttachmentService } from './bidAttachment.service';
import { BidAttachmentRepository } from './bidAttachment.repository';

const bidAttachmentService = Container.get(BidAttachmentService);

const bidAttachmentRepo = Container.get(BidAttachmentRepository);

export const BidAttachment = {
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;

        const bidAttachment = await bidAttachmentService.create({
            ...params,
            file: params.file,
        });
        ctx.res = Helpers.Json.success(<CreateResult>{
            id: bidAttachment.id,
        });
    },
    bulkCreate: async (ctx: Context<any>) => {
        const params = <BulkCreateParams>ctx.req;
        const contactPersonAttachments = await bidAttachmentService.bulkCreate({
            ...params,
            attachments: params.attachments,
        });
        ctx.res = Helpers.Json.success(<BulkCreateResult>{
            ids: contactPersonAttachments.map((data) => data.id),
        });
    },

    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const bidAttachment = await bidAttachmentService.update({
            ...params,
            name: params.name ?? <any>null,
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{
            id: bidAttachment.id,
        });
    },
    delete: async (ctx: Context<any>) => {
        const params = <DeleteParams>ctx.req;
        await bidAttachmentRepo.delete(params);
        ctx.res = Helpers.Json.success();
    },
};
