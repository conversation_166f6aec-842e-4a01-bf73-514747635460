import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    UpdateParams,
    DeleteParams,
    CreateDataParams,
} from '../types/bidAttachment.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { BidAttachment } from '@clinico/typeorm-persistence/models/salesRepWorkstation/bidAttachment.model';

@Service()
export class BidAttachmentRepository extends CommonService<BidAttachment> {
    private bidAttachmentRepo = ClinicoDataSource.getRepository(BidAttachment);

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<BidAttachment>> {
        const filters: FindOptionsWhere<BidAttachment> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.bidId) {
            filters.bidId = params.bidId;
        }

        filters.deleted = false;

        const data = await this.bidAttachmentRepo.findAndCount({
            where: filters,
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(params: CreateDataParams): Promise<BidAttachment> {
        const bidAttachmentToCreate = this.bidAttachmentRepo.create({
            ...params,
        });

        await this.validate(bidAttachmentToCreate);
        const bidAttachment = await this.bidAttachmentRepo.save(
            bidAttachmentToCreate,
        );
        return bidAttachment;
    }

    async update(params: UpdateParams): Promise<BidAttachment> {
        const bidAttachmentToUpdate = await this.findOneOrError(params.id);
        const bidAttachment = await this.bidAttachmentRepo.save({
            ...bidAttachmentToUpdate,
            ...params,
            id: bidAttachmentToUpdate.id,
        });
        return bidAttachment;
    }

    async delete(params: DeleteParams): Promise<void> {
        const bidAttachmentToDelete = await this.findOneOrError(params.id);
        bidAttachmentToDelete.updatedUserId = params.deletedUserId;
        bidAttachmentToDelete.deleted = true;
        await this.bidAttachmentRepo.save(bidAttachmentToDelete);
    }

    async validate(bidAttachment: BidAttachment): Promise<void> {
        const schema = Joi.object<BidAttachment>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(bidAttachment, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
