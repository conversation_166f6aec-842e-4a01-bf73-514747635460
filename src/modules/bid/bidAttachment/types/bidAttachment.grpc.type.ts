import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import {
    CommonCreateParams,
    CommonUpdateParams,
    CommonDeletedParams,
} from '@/common/types/common.type';
import { FileUpload } from '@clinico/graphql-upload';
export type CreateParams = CommonCreateParams & {
    bidId: number;
    file: Promise<FileUpload> | GRPCFileUpload;
    name: string;
};

export type BulkCreateParams = {
    bidId: number;
    attachments: {
        file: GRPCFileUpload;
        name: string;
        memo?: string;
    }[];
    createdUserId: number;
};

export type UpdateParams = CommonUpdateParams & {
    name?: string | null;
};

export type CreateResult = {
    id: number;
};

export type BulkCreateResult = {
    ids: number[];
};

export type UpdateResult = {
    id: number;
};

export type DeleteParams = CommonDeletedParams;
