import { ArgsType, Field, ID, InputType, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
    AttachmentCreateInput,
} from '@/common/types/common.gql.type';
import { GraphQLUpload, FileUpload } from '@clinico/graphql-upload';
import { BidAttachment } from '@/common/graphql/model/impl/salesRepWorkstation/bidAttachment.impl';

@InputType()
class BidAttachmentSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => ID, { nullable: true })
    bidId?: number;
}

@ArgsType()
export class BidAttachmentSearchArgs extends CommonSearchArgs(
    BidAttachmentSearchInput,
) {}

@InputType()
export class BidAttachmentCreateInput {
    @Field((type) => ID)
    bidId: number;

    @Field((type) => GraphQLUpload)
    file: Promise<FileUpload>;

    @Field({ nullable: true })
    name: string;
}

@InputType()
export class BidAttachmentUpdateInput {
    @Field((type) => ID)
    id: number;

    @Field({ nullable: true, description: '檔案名稱' })
    name: string;
}

@InputType()
export class BidAttachmentBulkCreateInput {
    @Field((type) => ID)
    bidId: number;

    @Field((type) => [AttachmentCreateInput])
    attachments: AttachmentCreateInput[];
}

@ObjectType()
export class BidAttachmentSearchResult extends PaginatedSearchResult(
    BidAttachment,
) {}
