import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import {
    CommonSearchParams,
    CommonCreateParams,
    CommonUpdateParams,
    CommonDeletedParams,
} from '@/common/types/common.type';
import { FileUpload } from '@clinico/graphql-upload';

export type SearchParams = CommonSearchParams & {
    bidId?: number;
};

export type CreateParams = CommonCreateParams & {
    bidId: number;
    file: Promise<FileUpload> | GRPCFileUpload;
    name: string;
};
export type CreateDataParams = CommonCreateParams & {
    bidId: number;
    s3Key: string;
    extension: string;
    name: string;
};

export type BulkCreateParams = CommonCreateParams & {
    bidId: number;
    attachments: {
        file: Promise<FileUpload> | GRPCFileUpload;
        name: string;
        memo?: string;
    }[];
};
export type UpdateParams = CommonUpdateParams & {
    name?: string;
};

export type DeleteParams = CommonDeletedParams;
