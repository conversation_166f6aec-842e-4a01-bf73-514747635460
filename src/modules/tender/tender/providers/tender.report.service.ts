import moment from 'moment';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { Inject, Service } from 'typedi';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import { BaseError } from '@clinico/base-error';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { tenderAll } from '../types/tender.type';
import { Tender } from '@clinico/typeorm-persistence/models/salesRepWorkstation/tender.model';
import { TenderItem } from '@clinico/typeorm-persistence/models/salesRepWorkstation/tenderItem.model';
import _ from 'lodash';
import { CustomerService } from '@/modules/customer/customer/providers/customer.service';
import { ProvinceService } from '@/modules/province/providers/province.service';
import { CityService } from '@/modules/city/providers/city.service';
import { DistrictService } from '@/modules/district/providers/district.service';
import { In } from 'typeorm';
import { TenderService } from './tender.service';

// 欄位映射表 - 對照 DB 欄位
const FIELD_MAPPING = {
    标文ID: 'standardTextId',
    标文关键词: 'keyword',
    信息标题: 'messageHeader',
    省: 'province',
    市: 'city',
    县: 'district',
    发布时间: 'releaseTime',
    '招标/项目编号': 'projectCode',
    合并数据: 'bidCustomerName',
    招标预算: 'bidBudget',
    招标单位联系人: 'bidContactPerson',
    招标单位联系方式: 'bidContactPhone',
    中标单位: 'winBidCustomer',
    中标金额: 'winBidBudget',
    中标单位联系人: 'winBidContactPerson',
    中标单位联系方式: 'winBidContactPhone',
    代理机构: 'agency',
    代理联系人: 'proxyContactPerson',
    代理联系电话: 'proxyContactPhone',
    信息类型: 'informationType',
    招标方式: 'bidMethod',
    标书获取时间: 'bidAcquisitionTime',
    标书截止时间: 'bidDeadline',
    投标开始时间: 'bidStartTime',
    投标截止时间: 'bidEndTime',
    开标时间: 'bidOpeningTime',
    评标专家: 'bidEvaluationExpert',
    资金来源: 'fundSource',
    是否电子招标: 'isElectronicBid',
    二级信息类型: 'secondaryInformationType',
    标的物名称: 'bidMatterName',
    标的物品牌: 'bidMatterBrand',
    标的物型号: 'bidMatterModel',
    数量: 'quantity',
    单价: 'unitPrice',
    总价: 'totalPrice',
};

// 只保留必要欄位檢查
const REQUIRED_FIELDS = ['standardTextId'];

@Service()
export class TenderReportService {
    @Inject()
    private customerService: CustomerService;
    @Inject()
    private provinceService: ProvinceService;
    @Inject()
    private cityService: CityService;
    @Inject()
    private districtService: DistrictService;
    @Inject()
    private tenderService: TenderService;

    private errors: string[] = [];
    /**
     * 使用 Excel2Templater 匯入 Excel 檔案並處理培訓資料
     */
    async importExcel(fileBuffer: Buffer): Promise<tenderAll[] | null> {
        let tempFilePath: string | null = null;

        try {
            // 建立暫存檔案
            tempFilePath = await this.createTempFile(fileBuffer);

            // 使用 Excel2Templater 讀取檔案
            const rawData = await this.readExcelWithTemplater(tempFilePath);

            // 處理表頭映射
            const mappedData = this.mapHeaders(rawData);

            // 直接轉換資料格式，不經過驗證清理
            const processedData = this.processData(mappedData);

            return processedData;
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }

    private async createTempFile(buffer: Buffer): Promise<string> {
        const tempDir = os.tmpdir();
        const tempFileName = `tender_import_${Date.now()}_${Math.random()
            .toString(36)
            .substr(2, 9)}.xlsx`;
        const tempFilePath = path.join(tempDir, tempFileName);

        await fs.promises.writeFile(tempFilePath, buffer);
        return tempFilePath;
    }

    /**
     * 使用 Excel2Templater 讀取 Excel 檔案
     */
    private async readExcelWithTemplater(filePath: string): Promise<any[][]> {
        const excel = new Utils.Excel2Templater();
        await excel.load(filePath);

        const workbook = excel.getWorkbook();
        const sheets = excel.getAllSheets();

        if (sheets.length === 0) {
            throw new Error('Excel 檔案中沒有工作表');
        }

        // 使用第一個工作表
        const worksheet = workbook.getWorksheet(sheets[0].id);

        if (!worksheet) {
            throw new Error('無法取得工作表');
        }

        const data: any[][] = [];

        worksheet.eachRow((row: any, rowNumber: number) => {
            const rowData: any[] = [];

            row.eachCell((cell: any, colNumber: number) => {
                let value = cell.value;

                // 處理不同類型的值
                if (cell.type === 6) {
                    // Date
                    value = moment(value).format('YYYY-MM-DD HH:mm:ss');
                } else if (cell.type === 1) {
                    // Number
                    value = cell.value;
                } else if (cell.type === 2) {
                    // String
                    value = cell.value?.toString().trim();
                } else if (cell.type === 3) {
                    // Formula
                    value = cell.result || cell.value;
                }

                rowData[colNumber - 1] = value || '';
            });

            // 確保行資料不為空
            if (
                rowData.some(
                    (cell) =>
                        cell !== null && cell !== undefined && cell !== '',
                )
            ) {
                data.push(rowData);
            }
        });

        return data;
    }

    /**
     * 映射表頭
     */
    private mapHeaders(rawData: any[][]): any[] {
        if (rawData.length === 0) {
            throw new Error('Excel文件為空');
        }

        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // 創建映射後的表頭
        const mappedHeaders = headers.map((header) => {
            const trimmedHeader = header.toString().trim();
            return FIELD_MAPPING[trimmedHeader] || trimmedHeader;
        });

        // 簡化的必要欄位檢查
        const missingFields = REQUIRED_FIELDS.filter(
            (field) => !mappedHeaders.includes(field),
        );

        if (missingFields.length > 0) {
            this.errors.push(`缺少必要欄位: ${missingFields.join(', ')}`);
        }

        // 轉換為物件陣列
        return dataRows.map((row, index) => {
            const rowData: any = {};
            mappedHeaders.forEach((header, colIndex) => {
                rowData[header] = row[colIndex] || '';
            });
            rowData._rowIndex = index + 2; // Excel行號（從2開始）
            return rowData;
        });
    }

    /**
     * 處理最終資料 - 直接轉換，不進行驗證
     */
    private processData(data: any[]): tenderAll[] {
        return data.map((row) => {
            const { _rowIndex, ...cleanRow } = row;
            return cleanRow as tenderAll;
        });
    }

    /**
     * 資料庫操作
     */
    async upsertTenders(processedData: tenderAll[] | null): Promise<any> {
        try {
            await ClinicoDataSource.transaction(async (manager) => {
                if (processedData && processedData.length > 0) {
                    //province
                    const provinceNames = _.compact(
                        processedData.map((data) => data.province),
                    );
                    const provinces = await this.provinceService.findByNames(
                        _.uniq(provinceNames),
                    );
                    const provinceMap = new Map(
                        provinces.map((data) => [data.name, data.id]),
                    );

                    //city
                    const cityNames = _.compact(
                        processedData.map((data) => data.city),
                    );
                    const cities = await this.cityService.findByNames(
                        _.uniq(cityNames),
                    );
                    const cityMap = new Map(
                        cities.map((data) => [data.name, data.id]),
                    );

                    // district
                    const districtNames = _.compact(
                        processedData.map((data) => data.district),
                    );
                    const districts = await this.districtService.findByNames(
                        _.uniq(districtNames),
                    );
                    const districtMap = new Map(
                        districts.map((data) => [data.name, data.id]),
                    );

                    // bidCustomers
                    const bidCustomerNames = _.compact(
                        processedData.map((data) => data.bidCustomerName),
                    );
                    const bidCustomers = await this.customerService.findByNames(
                        _.uniq(bidCustomerNames),
                    );
                    const bidCustomerMap = new Map(
                        bidCustomers.map((data) => [data.name, data.id]),
                    );
                    //standardTextId
                    const standardTextIds = _.compact(
                        processedData.map((data) => data.standardTextId),
                    );
                    const tenders =
                        await this.tenderService.findByStandardTextIds(
                            _.uniq(standardTextIds),
                        );
                    const tenderMap = new Map(
                        tenders.map((data) => [data.standardTextId, data.id]),
                    );

                    // 將資料轉換為 Tender 實體格式
                    const tenderEntitiesToUpsert = processedData.map(
                        (data) => ({
                            // 主鍵欄位
                            standardTextId: data.standardTextId,
                            // 其他欄位
                            keyword: data.keyword || undefined,
                            messageHeader: data.messageHeader || undefined,
                            provinceId: provinceMap.get(data.province || ''),
                            cityId: cityMap.get(data.city || ''),
                            districtId: districtMap.get(data.district || ''),
                            releaseTime: data.releaseTime || undefined,
                            projectCode: data.projectCode || undefined,
                            bidCustomerId: bidCustomerMap.get(
                                data.bidCustomerName || '',
                            ),

                            bidCustomerName: data.bidCustomerName || undefined,
                            bidBudget: data.bidBudget || undefined,
                            bidContactPerson:
                                data.bidContactPerson || undefined,
                            bidContactPhone: data.bidContactPhone || undefined,
                            winBidCustomer: data.winBidCustomer || undefined,
                            winBidBudget: data.winBidBudget || undefined,
                            winBidContactPerson:
                                data.winBidContactPerson || undefined,
                            winBidContactPhone:
                                data.winBidContactPhone || undefined,
                            agency: data.agency || undefined,
                            proxyContactPerson:
                                data.proxyContactPerson || undefined,
                            proxyContactPhone:
                                data.proxyContactPhone || undefined,
                            informationType: data.informationType || undefined,
                            bidMethod: data.bidMethod || undefined,
                            bidAcquisitionTime:
                                data.bidAcquisitionTime || undefined,
                            bidDeadline: data.bidDeadline || undefined,
                            bidStartTime: data.bidStartTime || undefined,
                            bidEndTime: data.bidEndTime || undefined,
                            bidOpeningTime: data.bidOpeningTime || undefined,
                            bidEvaluationExpert:
                                data.bidEvaluationExpert || undefined,
                            fundSource: data.fundSource || undefined,
                            isElectronicBid:
                                data.isElectronicBid === '是' ? true : false,
                            secondaryInformationType:
                                data.secondaryInformationType || undefined,
                        }),
                    );

                    const tenderIdMap = new Map<string, number>();
                    for (const tenderData of tenderEntitiesToUpsert) {
                        const originalTenderId = tenderMap.get(
                            tenderData.standardTextId,
                        );
                        let tenderId: number;
                        if (originalTenderId) {
                            // 更新现有记录
                            manager.update(Tender, originalTenderId, {
                                ...tenderData,
                            });
                            tenderId = originalTenderId;
                        } else {
                            // 插入新记录
                            const newTender = await manager.save(
                                Tender,
                                tenderData,
                            );
                            tenderId = newTender.id;
                            tenderMap.set(
                                tenderData.standardTextId,
                                newTender.id,
                            );
                        }

                        tenderIdMap.set(tenderData.standardTextId, tenderId);
                    }

                    // 將資料轉換為 TenderItem 實體格式
                    const tenderItemEntitiesToUpsert = processedData.map(
                        (data) => ({
                            // 主鍵欄位
                            standardTextId: data.standardTextId,
                            // 其他欄位
                            tenderId: tenderIdMap.get(
                                data.standardTextId || '',
                            ),
                            bidMatterName: data.bidMatterName || undefined,
                            bidMatterBrand: data.bidMatterBrand || undefined,
                            bidMatterModel: data.bidMatterModel || undefined,
                            quantity: data.quantity || undefined,
                            unitPrice: data.unitPrice || undefined,
                            totalPrice: data.totalPrice || undefined,
                        }),
                    );

                    // 使用事务保证原子性
                    await manager.transaction(async (transactionalManager) => {
                        // 第一步：逻辑删除已存在的旧数据
                        if (standardTextIds.length > 0) {
                            await transactionalManager.update(
                                TenderItem,
                                {
                                    standardTextId: In(standardTextIds),
                                    deleted: false,
                                },
                                { deleted: true },
                            );
                        }
                    });

                    await manager.save(TenderItem, tenderItemEntitiesToUpsert);

                    return {
                        success: true,
                        affectedRows: tenderEntitiesToUpsert.length,
                        message: `成功處理 ${tenderEntitiesToUpsert.length} 筆記錄`,
                    };
                }
            });
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: '資料庫操作失敗',
            };
        }
    }

    /**
     * 處理匯入的主要方法（結合資料庫操作）
     */
    async handleImport(fileBuffer: Buffer): Promise<null> {
        const result = await this.importExcel(fileBuffer);

        // 執行資料庫 upsert 操作
        await this.upsertTenders(result);
        return null;
    }
}
