import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    ObjectType,
} from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';

@ObjectType()
export class ConferenceBudget {
    @Field({ nullable: true, description: '事业部' })
    division?: string;

    @Field({ nullable: true, description: '事业部主管' })
    divisionSupervisor?: string;

    @Field({ nullable: true, description: '大区' })
    region?: string;

    @Field({ nullable: true, description: '大区主管' })
    regionSupervisor?: string;

    @Field({ nullable: true, description: '地区' })
    district?: string;

    @Field({ nullable: true, description: '地区主管' })
    districtSupervisor?: string;

    @Field({ nullable: true, description: '职位' })
    position?: string;

    @Field({ nullable: true, description: '成本中心1' })
    costCenter1?: string;

    @Field({ nullable: true, description: '成本中心2' })
    costCenter2?: string;

    @Field(() => Float, { nullable: true, description: '全年会议预算' })
    annualBudget?: number;

    @Field(() => Int, { nullable: true, description: '年' })
    year?: number;

    @Field(() => Float, { nullable: true, description: '可控费用' })
    controllableExpense?: number;

    @Field(() => Float, { nullable: true, description: '已申请费用' })
    usedExpense?: number;

    @Field(() => Float, { nullable: true, description: '全年剩余预算' })
    remainingBudget?: number;
}

@InputType()
class ConferenceBudgetSearchInput {
    @Field(() => Int, { nullable: true, description: '年' })
    year?: number;

    @Field({ nullable: true, description: '事业部' })
    division?: string;

    @Field({ nullable: true, description: '事业部主管' })
    divisionSupervisor?: string;

    @Field((type) => [String], { nullable: true, description: '大区' })
    regions?: string[];

    @Field((type) => [String], { nullable: true, description: '大区主管' })
    regionSupervisors?: string[];

    @Field((type) => [String], { nullable: true, description: '成本中心' })
    costCenters?: string[];

    @Field((type) => [String], { nullable: true, description: '成本中心编号' })
    costCenterCodes?: string[];
}

@ArgsType()
export class ConferenceBudgetSearchArgs extends CommonSearchArgs(
    ConferenceBudgetSearchInput,
) {}

@ObjectType()
export class ConferenceBudgetSearchResult extends PaginatedSearchResult(
    ConferenceBudget,
) {}
