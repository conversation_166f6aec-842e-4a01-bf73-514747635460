import { CommonLimitParams } from '@/common/types/common.type';

export type ConferenceBudget = {
    division?: string;
    divisionSupervisor?: string;
    region?: string;
    regionSupervisor?: string;
    district?: string;
    districtSupervisor?: string;
    position?: string;
    costCenter1?: string;
    costCenter2?: string;
    annualBudget?: number;
    year?: number;
    controllableExpense?: number;
    usedExpense?: number;
    remainingBudget?: number;
};

export type ConferenceBudgetYonyouFee = {
    deptCode?: string;
    amount?: number;
};

export type ConferenceBudgetUsedFee = {
    costCenterCode?: string;
    amount?: string;
};

export type SearchParams = CommonLimitParams & {
    year?: number; // 年份
    division?: string;
    divisionSupervisor?: string;
    regions?: string[];
    regionSupervisors?: string[];
    costCenters?: string[];
    costCenterCodes?: string[];
};
