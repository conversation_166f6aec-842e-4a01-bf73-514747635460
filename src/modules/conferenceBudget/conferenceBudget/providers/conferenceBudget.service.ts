import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import {
    ConferenceBudget,
    SearchParams,
    ConferenceBudgetYonyouFee,
    ConferenceBudgetUsedFee,
} from '../types/conferenceBudget.type';
import { YonYouDataSource } from '@/common/databases/yonyou.database';
import Big from 'big.js';

@Service()
export class ConferenceBudgetService {
    async search(params: SearchParams): Promise<ConferenceBudget[]> {
        const year = params?.year ?? new Date().getFullYear();
        let filters = '';
        if (params.division) {
            filters += ` AND division = '${params.division}' `;
        }
        if (params.divisionSupervisor) {
            filters += ` AND division_supervisor = '${params.divisionSupervisor}' `;
        }
        if (params.regions && params.regions.length > 0) {
            filters += ` AND region IN ('${params.regions.join("','")}') `;
        }
        if (params.regionSupervisors && params.regionSupervisors.length > 0) {
            filters += ` AND region_supervisor IN ('${params.regionSupervisors.join(
                "','",
            )}') `;
        }
        if (params.costCenters && params.costCenters.length > 0) {
            filters += ` AND (cost_center1 IN ('${params.costCenters.join(
                "','",
            )}') OR cost_center2 IN ('${params.costCenters.join("','")}'))`;
        }
        if (params.costCenterCodes && params.costCenterCodes.length > 0) {
            filters += ` AND (cost_center1 IN ('${params.costCenterCodes.join(
                "','",
            )}') OR cost_center2 IN ('${params.costCenterCodes.join("','")}'))`;
        }

        const yonyouSql = this.buildYonyouRawSql(year);
        const yonyouItems = await YonYouDataSource.query(yonyouSql);
        const usedFeeSql = this.buildUsedFeeRawSql(year);
        const usedFeeItems = await ClinicoDataSource.query(usedFeeSql);

        const sql = this.buildRawSql(year, filters);
        const items = await ClinicoDataSource.query(sql);
        const results: ConferenceBudget[] = this.transformData(
            items,
            yonyouItems,
            usedFeeItems,
        );
        return results;
    }

    private transformData(
        items: any[],
        yonyouItems: ConferenceBudgetYonyouFee[],
        usedFeeItems: ConferenceBudgetUsedFee[],
    ): ConferenceBudget[] {
        const feeMap = _.keyBy(yonyouItems, 'deptCode');
        const usedFeeMap = _.keyBy(usedFeeItems, 'costcentercode');
        return _.map(items, (item) => {
            const detail: ConferenceBudget = {
                division: item.division,
                divisionSupervisor: item.division_supervisor,
                region: item.region,
                regionSupervisor: item.region_supervisor,
                district: item.district,
                districtSupervisor: item.district_supervisor,
                position: item.position,
                annualBudget: item.annual_budget,
                year: item.year,
                costCenter1: item.cost_center1,
                costCenter2: item.cost_center2,
            };

            // 计算可控费用
            detail.controllableExpense = _.chain([
                detail.costCenter1,
                detail.costCenter2,
            ])
                .compact() // 移除空值
                .uniq() // 去重（避免重复计算相同部门）
                .reduce((sum, cc) => {
                    const feeItem = feeMap[cc];
                    return sum + (feeItem?.amount ?? 0);
                }, 0)
                .value();

            // 计算已使用费用
            detail.usedExpense = _.chain([
                detail.costCenter1,
                detail.costCenter2,
            ])
                .compact() // 移除空值
                .uniq() // 去重（避免重复计算相同部门）
                .reduce((sum, cc) => {
                    const usedFeeItem = usedFeeMap[cc];
                    const amount = parseFloat(usedFeeItem?.amount || '0');
                    return sum + amount;
                }, 0)
                .value();

            // 计算剩余预算
            const annualBig = new Big(detail.annualBudget || 0);
            const expenseBig = new Big(detail.controllableExpense || 0);
            const usedExpenseBig = new Big(detail.usedExpense || 0);
            detail.remainingBudget = annualBig.minus(usedExpenseBig).toNumber();

            return detail;
        });
    }

    private buildRawSql(year: number, filters: string): string {
        // prettier-ignore
        const sql = `
            WITH cost_centers AS (
                    SELECT cbcc.conference_budget_id
                          ,cc.code
                          ,ROW_NUMBER() OVER (PARTITION BY cbcc.conference_budget_id ORDER BY cc.id) AS rn
                    FROM sales_rep_workstation.conference_budget_cost_centers cbcc 
                    LEFT JOIN public.cost_centers cc 
                    ON cc.id = cbcc.cost_center_id 
                    AND cc.deleted = FALSE 
                    AND cc.is_active = TRUE
            ),
            budget_base AS (
                    SELECT cb.id
                          ,cb.annual_budget
                          ,cb."year"
                          ,stu.id AS unit_id
                          ,stu.unit_role
                          ,stup.id AS position_id
                          ,stup.name AS position
                          ,u.name AS unit_supervisor
                          ,st.id AS team_id
                          ,st.name AS team_name
                          ,st1.id AS region_id
                          ,st1.name AS region_name
                          ,u1.name AS region_supervisor
                          ,st2.id AS division_id
                          ,st2.name AS division_name
                          ,u2.name AS division_supervisor
                    FROM sales_rep_workstation.conference_budgets cb 
                    INNER JOIN sales_rep_workstation.sales_team_units stu 
                        ON cb.sales_team_unit_id = stu.id 
                        AND stu.deleted = FALSE 
                    INNER JOIN sales_rep_workstation.sales_team_unit_posts stup 
                        ON stup.id = stu.sales_team_unit_post_id
                    LEFT JOIN public.users u 
                        ON stu.user_id = u.id 
                        AND u.is_active = TRUE 
                        AND u.deleted = FALSE
                    LEFT JOIN sales_rep_workstation.sales_teams st 
                        ON stu.sales_team_id = st.id 
                        AND st.deleted = false 
                    LEFT JOIN sales_rep_workstation.sales_teams st1 
                        ON st.parent_id = st1.id 
                        AND st1.deleted = FALSE
                    LEFT JOIN public.users u1 
                        ON st1.manager_id = u1.id 
                        AND u1.is_active = TRUE 
                        AND u1.deleted = FALSE
                    LEFT JOIN sales_rep_workstation.sales_teams st2 
                        ON st1.parent_id = st2.id 
                        AND st2.deleted = FALSE
                    LEFT JOIN public.users u2 
                        ON st2.manager_id = u2.id 
                        AND u2.is_active = TRUE 
                        AND u2.deleted = FALSE
                    WHERE stup.id IN (2, 4, 5)
                    AND cb.deleted = FALSE 
                    AND cb.year = ${year}
            ),
            combined_data AS (
                    SELECT
                        CASE 
                            WHEN bb.position_id = 5 THEN bb.team_name
                            WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.division_name
                            WHEN bb.position_id = 2 AND bb.region_id = 28 THEN bb.region_name
                            WHEN bb.position_id = 4 AND bb.region_id = 1 THEN bb.region_name
                        END AS division
                        ,CASE 
                            WHEN bb.position_id = 5 THEN bb.unit_supervisor
                            WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.division_supervisor
                            WHEN bb.position_id = 2 AND bb.region_id = 28 THEN bb.region_supervisor
                            WHEN bb.position_id = 4 AND bb.region_id = 1 THEN bb.region_supervisor
                        END AS division_supervisor
                        ,CASE 
                            WHEN bb.position_id = 4 THEN bb.unit_role
                            WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.region_name
                            ELSE NULL 
                        END AS region
                        ,CASE 
                            WHEN bb.position_id = 4 THEN bb.unit_supervisor
                            WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.region_supervisor
                            ELSE NULL 
                        END AS region_supervisor
                        ,CASE 
                            WHEN bb.position_id = 2 THEN bb.unit_role
                            ELSE NULL 
                        END AS district
                        ,CASE 
                            WHEN bb.position_id = 2 THEN bb.unit_supervisor
                            ELSE NULL 
                        END AS district_supervisor
                        ,bb.position
                        ,bb.annual_budget
                        ,bb."year"
                        ,MAX(CASE WHEN cc.rn = 1 THEN cc.code END) AS cost_center1
                        ,MAX(CASE WHEN cc.rn = 2 THEN cc.code END) AS cost_center2
                    FROM budget_base bb
                    INNER JOIN cost_centers cc 
                        ON bb.id = cc.conference_budget_id
                    WHERE 
                        (bb.position_id = 2 AND (bb.division_id = 1 OR bb.region_id = 28)) OR
                        (bb.position_id = 4 AND bb.region_id = 1) OR
                        (bb.position_id = 5)
                    GROUP BY 
                        CASE WHEN bb.position_id = 5 THEN bb.team_name
                             WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.division_name
                             WHEN bb.position_id = 2 AND bb.region_id = 28 THEN bb.region_name
                             WHEN bb.position_id = 4 AND bb.region_id = 1 THEN bb.region_name 
                             END,
                        CASE WHEN bb.position_id = 5 THEN bb.unit_supervisor
                             WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.division_supervisor
                             WHEN bb.position_id = 2 AND bb.region_id = 28 THEN bb.region_supervisor
                             WHEN bb.position_id = 4 AND bb.region_id = 1 THEN bb.region_supervisor 
                             END,
                        CASE WHEN bb.position_id = 4 THEN bb.unit_role
                             WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.region_name
                             ELSE NULL 
                             END,
                        CASE WHEN bb.position_id = 4 THEN bb.unit_supervisor
                             WHEN bb.position_id = 2 AND bb.division_id = 1 THEN bb.region_supervisor
                             ELSE NULL 
                             END,
                        CASE WHEN bb.position_id = 2 THEN bb.unit_role ELSE NULL 
                            END,
                        CASE WHEN bb.position_id = 2 THEN bb.unit_supervisor ELSE NULL 
                            END,
                        bb.position,
                        bb.annual_budget,
                        bb."year"
            ),
            mkt_data AS (
						SELECT '市场部' AS division
                              ,'古琳琳' AS division_supervisor
                              ,NULL AS region
                              ,NULL AS region_supervisor
                              ,NULL AS district
                              ,NULL AS district_supervisor
                              ,'全国' AS position
                              ,cb.annual_budget
                              ,cb."year"
                              ,MAX ( CASE WHEN cc.rn = 1 THEN cc.code END ) AS cost_center1
                              ,MAX ( CASE WHEN cc.rn = 2 THEN cc.code END ) AS cost_center2 
	                    FROM sales_rep_workstation.conference_budgets cb
		                INNER JOIN cost_centers cc ON cb.ID = cc.conference_budget_id 
	                    WHERE cb.sales_team_unit_id IS NULL 
		                AND cb.YEAR = ${year}
                        AND cb.deleted = FALSE 
	                    GROUP BY cb.id, cb.annual_budget, cb."year"
			)
            SELECT * FROM (
                        SELECT * FROM combined_data
                        UNION ALL
                        SELECT * FROM mkt_data
            ) combined_results
            WHERE 1 = 1
            ${filters}
            `;
        return sql;
    }

    private buildYonyouRawSql(year: number): string {
        // prettier-ignore
        const sql = `
            SELECT vce.dept_code AS deptCode
                  ,SUM(vce.amount) AS amount 
            FROM [CLINICO_CHINA].[dbo].v_controllable_expense vce
            INNER JOIN [CLINICO_CHINA].[dbo].t_statist_account tsa on vce.accounting_code = tsa.accounting_code
            WHERE tsa.statist_name = '会议费'
            AND YEAR(vce.accounting_date) = ${year}
            GROUP BY vce.dept_code,tsa.statist_name
         `;
        return sql;
    }

    private buildUsedFeeRawSql(year: number): string {
        // prettier-ignore
        const sql = `
            SELECT cc.code AS costCenterCode
                  ,SUM(COALESCE(ma.confirm_expense, ma.expense_budget)) AS amount
            FROM public.market_activities ma 
            INNER JOIN public.cost_centers cc ON ma.cost_center_id = cc.id
            WHERE 
                ma.deleted = FALSE
                AND ma.status != -1
            		AND EXTRACT(YEAR FROM ma.date1) = ${year}
            GROUP BY cc.code
         `;
        return sql;
    }
}
