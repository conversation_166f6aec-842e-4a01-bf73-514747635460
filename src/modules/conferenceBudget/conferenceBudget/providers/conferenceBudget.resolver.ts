import { Inject, Service } from 'typedi';
import {
    Args,
    FieldResolver,
    Float,
    Query,
    <PERSON>solver,
    Root,
} from 'type-graphql';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { ConferenceBudgetService } from './conferenceBudget.service';
import {
    ConferenceBudget,
    ConferenceBudgetSearchArgs,
} from '../types/conferenceBudget.gql.type';
import { MarketActivityService } from '@/modules/marketActivity/marketActivity/providers/marketActivity.service';

@Service()
@Resolver(() => ConferenceBudget)
export class ConferenceBudgetResolver {
    @Inject()
    private conferenceBudgetService: ConferenceBudgetService;
    @Inject()
    private marketActivityService: MarketActivityService;

    @UserAuthInterceptor('conferenceBudget.read')
    @Query(() => [ConferenceBudget])
    async conferenceBudgets(
        @Args() params: ConferenceBudgetSearchArgs,
    ): Promise<ConferenceBudget[]> {
        const result = await this.conferenceBudgetService.search({
            ...params.filters,
            ...params,
        });
        return result;
    }

    @FieldResolver(() => Float, {
        description: '已申請未結案費用',
    })
    async unresolvedFee(
        @Root() conferenceBudget: ConferenceBudget,
    ): Promise<number> {
        return this.marketActivityService.calculateUnResolvedFees(
            conferenceBudget,
        );
    }
}
