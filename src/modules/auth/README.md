## Usage

針對 `me -> userPermission` 的權限，API 會針對每個入口(endpoint)來確認權限

目前有兩種方式來設置權限

- 針對 endpoint 

 ```
    @UserAuthInterceptor()
    @Query(() => StoreSearchResult)
    async stores(@Args() params: StoreSearchArgs):
    ...

 ```

  我們直接在 `@UserAuthInterceptor()` 中帶入權限即可

 ```
    @UserAuthInterceptor('store.read') // 也可以使用陣列 ['store.read']
    @Query(() => StoreSearchResult)
    async stores(@Args() params: StoreSearchArgs):
    ...

 ```

- 針對 `field`

在前方加上 `@Authorized('權限code', 預設值)` 

```
    @Authorized('store.email', '<EMAIL>')
    email?: string;
```