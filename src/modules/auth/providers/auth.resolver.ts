import { Utils } from '@clinico/clinico-node-framework';
import { Arg, Mutation, Resolver } from 'type-graphql';
import { Inject, Service } from 'typedi';
import { UserLoginResult } from '../types/auth.gql.type';
import { AuthService } from './auth.service';

@Service()
@Resolver()
export class AuthResolver {
    @Inject('AuthService')
    private authService: AuthService;

    @Mutation((returns) => UserLoginResult, { name: 'userLogin' })
    async userLogin(
        @Arg('SSOToken', (type) => String) ssoToken: string,
    ): Promise<UserLoginResult> {
        const payload = await this.authService.userLogin(ssoToken);
        const token = await Utils.JWT.sign(payload);
        return { ...payload, token };
    }
}
