import { Inject, Service } from 'typedi';
import { Utils } from '@clinico/clinico-node-framework';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { UserService } from '@/modules/user/providers/user.service';
import { client as RedisClient } from '@/common/databases/redis.database';
import { UserPayload, UserPermission } from '../types/auth.type';
import { PermissionService } from '@/modules/permission/permission/providers/permission.service';
import { CompanyService } from '@/modules/company/providers/company.service';
import { SalesTeamGroupService } from '@/modules/salesTeam/salesTeamGroup/providers/salesTeamGroup.service';
import { SalesTeamService } from '@/modules/salesTeam/salesTeam/providers/salesTeam.service';
import { RegionService } from '@/modules/region/providers/region.service';
import { RoleService } from '@/modules/permission/role/providers/role.service';
import _ from 'lodash';
import { ArgsDictionary } from 'type-graphql';
import { MetadataStorage } from '@/common/metadata/metadataStorage';
import { IdHelper } from '@/common/helpers/id.helper';
import configs from '@/configs';
import { ProductTeamService } from '@/modules/productTeam/providers/productTeam.service';
import { RefreshTokenLogService } from '@/modules/refreshTokenLog/providers/refreshTokenLog.service';
import { DeptService } from '@/modules/dept/providers/dept.service';

@Service('AuthService')
export class AuthService {
    @Inject('MetadataStorage')
    private metadataStorage: MetadataStorage;
    @Inject()
    private userService: UserService;
    @Inject()
    private roleService: RoleService;
    @Inject()
    private permissionService: PermissionService;
    @Inject()
    private regionService: RegionService;
    @Inject()
    private companyService: CompanyService;
    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @Inject()
    private salesTeamService: SalesTeamService;
    @Inject()
    private productTeamService: ProductTeamService;
    @Inject()
    private refreshTokenLogService: RefreshTokenLogService;
    @Inject()
    private deptService: DeptService;

    private redisPrefixes: { erp: string; salesRep: string };
    private redisTTL: number;

    constructor() {
        this.redisPrefixes = configs.redis.prefixes;
        this.redisTTL = configs.redis.TTL;
    }

    async userLogin(SSOToken: string): Promise<UserPayload> {
        try {
            const { userCode } = await Utils.SSO.verify(SSOToken);
            const user = await this.userService.findOneOrErrorByCode(userCode);

            // 紀錄每次交換Token的紀錄
            await this.refreshTokenLogService.create({ userId: user.id });

            return {
                id: user.id,
                code: user.code,
                name: user.name,
                email: user.email,
            };
        } catch (error) {
            if (error.message == 'Invalid SSO Token') {
                throw new BaseError(error.message, httpStatus.UNAUTHORIZED);
            } else {
                throw new BaseError(
                    error.message,
                    httpStatus.INTERNAL_SERVER_ERROR,
                );
            }
        }
    }

    async userPermissionCheck(
        userPermission: UserPermission,
        permissions: string[],
    ): Promise<void> {
        const result = !_.isEmpty(
            permissions.filter((permission) =>
                userPermission.codes.includes(permission),
            ),
        );
        if (!result) throw new BaseError('Permission denied!', 403);
    }

    /**
     * 驗證 Request Args是否合法
     */
    async authorizedArgs(
        userPermission: UserPermission,
        filterArgs: any,
    ): Promise<void> {
        // regionIds
        filterArgs.regionIds = IdHelper.intersectionIds(
            filterArgs.regionIds,
            userPermission.allowRegionIds,
        );

        // companyIds
        filterArgs.companyIds = IdHelper.intersectionIds(
            filterArgs.companyIds,
            userPermission.allowCompanyIds,
        );

        // salesTeamGroupIds
        filterArgs.salesTeamGroupIds = IdHelper.intersectionIds(
            filterArgs.salesTeamGroupIds,
            userPermission.allowSalesTeamGroupIds,
        );

        // salesTeamIds
        filterArgs.salesTeamIds = IdHelper.intersectionIds(
            filterArgs.salesTeamIds,
            userPermission.allowSalesTeamIds,
        );

        // productTeamIds
        filterArgs.productTeamIds = IdHelper.intersectionIds(
            filterArgs.productTeamIds,
            userPermission.allowProductTeamIds,
        );

        // userIds
        if (userPermission.allowUserIds.length > 0) {
            filterArgs.permissionUserIds = userPermission.allowUserIds;
        }

        // eyeFixedAssetServiceProviderIds
        if (userPermission.allowEyeFixedAssetsServiceProvidersIds.length > 0) {
            filterArgs.eyeFixedAssetServiceProviderIds =
                userPermission.allowEyeFixedAssetsServiceProvidersIds;
        }

        // assigneeDeptIds
        // 有主管權限(supervision_read)可透過查詢參數進行篩選
        if (!userPermission.codes.includes('service_order.supervision_read')) {
            const assigneeDeptIds = filterArgs.assigneeDeptIds ?? [];
            if (filterArgs.assigneeDeptId) {
                assigneeDeptIds.push(filterArgs.assigneeDeptId);
            }
            filterArgs.assigneeDeptIds = IdHelper.intersectionIds(
                assigneeDeptIds,
                userPermission.allowAssignDepartmentIds,
            );
            if (filterArgs.assigneeDeptIds.length == 0) {
                filterArgs.assigneeDeptIds =
                    userPermission.allowAssignDepartmentIds;
            }
        }
    }

    /**
     * 驗證 Response Fileds是否合法
     */
    async authorizedFields(
        userPermission: UserPermission,
        responese: any,
    ): Promise<void> {
        if (_.isNil(responese)) return;

        // // 當 responese 是 Class 的時候 (符合impl)
        if (responese.constructor.toString().match(/^class/)) {
            await this._authorizedFields(userPermission, responese);
        }

        // 當 responese 是 Array
        if (Array.isArray(responese) === true) {
            for (const row of responese) {
                await this._authorizedFields(userPermission, row);
            }
        }
    }

    private async _authorizedFields(
        userPermission: UserPermission,
        responese: any,
    ): Promise<void> {
        const authorizedFields = this.metadataStorage.authorizedFields.filter(
            (el) => el.target == responese.constructor.name,
        );
        for (const authorizedField of authorizedFields) {
            if (
                !userPermission.codes.includes(authorizedField.permissionCode)
            ) {
                responese[authorizedField.fieldName] =
                    authorizedField.defaultValue;
            }
        }
    }

    async findUserPermission(userId: number): Promise<UserPermission> {
        let permissionCodes: string[] = [];
        let allowRegionIds: number[] = [];
        let allowCompanyIds: number[] = [];
        let allowSalesTeamGroupIds: number[] = [];
        let allowSalesTeamIds: number[] = [];
        let allowProductTeamIds: number[] = [];
        let allowUserIds: number[] = [];
        let allowAssignDepartmentIds: number[] = [];
        let allowEyeFixedAssetsServiceProvidersIds: number[] = [];

        const roleResult = await this.roleService.search({
            userId,
        });
        const roles = roleResult.rows;

        for (const role of roles) {
            permissionCodes = permissionCodes.concat(
                role.rolesPermissions
                    .filter((el) => el.permission.applicationId == 1)
                    .map((el) => el.permission.code),
            );
            allowRegionIds = allowRegionIds.concat(role.allowRegionIds);
            allowCompanyIds = allowCompanyIds.concat(role.allowCompanyIds);
            allowSalesTeamGroupIds = allowSalesTeamGroupIds.concat(
                role.allowSalesTeamGroupIds,
            );
            allowSalesTeamIds = allowSalesTeamIds.concat(
                role.allowSalesTeamIds,
            );
            allowProductTeamIds = allowProductTeamIds.concat(
                role.allowProductTeamIds,
            );
            allowUserIds = allowUserIds.concat(role.allowUserIds);
            allowEyeFixedAssetsServiceProvidersIds =
                allowEyeFixedAssetsServiceProvidersIds.concat(
                    role.allowEyeFixedAssetsServiceProvidersIds,
                );
        }

        // 系統管理員
        if (roles.find((el) => el.isSystemAdmin == true)) {
            const allPermissions = await this.permissionService.findAllInERP();
            const allRegions = await this.regionService.findAll();
            const allCompanies = await this.companyService.findAll();
            const allSalesTeams = await this.salesTeamService.findAll();
            const allSalesTeamGroups =
                await this.salesTeamGroupService.findAll();
            const allProductTeams = await this.productTeamService.findAll();

            const excludedPermissions = new Set([
                'work_diary.read_engineer',
                'work_diary.read_manager',
                'work_diary.read_director',
                'service_order.read_engineer',
                'service_order.read_manager',
                'service_order.read_director',
            ]);
            permissionCodes = allPermissions.map((el) => el.code);
            permissionCodes = permissionCodes.filter(
                (code) => !excludedPermissions.has(code),
            );

            allowRegionIds = allRegions.map((el) => el.id);
            allowCompanyIds = allCompanies.map((el) => el.id);
            allowSalesTeamIds = allSalesTeams.map((el) => el.id);
            allowSalesTeamGroupIds = allSalesTeamGroups.map((el) => el.id);
            allowProductTeamIds = allProductTeams.map((el) => el.id);
            allowUserIds = [];
        }
        // 只允許顯示自己相關資料
        if (roles.find((el) => el.isOnlyOwnUser == true)) {
            allowUserIds = [userId];
        }
        // 全區域
        if (roles.find((el) => el.isAllowAllRegions == true)) {
            const allRegions = await this.regionService.findAll();
            allowRegionIds = allowRegionIds.concat(
                allRegions.map((el) => el.id),
            );
        }
        // 全部台灣公司
        if (roles.find((el) => el.isAllowAllTwCompanies == true)) {
            const allCompanies = await this.companyService.search({
                regionIds: [1],
            });
            allowCompanyIds = allowCompanyIds.concat(
                allCompanies.rows.map((el) => el.id),
            );
        }
        // 全部中國公司
        if (roles.find((el) => el.isAllowAllCnCompanies == true)) {
            const allCompanies = await this.companyService.search({
                regionIds: [2],
            });
            allowCompanyIds = allowCompanyIds.concat(
                allCompanies.rows.map((el) => el.id),
            );
        }
        // 全部業務團隊
        if (roles.find((el) => el.isAllowAllSalesTeamGroups == true)) {
            const allSalesTeams = await this.salesTeamService.findAll();
            const allSalesTeamGroups =
                await this.salesTeamGroupService.findAll();
            allowSalesTeamIds = allSalesTeams.map((el) => el.id);
            allowSalesTeamGroupIds = allSalesTeamGroups.map((el) => el.id);
        }
        // 全科明／明睿業務團隊
        if (roles.find((el) => el.isAllowAllTwnEcAndMrSalesTeam == true)) {
            const allSalesTeams = await this.salesTeamService.search({
                salesTeamGroupIds: [1],
            });
            allowSalesTeamIds = allowSalesTeamIds.concat(
                allSalesTeams.rows.map((el) => el.id),
            );
            allowSalesTeamGroupIds.push(1);
        }
        // 全電子耳業務團隊
        if (roles.find((el) => el.isAllowAllTwnClSalesTeam == true)) {
            const allSalesTeams = await this.salesTeamService.search({
                salesTeamGroupIds: [2],
            });
            allowSalesTeamIds = allowSalesTeamIds.concat(
                allSalesTeams.rows.map((el) => el.id),
            );
            allowSalesTeamGroupIds.push(2);
        }
        // 全臺灣眼科業務
        if (roles.find((el) => el.isAllowAllTwnEyeSalesTeam == true)) {
            const allSalesTeams = await this.salesTeamService.search({
                salesTeamGroupIds: [3],
            });
            allowSalesTeamIds = allowSalesTeamIds.concat(
                allSalesTeams.rows.map((el) => el.id),
            );
            allowSalesTeamGroupIds.push(3);
        }
        // 全中国眼科业务
        if (roles.find((el) => el.isAllowAllChnEyeSalesTeam == true)) {
            const allSalesTeams = await this.salesTeamService.search({
                salesTeamGroupIds: [4],
            });
            allowSalesTeamIds = allowSalesTeamIds.concat(
                allSalesTeams.rows.map((el) => el.id),
            );
            allowSalesTeamGroupIds.push(4);
        }

        const user = await this.userService.findOne(userId);
        const depts = await this.deptService.search({ managerId: userId });
        const allowAssignDepartments = await this.deptService.findChildren(
            depts.rows.map((data) => data.id),
        );
        allowAssignDepartmentIds = allowAssignDepartments.map(
            (data) => data.id,
        );
        if (allowAssignDepartmentIds.length == 0) {
            allowAssignDepartmentIds = user ? [user.departmentId] : [];
        } else if (user) {
            allowAssignDepartmentIds.push(user.departmentId);
        }

        return {
            codes: permissionCodes,
            allowRegionIds: IdHelper.uniqIds(allowRegionIds),
            allowCompanyIds: IdHelper.uniqIds(allowCompanyIds),
            allowSalesTeamGroupIds: IdHelper.uniqIds(allowSalesTeamGroupIds),
            allowSalesTeamIds: IdHelper.uniqIds(allowSalesTeamIds),
            allowProductTeamIds: IdHelper.uniqIds(allowProductTeamIds),
            allowUserIds: IdHelper.uniqIds(allowUserIds),
            allowAssignDepartmentIds: IdHelper.uniqIds(
                allowAssignDepartmentIds,
            ),
            allowEyeFixedAssetsServiceProvidersIds: IdHelper.uniqIds(
                allowEyeFixedAssetsServiceProvidersIds,
            ),
        };
    }

    async saveUserPermissionToRedis(userId: number): Promise<UserPermission> {
        const userPermission = await this.findUserPermission(userId);
        await RedisClient.set(
            `${this.redisPrefixes.erp}:${userId}`,
            JSON.stringify(userPermission),
            { EX: this.redisTTL },
        );
        return userPermission;
    }

    async findUserPermissionFromRedis(userId: number): Promise<UserPermission> {
        const result = await RedisClient.get(
            `${this.redisPrefixes.erp}:${userId}`,
        );
        if (_.isNil(result)) {
            return await this.saveUserPermissionToRedis(userId);
        }
        return JSON.parse(result);
    }

    async deleteUserPermissionFromRedis(userIds: number[]): Promise<void> {
        for (const userId of userIds) {
            await RedisClient.del(`${this.redisPrefixes.erp}:${userId}`);
            await RedisClient.del(`${this.redisPrefixes.salesRep}:${userId}`);
        }
    }

    async deleteAllUserPermissionFromRedis(): Promise<void> {
        const keys = await RedisClient.keys(`*:user-permission:*`);
        for (const key of keys) {
            await RedisClient.del(key);
        }
    }
}
