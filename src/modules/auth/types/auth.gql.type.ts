import { JWTResolver } from 'graphql-scalars';
import { Field, ID, ObjectType } from 'type-graphql';

@ObjectType()
export class UserLoginResult {
    @Field((type) => ID)
    id: number;

    @Field()
    code: string;

    @Field()
    name: string;

    @Field()
    email: string;

    @Field((type) => JWTResolver)
    token: string;
}

@ObjectType()
export class UserPermission {
    @Field((type) => [String], { description: '權限codes' })
    codes: string[];

    @Field((type) => [ID], { description: '允許的區域ids' })
    allowRegionIds: number[];

    @Field((type) => [ID], { description: '允許的公司ids' })
    allowCompanyIds: number[];

    @Field((type) => [ID], { description: '允許的業務團隊組織ids' })
    allowSalesTeamGroupIds: number[];

    @Field((type) => [ID], { description: '允許的業務團隊ids' })
    allowSalesTeamIds: number[];

    @Field((type) => [ID], { description: '允許的使用者ids' })
    allowUserIds: number[];

    @Field((type) => [ID], { description: '允許的PM團隊ids' })
    allowProductTeamIds: number[];
}
