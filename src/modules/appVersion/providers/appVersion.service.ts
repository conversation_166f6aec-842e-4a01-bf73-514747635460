import { Service } from 'typedi';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FindOptionsWhere, In } from 'typeorm';
import { CommonService } from '@/common/providers/common.service';
import { AppVersion } from '@clinico/typeorm-persistence/models/public/appVersion.model';
import { CommonSearchResult } from '@/common/types/common.type';
import { SearchParams, UpdateParams } from '../types/appVersion.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import Joi from 'joi';

@Service()
export class AppVersionService extends CommonService<AppVersion> {
    private appVersionRepo = ClinicoDataSource.getRepository(AppVersion);

    get commonDataNotFoundMessage(): string {
        return 'AppVersion not found';
    }

    async findOneByCode(code: string): Promise<AppVersion> {
        const appVersions = await this.search({ code: code });
        if (appVersions.count !== 1)
            throw new BaseError(this.commonDataNotFoundMessage, 404);

        return appVersions.rows[0];
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<AppVersion>> {
        const filters: FindOptionsWhere<AppVersion> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.code) {
            filters.code = params.code;
        }

        const data = await this.appVersionRepo.findAndCount({
            where: filters,
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async update(params: UpdateParams): Promise<AppVersion> {
        const appVersion = await this.findOneOrError(params.id);
        const appVersionToUpdate = await this.appVersionRepo.create({
            ...appVersion,
            ...params,
            id: appVersion.id,
        });

        const id = await ClinicoDataSource.transaction(async (manager) => {
            const customer = await manager.save(appVersionToUpdate);
            await this.validate(customer, manager);
            return customer.id;
        });
        return await this.findOneOrError(id);
    }

    async validate(
        appVersion: AppVersion,
        manager: EntityManager,
    ): Promise<void> {
        const schema = Joi.object<AppVersion>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(appVersion, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
