import { Inject, Service } from 'typedi';
import { Arg, Query, Resolver } from 'type-graphql';
import { AppVersionService } from './appVersion.service';
import { AppVersion } from '@/common/graphql/model/impl/public/appVersion.impl';

@Service()
@Resolver((of) => AppVersion)
export class AppVersionResolver {
    @Inject()
    private appVersionService: AppVersionService;

    @Query(() => AppVersion)
    async appVersion(@Arg('code') code: string): Promise<AppVersion> {
        return await this.appVersionService.findOneByCode(code);
    }
}
