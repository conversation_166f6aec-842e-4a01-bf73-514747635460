import {
    Body,
    JsonController,
    Param,
    Put,
    UseBefore,
} from 'routing-controllers';
import { UpdateParams } from '../types/appVersion.controller.type';
import Container from 'typedi';
import { AppVersionService } from './appVersion.service';
import axios from 'axios';
import { KoaAccessKeyAuthInterceptor } from '@/common/interceptors/serviceAuth.interceptor';

const appVersionService = Container.get(AppVersionService);

@JsonController('/appVersion')
@UseBefore(KoaAccessKeyAuthInterceptor)
export class AppVersionController {
    @Put('/:code')
    async updateCurrentVersion(
        @Body() params: UpdateParams,
        @Param('code') code: string,
    ): Promise<boolean> {
        const appVersion = await appVersionService.findOneByCode(code);
        await appVersionService.update({
            id: appVersion.id,
            currentVersion: params.currentVersion,
        });
        return true;
    }
}
