import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { AppVersion } from '@/common/graphql/model/impl/public/appVersion.impl';

@InputType()
class AppVersionSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field({ nullable: true })
    code?: string;
}

@ArgsType()
export class AppVersionSearchArgs extends CommonSearchArgs(
    AppVersionSearchInput,
) {}

@ObjectType()
export class AppVersionSearchResult extends PaginatedSearchResult(AppVersion) {}
