import moment from 'moment';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import configs from '@/configs';
import { Inject, Service } from 'typedi';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import { ExcelOutputSearchParams as SearchParams } from '../types/trainingScore.type';
import { TrainingScoreService } from './trainingScore.service';
import { TrainingScore } from '@/common/graphql/model/impl/salesRepWorkstation/trainingScore.impl';
import { TrainingScore as TrainingScoreModel } from '@clinico/typeorm-persistence/models/salesRepWorkstation/trainingScore.model';
import { BaseError } from '@clinico/base-error';
import { ClinicoDataSource } from '@/common/databases/clinico.database';

// 欄位映射表 - 對照 DB 欄位
const FIELD_MAPPING = {
    // 用code
    課程編碼: 'code',
    训练机构: 'trainingInstitution',
    内训外训: 'trainingType',
    課程名稱: 'courseName',
    讲师: 'instructor',
    产品类别: 'productCategory',
    产品及法规培训: 'productRegulationTraining',
    开始日期: 'courseStartDate',
    结束日期: 'courseEndDate',
    員工編號: 'userCode',
    培訓方式: 'present',
    '是否必須考核 (Y/N)': 'exam',
    成绩: 'employeeScore',
    考評次數: 'examCnt',
    最高考試成績: 'scoreMax',
    平均考試成績: 'scoreAvg',
    第一次考評時間: 'datetimeFirst',
    最佳成績的考評時間: 'datetimeHigh',
    最後一次考評時間: 'datetimeLast',
    最佳成績與考題建檔日的天數差: 'bestDays',
    '最佳成績考試所花的時間（分鐘）': 'bestMins',
    '是否及格 (Y/N)': 'pass',
    有无证书: 'hasCertificate',
    证书号码: 'certificateNumber',
    训练时数: 'trainingHours',
    '训练费用(NT$)': 'trainingCostNt',
};

// 只保留必要欄位檢查
const REQUIRED_FIELDS = ['courseName', 'userCode', 'courseStartDate'];

@Service()
export class TrainingScoreReportService {
    @Inject()
    private traingingScoreService: TrainingScoreService;
    private errors: string[] = [];
    async excel(params: SearchParams): Promise<Buffer> {
        const data = await this.data(params);
        const generator = new Utils.Excel2Templater();
        await generator.load(
            configs.templateFolder + `/excels/trainingScore/training.xlsx`,
        );

        generator.sheet('Sheet1');
        generator.fillRows({
            startRowIndex: 2,
            data: data,
            cellOptions: {
                font: {
                    size: 10,
                    name: 'Arial',
                },
                alignment: {
                    horizontal: 'left',
                    vertical: 'justify',
                },
            },
        });

        return await generator.saveToBuffer();
    }

    async data(params: SearchParams) {
        const trainings = await this.traingingScoreService.search({
            ...params,
        });
        const response = trainings.rows.map((s) => {
            return {
                // 不用courseCode用code當作課程編碼
                courseCode: s.code ?? '-',
                trainingInstitution: s.trainingInstitution ?? '-',
                trainingType: s?.trainingType ?? '-',
                courseCode2: s.code ?? '-',
                courseName: s?.courseName ?? '-',
                trainingType2: s?.trainingType ?? '-',
                instructor: s?.instructor ?? '-',
                productCategory: s?.productCategory ?? '-',
                productRegulationTraining: s?.productRegulationTraining ?? '-',
                startDate: s?.courseStartDate ?? '-',
                endDate: s?.courseEndDate ?? '-',
                employeeCode: s?.userCode ?? '-',
                trainingMethod: s?.present ?? '-',
                requiresAssessment: s?.exam ?? '-',
                score: s?.employeeScore ?? '-',
                assessmentCount: s?.examCnt ?? '-',
                highestScore: s?.scoreMax ?? '-',
                averageScore: s?.scoreAvg ?? '-',
                firstAssessmentTime: s?.datetimeFirst ?? '-',
                bestScoreAssessmentTime: s?.datetimeHigh ?? '-',
                lastAssessmentTime: s?.datetimeLast ?? '-',
                daysBetweenBestScoreAndFileDate: s?.bestDays ?? '-',
                bestScoreExamDuration: s?.bestMins ?? '-',
                isPassed: s?.pass ?? '-',
                hasCertificate: s?.hasCertificate ?? '-',
                certificateNumber: s?.certificateNumber ?? '-',
                trainingHours: s?.trainingHours ?? '-',
                trainingCost: s?.trainingCostNT ?? '-',
            };
        });
        return response;
    }
    /**
     * 使用 Excel2Templater 匯入 Excel 檔案並處理培訓資料
     */
    async importExcel(fileBuffer: Buffer): Promise<TrainingScore[] | null> {
        let tempFilePath: string | null = null;

        try {
            // 建立暫存檔案
            tempFilePath = await this.createTempFile(fileBuffer);

            // 使用 Excel2Templater 讀取檔案
            const rawData = await this.readExcelWithTemplater(tempFilePath);

            // 處理表頭映射
            const mappedData = this.mapHeaders(rawData);

            // 直接轉換資料格式，不經過驗證清理
            const processedData = this.processData(mappedData);

            return processedData;
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }

    /**
     * 建立暫存檔案
     */
    private async createTempFile(buffer: Buffer): Promise<string> {
        const tempDir = os.tmpdir();
        const tempFileName = `training_import_${Date.now()}_${Math.random()
            .toString(36)
            .substr(2, 9)}.xlsx`;
        const tempFilePath = path.join(tempDir, tempFileName);

        await fs.promises.writeFile(tempFilePath, buffer);
        return tempFilePath;
    }
    /**
     * 使用 Excel2Templater 讀取 Excel 檔案
     */
    private async readExcelWithTemplater(filePath: string): Promise<any[][]> {
        const excel = new Utils.Excel2Templater();
        await excel.load(filePath);

        const workbook = excel.getWorkbook();
        const sheets = excel.getAllSheets();

        if (sheets.length === 0) {
            throw new Error('Excel 檔案中沒有工作表');
        }

        // 使用第一個工作表
        const worksheet = workbook.getWorksheet(sheets[0].id);

        if (!worksheet) {
            throw new Error('無法取得工作表');
        }

        const data: any[][] = [];

        worksheet.eachRow((row: any, rowNumber: number) => {
            const rowData: any[] = [];

            row.eachCell((cell: any, colNumber: number) => {
                let value = cell.value;

                // 處理不同類型的值
                if (cell.type === 6) {
                    // Date
                    value = moment(value).format('YYYY-MM-DD');
                } else if (cell.type === 1) {
                    // Number
                    value = cell.value;
                } else if (cell.type === 2) {
                    // String
                    value = cell.value?.toString().trim();
                } else if (cell.type === 3) {
                    // Formula
                    value = cell.result || cell.value;
                }

                rowData[colNumber - 1] = value || '';
            });

            // 確保行資料不為空
            if (
                rowData.some(
                    (cell) =>
                        cell !== null && cell !== undefined && cell !== '',
                )
            ) {
                data.push(rowData);
            }
        });

        return data;
    }

    /**
     * 映射表頭
     */
    private mapHeaders(rawData: any[][]): any[] {
        if (rawData.length === 0) {
            throw new Error('Excel文件為空');
        }

        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // 創建映射後的表頭
        const mappedHeaders = headers.map((header) => {
            const trimmedHeader = header.toString().trim();
            return FIELD_MAPPING[trimmedHeader] || trimmedHeader;
        });

        // 簡化的必要欄位檢查
        const missingFields = REQUIRED_FIELDS.filter(
            (field) => !mappedHeaders.includes(field),
        );

        if (missingFields.length > 0) {
            this.errors.push(`缺少必要欄位: ${missingFields.join(', ')}`);
        }

        // 轉換為物件陣列
        return dataRows.map((row, index) => {
            const rowData: any = {};
            mappedHeaders.forEach((header, colIndex) => {
                rowData[header] = row[colIndex] || '';
            });
            rowData._rowIndex = index + 2; // Excel行號（從2開始）
            return rowData;
        });
    }

    /**
     * 處理最終資料 - 直接轉換，不進行驗證
     */
    private processData(data: any[]): TrainingScore[] {
        return data.map((row) => {
            const { _rowIndex, ...cleanRow } = row;
            return cleanRow as TrainingScore;
        });
    }

    /**
     * 資料庫操作：新增或更新培訓記錄
     */
    async upsertTrainingScores(
        processedData: TrainingScore[] | null,
    ): Promise<any> {
        try {
            await ClinicoDataSource.transaction(async (manager) => {
                if (processedData && processedData.length > 0) {
                    // 將資料轉換為 TrainingScore 實體格式
                    const entitiesToUpsert = processedData.map((data) => ({
                        // 主鍵欄位
                        courseName: data.courseName,
                        userCode: data.userCode,
                        courseStartDate: data.courseStartDate,

                        // 其他欄位
                        code: data.code,
                        trainingInstitution: data.trainingInstitution,
                        trainingType: data.trainingType,
                        instructor: data.instructor,
                        productCategory: data.productCategory,
                        productRegulationTraining:
                            data.productRegulationTraining,
                        courseEndDate: data.courseEndDate,
                        present: data.present,
                        exam: data.exam,
                        employeeScore: data.employeeScore,
                        examCnt: data.examCnt,
                        scoreMax: data.scoreMax,
                        scoreAvg: data.scoreAvg,
                        datetimeFirst: data.datetimeFirst,
                        datetimeHigh: data.datetimeHigh,
                        datetimeLast: data.datetimeLast,
                        bestDays: data.bestDays,
                        bestMins: data.bestMins,
                        pass: data.pass,
                        hasCertificate: data.hasCertificate,
                        certificateNumber: data.certificateNumber,
                        trainingHours: data.trainingHours,
                        trainingCostNt: data.trainingCostNT,
                    }));

                    // 使用 upsert 執行新增或更新
                    const upsertResult = await manager.upsert(
                        TrainingScoreModel, // 您的實體類別名稱
                        entitiesToUpsert,
                        {
                            conflictPaths: [
                                'courseName',
                                'userCode',
                                'courseStartDate',
                            ],
                            skipUpdateIfNoValuesChanged: true,
                        },
                    );

                    console.log(
                        'result',
                        `成功處理 ${entitiesToUpsert.length} 筆培訓記錄`,
                    );

                    return {
                        success: true,
                        affectedRows:
                            upsertResult.raw.affectedRows ||
                            entitiesToUpsert.length,
                        message: `成功處理 ${entitiesToUpsert.length} 筆培訓記錄`,
                    };
                }
            });
        } catch (error) {
            return {
                success: false,
                error: error.message,
                message: '資料庫操作失敗',
            };
        }
    }

    /**
     * 處理匯入的主要方法（結合資料庫操作）
     */
    async handleImport(fileBuffer: Buffer): Promise<null> {
        const result = await this.importExcel(fileBuffer);

        // 執行資料庫 upsert 操作
        await this.upsertTrainingScores(result);
        return null;
    }
}
