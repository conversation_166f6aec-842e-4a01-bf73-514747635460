import {
    Body,
    Get,
    JsonController,
    Param,
    Post,
    Put,
    UseBefore,
    Ctx,
    UploadedFile,
} from 'routing-controllers';
import * as fs from 'fs/promises';
import { OpenAPI } from 'routing-controllers-openapi';
import Koa from 'koa';
import Container from 'typedi';
import { TrainingScoreReportService } from './trainingScore.report.service';
import moment from 'moment';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { ExcelOutputSearchParams } from '../types/trainingScore.type';

const trainingScoreReportService = Container.get(TrainingScoreReportService);

@JsonController('/trainingScore')
@UseBefore(UserAuthKoaInterceptor)
export class TrainingScoreReportController {
    /**
     * Endpoint for user
     */
    @OpenAPI({
        summary: '培訓匯出',
    })
    @Post('/export')
    async export(
        @Ctx() ctx: Koa.Context,
        @Body() params: ExcelOutputSearchParams,
    ): Promise<Koa.Context> {
        const fileName = `trainingScore_${params.code}_${moment().format(
            'YYYY-MM-DD_HHmm',
        )}.xlsx`;
        const body = await trainingScoreReportService.excel({
            ...params,
        });

        ctx.set('Content-filename', fileName);
        ctx.set('Content-disposition', 'attachment; filename=' + fileName);
        ctx.set('Content-type', 'application/vnd.ms-excel');
        ctx.body = body;
        return ctx;
    }
}
