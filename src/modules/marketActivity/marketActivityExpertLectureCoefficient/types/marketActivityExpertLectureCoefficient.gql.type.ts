import { MarketActivityExpertFeeItem } from '@/common/graphql/model/impl/public/marketActivityExpertFeeItem.impl';
import { MarketActivityExpertLectureCoefficient } from '@/common/graphql/model/impl/public/marketActivityExpertLectureCoefficient.impl';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    ObjectType,
} from 'type-graphql';

@InputType()
class MarketActivityExpertLectureCoefficientSearchInput {
    @Field((type) => ID, { nullable: true })
    expertTypeId?: number;
    @Field((type) => ID, { nullable: true })
    expertServiceTimeId?: number;
}
@ArgsType()
export class MarketActivityExpertLectureCoefficientSearchArgs extends CommonSearchArgs(
    MarketActivityExpertLectureCoefficientSearchInput,
) {}

@ObjectType()
export class MarketActivityExpertLectureCoefficientSearchResult extends PaginatedSearchResult(
    MarketActivityExpertLectureCoefficient,
) {}
