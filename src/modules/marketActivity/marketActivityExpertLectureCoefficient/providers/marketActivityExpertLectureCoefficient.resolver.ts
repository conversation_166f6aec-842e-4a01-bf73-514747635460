import { Inject, Service } from 'typedi';
import { <PERSON>rgs, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import { MarketActivityExpertFeeItem } from '@/common/graphql/model/impl/public/marketActivityExpertFeeItem.impl';
import {
    MarketActivityExpertLectureCoefficientSearchArgs,
    MarketActivityExpertLectureCoefficientSearchResult,
} from '../types/marketActivityExpertLectureCoefficient.gql.type';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityExpertLectureCoefficientService } from './marketActivityExpertLectureCoefficient.service';
import { MarketActivityExpertType } from '@/common/graphql/model/impl/public/marketActivityExpertType.impl';
import { MarketActivityExpertLectureCoefficient } from '@/common/graphql/model/impl/public/marketActivityExpertLectureCoefficient.impl';
import { MarketActivityExpertTypeService } from '../../marketActivityExpertType/providers/marketActivityExpertType.service';
import { MarketActivityExpertServiceTime } from '@/common/graphql/model/impl/public/marketActivityExpertServiceTime.impl';
import { MarketActivityExpertServiceTimeService } from '../../marketActivityExpertServiceTime/providers/marketActivityExpertServiceTime.service';

@Service()
@Resolver((of) => MarketActivityExpertLectureCoefficient)
export class MarketActivityExpertLectureCoefficientTimeResolver {
    @Inject()
    private marketActivityExpertLectureCoefficientService: MarketActivityExpertLectureCoefficientService;
    @Inject()
    private marketActivityExpertTypeService: MarketActivityExpertTypeService;
    @Inject()
    private marketActivityExpertServiceTimeService: MarketActivityExpertServiceTimeService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityExpertLectureCoefficientSearchResult)
    async marketActivityExpertCoefficientItems(
        @Args() params: MarketActivityExpertLectureCoefficientSearchArgs,
    ): Promise<MarketActivityExpertLectureCoefficientSearchResult> {
        const result =
            await this.marketActivityExpertLectureCoefficientService.search({
                ...params.filters,
                ...params,
            });
        return <MarketActivityExpertLectureCoefficientSearchResult>result;
    }

    @FieldResolver((returns) => MarketActivityExpertType, { nullable: true })
    async expertType(
        @Root()
        marketActivityExpertLectureCoefficientItem: MarketActivityExpertLectureCoefficient,
    ): Promise<MarketActivityExpertType | null> {
        const result = await this.marketActivityExpertTypeService.findOne(
            marketActivityExpertLectureCoefficientItem.expertTypeId,
        );
        return result;
    }

    @FieldResolver((returns) => MarketActivityExpertServiceTime, {
        nullable: true,
    })
    async expertServiceTime(
        @Root()
        marketActivityExpertLectureCoefficientItem: MarketActivityExpertLectureCoefficient,
    ): Promise<MarketActivityExpertType | null> {
        const result =
            await this.marketActivityExpertServiceTimeService.findOne(
                marketActivityExpertLectureCoefficientItem.expertServiceTimeId,
            );
        return result;
    }
}
