import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { MarketActivityExpertFeeItem } from '@clinico/typeorm-persistence/models/public/marketActivityExpertFeeItem.model';
import { CommonSearchResult } from '@/common/types/common.type';
import { SearchParams } from '../types/marketActivityExpertLectureCoefficient.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityExpertLectureCoefficient } from '@clinico/typeorm-persistence/models/public/marketActivityExpertLectureCoefficient.model';

@Service()
export class MarketActivityExpertLectureCoefficientService extends CommonService<MarketActivityExpertLectureCoefficient> {
    private marketActivityExpertLectureCoefficientRepo =
        ClinicoDataSource.getRepository(MarketActivityExpertLectureCoefficient);

    get commonDataNotFoundMessage(): string {
        return 'MarketActivityExpertLectureCoefficient not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityExpertLectureCoefficient>> {
        const filters: FindOptionsWhere<MarketActivityExpertLectureCoefficient> =
            {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.expertTypeId) {
            filters.expertTypeId = params.expertTypeId;
        }
        if (params.expertServiceTimeId) {
            filters.expertServiceTimeId = params.expertServiceTimeId;
        }

        const data =
            await this.marketActivityExpertLectureCoefficientRepo.findAndCount({
                where: filters,
                skip: params.offset,
                take: params.limit,
                order: {
                    id: 'desc',
                },
            });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }
}
