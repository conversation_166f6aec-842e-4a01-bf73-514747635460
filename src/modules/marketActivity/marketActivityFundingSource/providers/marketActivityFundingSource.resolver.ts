import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    MarketActivityFundingSourceSearchArgs,
    MarketActivityFundingSourceSearchResult,
} from '../types/marketActivityFundingSource.gql.type';
import { MarketActivityFundingSourceService } from './marketActivityFundingSource.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityFundingSource } from '@/common/graphql/model/impl/public/marketActivityFundingSource.impl';

@Service()
@Resolver((of) => MarketActivityFundingSource)
export class MarketActivityFundingSourceResolver {
    @Inject()
    private marketActivityFundingSourceService: MarketActivityFundingSourceService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityFundingSourceSearchResult)
    async marketActivityFundingSources(
        @Args() params: MarketActivityFundingSourceSearchArgs,
    ): Promise<MarketActivityFundingSourceSearchResult> {
        const result = await this.marketActivityFundingSourceService.search({
            ...params.filters,
            ...params,
        });
        return <MarketActivityFundingSourceSearchResult>result;
    }
}
