import { Service } from 'typedi';
import { EntityManager, FindOptionsWhere, In } from 'typeorm';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { SearchParams } from '../types/marketActivityFundingSource.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { MarketActivityFundingSource } from '@clinico/typeorm-persistence/models/public/marketActivityFundingSource.model';

@Service()
export class MarketActivityFundingSourceService extends CommonService<MarketActivityFundingSource> {
    private marketActivityExpertLevelRepo = ClinicoDataSource.getRepository(
        MarketActivityFundingSource,
    );

    get commonDataNotFoundMessage(): string {
        return 'MarketActivityFundingSourceService not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityFundingSource>> {
        const filters: FindOptionsWhere<MarketActivityFundingSource> = {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.name) {
            filters.name = params.name;
        }
        if (params.code) {
            filters.code = params.code;
        }
        filters.deleted = false;

        const data = await this.marketActivityExpertLevelRepo.findAndCount({
            where: {
                ...filters,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }
}
