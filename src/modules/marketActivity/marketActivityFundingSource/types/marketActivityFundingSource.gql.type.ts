import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { MarketActivityFundingSource } from '@/common/graphql/model/impl/public/marketActivityFundingSource.impl';

@InputType()
class MarketActivityFundingSourceSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => String, { nullable: true })
    name?: string;

    @Field({ nullable: true })
    code?: string;
}

@ArgsType()
export class MarketActivityFundingSourceSearchArgs extends CommonSearchArgs(
    MarketActivityFundingSourceSearchInput,
) {}

@ObjectType()
export class MarketActivityFundingSourceSearchResult extends PaginatedSearchResult(
    MarketActivityFundingSource,
) {}
