import { MarketActivityActualExpertFeeItem } from '@/common/graphql/model/impl/public/marketActivityActualExpertFeeItem.impl';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    ObjectType,
} from 'type-graphql';

@InputType()
class MarketActivityActualExpertFeeItemSearchInput {
    @Field((type) => ID, { nullable: true })
    marketActivityId?: number;
}
@InputType()
export class MarketActivityActualExpertFeeItemCreateInput {
    @Field((type) => ID, { nullable: true })
    expertTypeId?: number;

    @Field((type) => String, { nullable: true })
    name?: string;

    @Field((type) => Float, { nullable: true })
    expense?: string;

    @Field({ nullable: true })
    memo?: string;

    @Field((type) => ID, { nullable: true })
    expertLevelId?: number;

    @Field((type) => ID, { nullable: true })
    expertServiceTimeId?: number;
}

@ArgsType()
export class MarketActivityActualExpertFeeItemSearchArgs extends CommonSearchArgs(
    MarketActivityActualExpertFeeItemSearchInput,
) {}

@ObjectType()
export class MarketActivityActualExpertFeeItemSearchResult extends PaginatedSearchResult(
    MarketActivityActualExpertFeeItem,
) {}
