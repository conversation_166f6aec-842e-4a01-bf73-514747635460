import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
} from '../types/marketActivityActualExpertFeeItem.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityActualExpertFeeItem } from '@clinico/typeorm-persistence/models/public/marketActivityActualExpertFeeItem.model';

@Service()
export class MarketActivityActualExpertFeeItemService extends CommonService<MarketActivityActualExpertFeeItem> {
    private marketActivityExpertFeeItemRepo = ClinicoDataSource.getRepository(
        MarketActivityActualExpertFeeItem,
    );

    get commonDataNotFoundMessage(): string {
        return 'MarketActivityExpertFeeItem not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityActualExpertFeeItem>> {
        const filters: FindOptionsWhere<MarketActivityActualExpertFeeItem> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.marketActivityId) {
            filters.marketActivityId = params.marketActivityId;
        }

        const data = await this.marketActivityExpertFeeItemRepo.findAndCount({
            where: filters,
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'asc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(
        params: CreateParams,
        entityManager: EntityManager,
    ): Promise<MarketActivityActualExpertFeeItem> {
        const marketActivityActualExpertFeeItemToCreate =
            this.marketActivityExpertFeeItemRepo.create({
                ...params,
            });
        await this.validate(marketActivityActualExpertFeeItemToCreate);
        const marketActivityActualExpertFeeItem = await entityManager.save(
            marketActivityActualExpertFeeItemToCreate,
        );
        return marketActivityActualExpertFeeItem;
    }

    async validate(
        marketActivityActualExpertFeeItem: MarketActivityActualExpertFeeItem,
    ): Promise<void> {
        const schema = Joi.object<MarketActivityActualExpertFeeItem>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(marketActivityActualExpertFeeItem, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
