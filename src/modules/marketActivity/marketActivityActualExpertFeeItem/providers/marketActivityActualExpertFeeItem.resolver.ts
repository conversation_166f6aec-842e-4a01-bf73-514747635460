import { Inject, Service } from 'typedi';
import { <PERSON>rgs, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import { MarketActivityExpertFeeItem } from '@/common/graphql/model/impl/public/marketActivityExpertFeeItem.impl';
import {
    MarketActivityActualExpertFeeItemSearchArgs,
    MarketActivityActualExpertFeeItemSearchResult,
} from '../types/marketActivityActualExpertFeeItem.gql.type';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityExpertType } from '@/common/graphql/model/impl/public/marketActivityExpertType.impl';
import { MarketActivityExpertTypeService } from '../../marketActivityExpertType/providers/marketActivityExpertType.service';
import { MarketActivityExpertServiceTimeService } from '../../marketActivityExpertServiceTime/providers/marketActivityExpertServiceTime.service';
import { MarketActivityExpertLevelService } from '../../marketActivityExpertLevel/providers/marketActivityExpertLevel.service';
import { MarketActivityExpertServiceTime } from '@/common/graphql/model/impl/public/marketActivityExpertServiceTime.impl';
import { MarketActivityExpertLevel } from '@/common/graphql/model/impl/public/marketActivityExpertLevel.impl';
import { MarketActivityActualExpertFeeItem } from '@/common/graphql/model/impl/public/marketActivityActualExpertFeeItem.impl';
import { MarketActivityActualExpertFeeItemService } from './marketActivityActualExpertFeeItem.service';

@Service()
@Resolver((of) => MarketActivityActualExpertFeeItem)
export class MarketActivityActualExpertFeeItemResolver {
    @Inject()
    private marketActivityActualExpertFeeItemService: MarketActivityActualExpertFeeItemService;
    @Inject()
    private marketActivityExpertTypeService: MarketActivityExpertTypeService;
    @Inject()
    private marketActivityExpertServiceTimeService: MarketActivityExpertServiceTimeService;
    @Inject()
    private marketActivityExpertLevelService: MarketActivityExpertLevelService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityActualExpertFeeItemSearchResult)
    async marketActivityExpertFeeItemItems(
        @Args() params: MarketActivityActualExpertFeeItemSearchArgs,
    ): Promise<MarketActivityActualExpertFeeItemSearchResult> {
        const result =
            await this.marketActivityActualExpertFeeItemService.search({
                ...params.filters,
                ...params,
            });
        return <MarketActivityActualExpertFeeItemSearchResult>result;
    }

    @FieldResolver((returns) => MarketActivityExpertType, { nullable: true })
    async expertType(
        @Root() marketActivityExpertFeeItem: MarketActivityExpertFeeItem,
    ): Promise<MarketActivityExpertType | null> {
        const result = await this.marketActivityExpertTypeService.findOne(
            marketActivityExpertFeeItem.expertTypeId,
        );
        return result;
    }

    @FieldResolver((returns) => MarketActivityExpertServiceTime, {
        nullable: true,
    })
    async expertServiceTime(
        @Root() marketActivityExpertFeeItem: MarketActivityExpertFeeItem,
    ): Promise<MarketActivityExpertServiceTime | null> {
        const result =
            await this.marketActivityExpertServiceTimeService.findOne(
                marketActivityExpertFeeItem.expertServiceTimeId,
            );
        return result;
    }

    @FieldResolver((returns) => MarketActivityExpertLevel, { nullable: true })
    async expertLevel(
        @Root() marketActivityExpertFeeItem: MarketActivityExpertFeeItem,
    ): Promise<MarketActivityExpertLevel | null> {
        const result = await this.marketActivityExpertLevelService.findOne(
            marketActivityExpertFeeItem.expertLevelId,
        );
        return result;
    }
}
