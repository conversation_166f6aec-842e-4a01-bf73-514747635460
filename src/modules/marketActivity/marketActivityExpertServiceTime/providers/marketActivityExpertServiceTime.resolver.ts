import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    MarketActivityExpertServiceTimeSearchArgs,
    MarketActivityExpertServiceTimeSearchResult,
} from '../types/marketActivityExpertServiceTime.gql.type';
import { MarketActivityExpertServiceTimeService } from './marketActivityExpertServiceTime.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityExpertServiceTime } from '@/common/graphql/model/impl/public/marketActivityExpertServiceTime.impl';

@Service()
@Resolver((of) => MarketActivityExpertServiceTime)
export class MarketActivityExpertServiceTimeResolver {
    @Inject()
    private marketActivityExpertServiceTimeService: MarketActivityExpertServiceTimeService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityExpertServiceTimeSearchResult)
    async marketActivityExpertServiceTimes(
        @Args() params: MarketActivityExpertServiceTimeSearchArgs,
    ): Promise<MarketActivityExpertServiceTimeSearchResult> {
        const result = await this.marketActivityExpertServiceTimeService.search(
            {
                ...params.filters,
                ...params,
            },
        );
        return <MarketActivityExpertServiceTimeSearchResult>result;
    }
}
