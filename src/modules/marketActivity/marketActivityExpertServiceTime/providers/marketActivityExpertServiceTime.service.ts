import { Service } from 'typedi';
import { <PERSON><PERSON><PERSON><PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { SearchParams } from '../types/marketActivityExpertServiceTime.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { MarketActivityExpertServiceTime } from '@clinico/typeorm-persistence/models/public/marketActivityExpertServiceTime.model';

@Service()
export class MarketActivityExpertServiceTimeService extends CommonService<MarketActivityExpertServiceTime> {
    private marketActivityExpertServiceTimeRepo =
        ClinicoDataSource.getRepository(MarketActivityExpertServiceTime);

    get commonDataNotFoundMessage(): string {
        return 'MarketActivityExpertServiceTimeService not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityExpertServiceTime>> {
        const filters: FindOptionsWhere<MarketActivityExpertServiceTime> = {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.name) {
            filters.name = params.name;
        }
        filters.deleted = false;

        const data =
            await this.marketActivityExpertServiceTimeRepo.findAndCount({
                where: {
                    ...filters,
                },
                skip: params.offset,
                take: params.limit,
                order: {
                    id: 'desc',
                },
            });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }
}
