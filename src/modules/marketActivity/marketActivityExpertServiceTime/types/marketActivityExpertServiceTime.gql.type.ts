import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { MarketActivityExpertServiceTime } from '@/common/graphql/model/impl/public/marketActivityExpertServiceTime.impl';

@InputType()
class MarketActivityExpertServiceTimeSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => String, { nullable: true })
    name?: string;
}

@ArgsType()
export class MarketActivityExpertServiceTimeSearchArgs extends CommonSearchArgs(
    MarketActivityExpertServiceTimeSearchInput,
) {}

@ObjectType()
export class MarketActivityExpertServiceTimeSearchResult extends PaginatedSearchResult(
    MarketActivityExpertServiceTime,
) {}
