import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    ObjectType,
} from 'type-graphql';
import {
    AttachmentCreateInput,
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { EnumMarketActivityApprovalStatus } from '@clinico/type-graphql-persistence/models/public/marketActivityApprovalMapping.model';
import { MarketActivityApprovalMapping } from '@/common/graphql/model/impl/public/marketActivityApprovalMapping.impl';

@InputType()
class MarketActivityApprovalMappingSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => ID, { nullable: true })
    marketActivityId?: number;

    @Field((type) => Int, {
        nullable: true,
        description: '市場活動單關卡等級',
    })
    level?: number;

    @Field((type) => ID, { nullable: true, description: '簽核人' })
    approvalUserId?: number;

    @Field((type) => EnumMarketActivityApprovalStatus, { nullable: true })
    approvalStatus: EnumMarketActivityApprovalStatus;
}

@ArgsType()
export class MarketActivityApprovalMappingSearchArgs extends CommonSearchArgs(
    MarketActivityApprovalMappingSearchInput,
) {}

@InputType()
export class MarketActivitySendApprovalInput {
    @Field((type) => ID)
    id: number;

    @Field((type) => EnumMarketActivityApprovalStatus, {
        nullable: true,
        description: '簽核狀態',
    })
    approvalStatus?: EnumMarketActivityApprovalStatus;
}

@ObjectType()
export class MarketActivityApprovalMappingSearchResult extends PaginatedSearchResult(
    MarketActivityApprovalMapping,
) {}
