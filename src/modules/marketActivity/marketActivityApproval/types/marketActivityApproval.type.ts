import {
    CommonSearchParams,
    CommonCreateParams,
    CommonUpdateParams,
    CommonDeletedParams,
} from '@/common/types/common.type';
import { EnumMarketActivityApprovalStatus } from '@clinico/typeorm-persistence/models/public/marketActivityApprovalMapping.model';

export type SearchParams = CommonSearchParams & {
    id?: number;
    marketActivityId?: number;
    level?: number;
    approvalUserId?: number;
    approvalStatus?: EnumMarketActivityApprovalStatus;
};

export type SendApprovalParams = CommonUpdateParams & {
    approvalStatus?: EnumMarketActivityApprovalStatus;
};
