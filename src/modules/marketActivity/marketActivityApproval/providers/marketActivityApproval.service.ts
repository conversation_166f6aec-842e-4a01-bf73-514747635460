import { OrganizationService } from '@/modules/organization/providers/organization.service';
import { Inject, Service } from 'typedi';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { MarketActivity } from '@clinico/typeorm-persistence/models/public/marketActivity.model';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { EnumMarketActivityStatus } from '@clinico/type-graphql-persistence/models/public/marketActivity.model';
import moment from 'moment';
import {
    SearchParams,
    SendApprovalParams,
} from '../types/marketActivityApproval.type';
import {
    EnumMarketActivityApprovalStatus,
    MarketActivityApprovalMapping,
} from '@clinico/typeorm-persistence/models/public/marketActivityApprovalMapping.model';
import { EntityManager, FindOptionsWhere, In, Not } from 'typeorm';
import { Supervisor } from '@/modules/organization/types/organization.type';
import { UserService } from '@/modules/user/providers/user.service';
import { MarketActivityApprovalRemindService } from './marketActivityApprovalRemind.mailer';
import { Helpers } from '@clinico/clinico-node-framework';
import { User } from '@clinico/typeorm-persistence/models/public/user.model';
import { CommonService } from '@/common/providers/common.service';
import {
    CommonSearchParams,
    CommonSearchResult,
} from '@/common/types/common.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { CodeService } from '@/modules/code/providers/code.service';
import { EnumCodeType } from '@/modules/code/types/code.interface';
import { CostCenter } from '@clinico/typeorm-persistence/models/public/costCenter.model';

@Service()
export class MarketActivityApprovalService extends CommonService<MarketActivityApprovalMapping> {
    @Inject()
    private organizationService: OrganizationService;
    @Inject()
    private userService: UserService;
    @Inject()
    private marketActivityApprovalRemindService: MarketActivityApprovalRemindService;
    @Inject()
    private codeService: CodeService;

    private marketActivityRepo =
        ClinicoDataSource.getRepository(MarketActivity);

    private marketActivityApprovalMappingRepo = ClinicoDataSource.getRepository(
        MarketActivityApprovalMapping,
    );

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityApprovalMapping>> {
        const filters: FindOptionsWhere<MarketActivityApprovalMapping> = {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.level) {
            filters.level = params.level;
        }
        if (params.approvalUserId) {
            filters.approvalUserId = params.approvalUserId;
        }
        if (params.approvalStatus) {
            filters.approvalStatus = params.approvalStatus;
        }

        const data = await this.marketActivityApprovalMappingRepo.findAndCount({
            where: {
                ...filters,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async findApprovalUser(
        marketActivityMappingId: number,
    ): Promise<User | null> {
        try {
            const marketAcitvityApprovalMapping =
                await this.marketActivityApprovalMappingRepo.findOneOrFail({
                    where: {
                        id: marketActivityMappingId,
                    },
                });

            return this.userService.findOneOrError(
                marketAcitvityApprovalMapping?.approvalUserId,
            );
        } catch (error) {
            console.error(
                `Error finding approval user for market activity ID ${marketActivityMappingId}:`,
                error,
            );
            throw error;
        }
    }

    // 建立市場活動簽核記錄
    async create(marketActivity: MarketActivity, manager: EntityManager) {
        const initiatedUser = await this.userService.findOne(
            marketActivity.primaryUserId,
        );
        if (!initiatedUser) {
            throw new Error('起單人不存在');
        }

        // 獲取各層級主管資訊
        const [businessManager, officeManager, regionalManager] =
            await Promise.all([
                this.organizationService.businessSupervisor({
                    companyId: initiatedUser.companyId,
                    deptId: initiatedUser.departmentId,
                }),
                this.organizationService.directSupervisor({
                    companyId: initiatedUser.companyId,
                    deptId: initiatedUser.departmentId,
                }),
                this.organizationService.regionalSupervisor({
                    companyId: initiatedUser.companyId,
                    deptId: initiatedUser.departmentId,
                }),
            ]);

        // 準備批核映射資料陣列
        const approvalMappings: {
            marketActivityId: number;
            level: number;
            approvalUserId: number;
            approvalStatus: number;
        }[] = [];

        // 處理 businessManager (level: 3)
        if (businessManager) {
            const businessUser = await this.userService.findOneByCode(
                businessManager.userCode,
            );
            if (businessUser) {
                approvalMappings.push({
                    marketActivityId: marketActivity.id,
                    level: 3,
                    approvalUserId: businessUser.id,
                    approvalStatus: EnumMarketActivityApprovalStatus.Processing,
                });
            }
        }

        // 處理 regionalManager (level: 2)
        if (regionalManager) {
            const regionalUser = await this.userService.findOneByCode(
                regionalManager.userCode,
            );
            if (regionalUser) {
                approvalMappings.push({
                    marketActivityId: marketActivity.id,
                    level: 2,
                    approvalUserId: regionalUser.id,
                    approvalStatus: EnumMarketActivityApprovalStatus.Processing,
                });
            }
        }

        // 處理 officeManager (level: 1)
        if (officeManager) {
            const officeUser = await this.userService.findOneByCode(
                officeManager.userCode,
            );
            if (officeUser) {
                approvalMappings.push({
                    marketActivityId: marketActivity.id,
                    level: 1,
                    approvalUserId: officeUser.id,
                    approvalStatus: EnumMarketActivityApprovalStatus.Processing,
                });
            }
        }

        // 批量新增批核映射資料
        if (approvalMappings.length > 0) {
            const marketActivityApprovalMappings = manager.create(
                MarketActivityApprovalMapping,
                approvalMappings,
            );
            await manager.save(marketActivityApprovalMappings);
            console.log('市場活動批核記錄建立成功');
        }
    }

    // 完整的市場活動簽核服務程式碼

    async findOneForMarketActivity(id: number): Promise<MarketActivity> {
        const marketActivities = await this.marketActivityRepo.find({
            where: {
                id: id,
                deleted: false,
            },
            relations: {
                createdUser: {
                    department: true,
                },
                type: true,
                participationType: true,
                nature: true,
                region: true,
                province: true,
                city: true,
                district: true,
                department: true,
                primaryUser: true,
                marketActivityExpertFeeItems: {
                    expertType: true,
                },
                marketActivityFeeItems: {
                    feeType: true,
                },
                marketActivityBusinessProducts: {
                    businessProduct: true,
                },
            },
        });
        if (marketActivities.length !== 1) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }
        return marketActivities[0];
    }

    // 優化後的 sendApproval 方法，支援連續處理
    async sendApproval(params: SendApprovalParams): Promise<boolean> {
        try {
            await ClinicoDataSource.transaction(async (manager) => {
                // 1. 更新市場活動狀態為處理中
                const marketActivityApproval = await manager.findOneByOrFail(
                    MarketActivityApprovalMapping,
                    { id: params.id },
                );

                const marketActivity = await manager.findOneByOrFail(
                    MarketActivity,
                    { id: marketActivityApproval.marketActivityId },
                );
                marketActivity.status = EnumMarketActivityStatus.Processing;
                await manager.save(marketActivity);

                // 2. 處理當前層級的簽核
                await this.createForMarketActivityInTransaction(
                    params,
                    marketActivity,
                    marketActivityApproval,
                    manager,
                );

                // 3. 如果是高層級主管，可能需要連續處理後續層級
                await this.processContinuousApproval(
                    params,
                    marketActivity,
                    marketActivityApproval,
                    manager,
                );

                // 檢查並更新最終狀態
                await this.checkAndUpdateFinalStatus(
                    marketActivity,
                    marketActivityApproval,
                    manager,
                );
            });

            return true;
        } catch (error) {
            console.error('簽核流程執行失敗:', error);
            throw error;
        }
    }

    // 根據前端參數更新簽核對應記錄
    private async updateApprovalMappingFromParams(
        params: SendApprovalParams,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        try {
            const { approvalStatus } = params;

            const approvalMapping = marketActivityApproval;
            // 更新簽核記錄
            if (approvalStatus !== undefined) {
                approvalMapping.approvalStatus = approvalStatus;
            }

            approvalMapping.approvalAt = new Date();

            await manager.save(approvalMapping);
            console.log(
                `成功更新簽核記錄: level=${marketActivityApproval.level}, status=${approvalStatus}`,
            );
        } catch (error) {
            console.error(
                `更新層級 ${marketActivityApproval.level} 簽核記錄時發生錯誤:`,
                error,
            );
            throw error;
        }
    }

    async createForMarketActivityInTransaction(
        params: SendApprovalParams,
        marketActivity: MarketActivity,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        // 批次處理簽核層級
        await this.processApprovalLevels(
            params,
            marketActivity,
            marketActivityApproval,
            manager,
        );
    }

    // 嚴格按照順序的簽核層級處理邏輯
    private async processApprovalLevels(
        params: SendApprovalParams,
        marketActivityForApproval: MarketActivity,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        const { primaryUserId } = marketActivityForApproval;
        const { level } = marketActivityApproval;

        // 先前端參數更新簽核記錄
        await this.updateApprovalMappingFromParams(
            params,
            marketActivityApproval,
            manager,
        );

        const initiatedUser = await this.userService.findOne(primaryUserId);
        if (!initiatedUser) return;
        // 只有當起單人是主管時，處理通過的簽核
        try {
            // 獲取各層級主管資訊
            const [businessManager, officeManager, regionalManager] =
                await Promise.all([
                    this.organizationService.businessSupervisor({
                        companyId: initiatedUser.companyId,
                        deptId: initiatedUser.departmentId,
                    }),
                    this.organizationService.directSupervisor({
                        companyId: initiatedUser.companyId,
                        deptId: initiatedUser.departmentId,
                    }),
                    this.organizationService.regionalSupervisor({
                        companyId: initiatedUser.companyId,
                        deptId: initiatedUser.departmentId,
                    }),
                ]);

            const managers = {
                businessManager,
                officeManager,
                regionalManager,
            };

            // 按順序處理簽核
            await this.processApprovalInSequence(
                level,
                managers,
                marketActivityForApproval,
                marketActivityApproval,
                manager,
            );
        } catch (error) {
            console.error('處理簽核層級時發生錯誤:', error);
            throw error;
        }
    }

    // 按順序處理簽核的核心邏輯
    private async processApprovalInSequence(
        currentLevel: number,
        managers: {
            businessManager: Supervisor | null;
            officeManager: Supervisor | null;
            regionalManager: Supervisor | null;
        },
        marketActivityForApproval: MarketActivity,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        const user = await this.userService.findOne(
            marketActivityForApproval.primaryUserId,
        );
        // 根據當前要處理的層級和用戶身份，決定處理邏輯
        switch (currentLevel) {
            case 1:
                await this.handleLevel1Approval(
                    user,
                    managers,
                    marketActivityApproval,
                    manager,
                );
                break;
            case 2:
                await this.handleLevel2Approval(
                    user,
                    managers,
                    marketActivityApproval,
                    manager,
                );
                break;
            case 3:
                await this.handleLevel3Approval(
                    user,
                    managers,
                    marketActivityApproval,
                    manager,
                );
                break;
            default:
                throw new Error(`無效的簽核層級: ${currentLevel}`);
        }
    }

    // 處理第一層級簽核
    private async handleLevel1Approval(
        user: any,
        managers: {
            businessManager: Supervisor | null;
            officeManager: Supervisor | null;
            regionalManager: Supervisor | null;
        },
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        // 如果起單人是地區經理，自動通過第一關
        if (managers.officeManager?.userCode === user.code) {
            await this.approveSingleLevel(1, marketActivityApproval, manager);
            console.log('起單人是地區經理，自動通過第一關');
            return;
        }

        // 如果起單人是大區經理，需要先通過第一關，但不能跳過
        if (managers.regionalManager?.userCode === user.code) {
            await this.approveSingleLevel(1, marketActivityApproval, manager);
            console.log('起單人是大區經理，通過第一關');
            return;
        }

        // 如果起單人是事業部經理，需要先通過第一關
        if (managers.businessManager?.userCode === user.code) {
            await this.approveSingleLevel(1, marketActivityApproval, manager);
            console.log('起單人是事業部經理，通過第一關');
            return;
        }
    }

    // 處理第二層級簽核
    private async handleLevel2Approval(
        user: any,
        managers: {
            businessManager: Supervisor | null;
            officeManager: Supervisor | null;
            regionalManager: Supervisor | null;
        },
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        // 如果起單人是大區經理，自動通過第二關
        if (managers.regionalManager?.userCode === user.code) {
            await this.approveSingleLevel(2, marketActivityApproval, manager);
            console.log('起單人是大區經理，自動通過第二關');
            return;
        }

        // 如果起單人是事業部經理，通過第二關
        if (managers.businessManager?.userCode === user.code) {
            await this.approveSingleLevel(2, marketActivityApproval, manager);
            console.log('起單人是事業部經理，通過第二關');
            return;
        }
    }

    // 處理第三層級簽核
    private async handleLevel3Approval(
        user: any,
        managers: {
            businessManager: Supervisor | null;
            officeManager: Supervisor | null;
            regionalManager: Supervisor | null;
        },
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        // 如果起單人是事業部經理，自動通過第三關
        if (managers.businessManager?.userCode === user.code) {
            await this.approveSingleLevel(3, marketActivityApproval, manager);
            console.log('起單人是事業部經理，自動通過第三關');
            return;
        }
    }

    // 處理連續簽核（高層級主管自動處理後續層級）
    private async processContinuousApproval(
        params: SendApprovalParams,
        marketActivity: MarketActivity,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        const marketActivityForApproval = marketActivity;
        const initiatedUser = await this.userService.findOne(
            marketActivityForApproval.primaryUserId,
        );
        if (!initiatedUser) {
            throw new Error('起單人不存在');
        }

        // 獲取主管資訊
        const [businessManager, regionalManager, officeManager] =
            await Promise.all([
                this.organizationService.businessSupervisor({
                    companyId: initiatedUser.companyId,
                    deptId: initiatedUser.departmentId,
                }),
                this.organizationService.regionalSupervisor({
                    companyId: initiatedUser.companyId,
                    deptId: initiatedUser.departmentId,
                }),
                this.organizationService.directSupervisor({
                    companyId: initiatedUser.companyId,
                    deptId: initiatedUser.departmentId,
                }),
            ]);

        // 檢查起單人身份並自動處理相應關卡
        await this.autoApproveBasedOnInitiatedUserRole(
            params,
            marketActivity,
            {
                businessManager,
                officeManager,
                regionalManager,
            },
            manager,
        );
    }
    // 新增：根據起單人身份自動通過相應關卡
    private async autoApproveBasedOnInitiatedUserRole(
        params: SendApprovalParams,
        marketActivity: MarketActivity,
        managers: {
            businessManager: Supervisor | null;
            officeManager: Supervisor | null;
            regionalManager: Supervisor | null;
        },
        manager: EntityManager,
    ): Promise<void> {
        // 1. 一次性取得所有需要的資料
        const [nowMarketActivityApprovals, createdUser] = await Promise.all([
            this.marketActivityApprovalMappingRepo.find({
                where: { marketActivityId: marketActivity.id },
                order: { level: 'ASC' },
            }),
            this.userService.findOne(marketActivity.primaryUserId),
        ]);

        // 2. 資料驗證
        if (
            !nowMarketActivityApprovals ||
            nowMarketActivityApprovals.length === 0
        ) {
            throw new Error('市場活動簽核記錄不存在');
        }
        if (!createdUser) {
            throw new Error('起單人不存在');
        }

        // 3. 決定需要自動通過的關卡數量
        let autoApproveCount = 0;
        let roleDescription = '';

        if (managers.businessManager?.userCode === createdUser.code) {
            autoApproveCount = 3;
            roleDescription = '事業部經理';
        } else if (managers.regionalManager?.userCode === createdUser.code) {
            autoApproveCount = 2;
            roleDescription = '大區經理';
        } else if (managers.officeManager?.userCode === createdUser.code) {
            autoApproveCount = 1;
            roleDescription = '地區經理';
        }

        if (autoApproveCount === 0) {
            return; // 不需要自動通過任何關卡
        }

        console.log(
            `起單人是${roleDescription}，自動通過前${autoApproveCount}關`,
        );

        // 4. 批次處理需要自動通過的簽核記錄
        const approvalsToUpdate = nowMarketActivityApprovals
            .slice(0, autoApproveCount)
            .filter(
                (approval) =>
                    approval.approvalStatus !==
                    EnumMarketActivityApprovalStatus.Approved,
            ); // 只處理尚未通過的

        if (approvalsToUpdate.length === 0) {
            return; // 沒有需要更新的記錄
        }

        // 5. 使用事務批次更新
        await manager.transaction(async (transactionalManager) => {
            const updatePromises = approvalsToUpdate.map((approval) => {
                const autoParams = {
                    ...params,
                    // 可以添加自動通過的標記
                    comment: `${roleDescription}自動通過`,
                    isAutoApproved: true,
                };
                return this.updateApprovalMappingFromParams(
                    autoParams,
                    approval,
                    transactionalManager,
                );
            });

            await Promise.all(updatePromises);
        });
    }

    // 優化後的 approveSingleLevel 方法
    private async approveSingleLevel(
        level: number,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        try {
            const approvalMapping = marketActivityApproval;
            // 檢查是否已經通過，避免重複處理
            if (
                approvalMapping.approvalStatus ===
                EnumMarketActivityApprovalStatus.Approved
            ) {
                console.log(`層級 ${level} 已經通過，跳過處理`);
                return;
            }

            approvalMapping.approvalStatus =
                EnumMarketActivityApprovalStatus.Approved;
            approvalMapping.approvalAt = new Date();

            await manager.save(approvalMapping);
            console.log(`成功通過層級 ${level}`);
        } catch (error) {
            console.error(`通過層級 ${level} 時發生錯誤:`, error);
            throw error;
        }
    }

    // 檢查並更新最終狀態
    private async checkAndUpdateFinalStatus(
        marketActivityForApproval: MarketActivity,
        marketActivityApproval: MarketActivityApprovalMapping,
        manager: EntityManager,
    ): Promise<void> {
        // 批次查詢所有簽核層級狀態
        // 使用事務管理器查詢，而不是直接查詢資料庫
        const approvalMappings = await manager.find(
            MarketActivityApprovalMapping,
            {
                where: {
                    marketActivityId: marketActivityApproval.marketActivityId,
                },
            },
        );

        // 首先檢查是否有任何層級被拒絕
        const hasRejected = approvalMappings.some(
            (mapping) =>
                mapping.approvalStatus ===
                EnumMarketActivityApprovalStatus.Rejected,
        );

        if (hasRejected) {
            const marketActivity = marketActivityForApproval;
            marketActivity.status = EnumMarketActivityStatus.Rejected;
            await manager.save(marketActivity);
            console.log('發現拒絕的簽核，市場活動狀態更新為已拒絕');
            return; // 直接返回，不寄信
        }

        // 檢查是否所有層級都已通過
        const allApproved = approvalMappings.every(
            (mapping) =>
                mapping.approvalStatus ===
                EnumMarketActivityApprovalStatus.Approved,
        );

        if (allApproved) {
            // 先找到成本中心代碼
            const costCenter = await manager.findOne(CostCenter, {
                where: {
                    id: marketActivityForApproval.costCenterId,
                    isActive: true,
                    deleted: false,
                },
            });

            const marketActivity = marketActivityForApproval;
            marketActivity.status = EnumMarketActivityStatus.Approved;
            marketActivity.executionCode =
                await this.codeService.marketActivityCode(
                    {
                        type: EnumCodeType.MarketActivityExecution,
                        regionId: 2,
                        values: costCenter?.code,
                    },
                    manager,
                );

            await manager.save(marketActivity);
            console.log('所有簽核層級已通過，市場活動狀態更新為已核准');

            // 🔥 只有在全部通過時才寄信
            await this.sendApprovalCompletionEmail(marketActivity);
        }

        // 如果既沒有拒絕也沒有全部通過，則保持 Processing 狀態
    }

    // 新增專門處理寄信的私有方法
    private async sendApprovalCompletionEmail(
        marketActivity: MarketActivity,
    ): Promise<void> {
        try {
            const recipients: string[] = [];
            if (!Helpers.Env.isProduction()) {
                recipients.push('<EMAIL>');
            } else {
                // 正式區寄信給财务专员和Thomas
                recipients.push(...['<EMAIL>']);
            }

            await this.marketActivityApprovalRemindService.sendRemindNotice({
                marketActivityStatus: marketActivity.status,
                to: recipients,
                marketActivityId: marketActivity.id,
            });

            console.log(`市場活動 ${marketActivity.id} 簽核完成通知信已發送`);
        } catch (error) {
            console.error('發送簽核完成通知信失敗:', error);
            // 這裡可以選擇是否要拋出錯誤，或者只記錄錯誤但不影響主流程
        }
    }

    // 找出該marketActivity的簽核記錄狀態非Approved的層級，找出最小關卡level，就是當單marketActivity的簽核狀態及待簽的關卡
    async findWaitToApprveMarketActivityAppproval(
        marketActivityId: number,
    ): Promise<MarketActivityApprovalMapping | null> {
        if (!marketActivityId || marketActivityId <= 0) {
            throw new Error('Invalid marketActivityId');
        }

        try {
            return await this.marketActivityApprovalMappingRepo.findOne({
                where: {
                    marketActivityId: marketActivityId,
                    approvalStatus: Not(
                        EnumMarketActivityApprovalStatus.Approved,
                    ),
                },
                order: {
                    level: 'ASC',
                },
            });
        } catch (error) {
            console.error(
                `Error finding wait to approve market activity approval for ID ${marketActivityId}:`,
                error,
            );
            throw error;
        }
    }
}
