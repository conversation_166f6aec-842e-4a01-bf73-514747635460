import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import {
    MarketActivityApprovalMappingSearchArgs,
    MarketActivityApprovalMappingSearchResult,
    MarketActivitySendApprovalInput,
} from '../types/marketActivityApproval.gql.type';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { MarketActivityApprovalService } from './marketActivityApproval.service';
import { MarketActivityApprovalMapping } from '@/common/graphql/model/impl/public/marketActivityApprovalMapping.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';

@Service()
@Resolver((of) => MarketActivityApprovalMapping)
export class MarketActivityApprovalMappingResolver {
    @Inject()
    private marketActivityApprovalService: MarketActivityApprovalService;

    @UserAuthInterceptor('marketActivityApproval.read')
    @Query(() => MarketActivityApprovalMappingSearchResult, {
        nullable: true,
        description: '市場活動簽核列表',
    })
    async marketActivitityApprovals(
        @Args() params: MarketActivityApprovalMappingSearchArgs,
    ): Promise<MarketActivityApprovalMappingSearchResult> {
        const result = await this.marketActivityApprovalService.search({
            ...params.filters,
            ...params,
        });
        return <MarketActivityApprovalMappingSearchResult>result;
    }

    @UserAuthInterceptor('marketActivityApproval.update')
    @Mutation((returns) => Boolean)
    async marketActivitySendApproval(
        @Arg('input') input: MarketActivitySendApprovalInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.marketActivityApprovalService.sendApproval({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @FieldResolver((returns) => User, {
        nullable: true,
        description: '該市場活動單的簽核人',
    })
    async approvalUser(
        @Root() marketActivityApproval: MarketActivityApprovalMapping,
    ): Promise<User | null> {
        const result =
            await this.marketActivityApprovalService.findApprovalUser(
                marketActivityApproval.id,
            );
        return result;
    }
}
