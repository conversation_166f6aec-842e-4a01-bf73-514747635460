import configs from '@/configs';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import { EnumState } from '@clinico/typeorm-persistence/models/salesRepWorkstation/weeklyWorkReport.model';
import { EnumMarketActivityStatus } from '@clinico/typeorm-persistence/models/public/marketActivity.model';
import { Service } from 'typedi';

@Service()
export class MarketActivityApprovalRemindService {
    private marketActivityApprovalWebUrl: string;
    private marketActivityApprovalWebUrlSalesWebUrl: string;

    constructor() {
        this.marketActivityApprovalWebUrl =
            configs.app.webUrl + '/campaigns/detail';
        this.marketActivityApprovalWebUrlSalesWebUrl =
            configs.app.salesWebUrl + '/campaigns/detail';
    }

    /**
     * 签核完成后，邮件通知申请人，财务端抄送【财务专员】和【Thomas】
     */

    async sendRemindNotice(params: {
        marketActivityStatus: EnumMarketActivityStatus;
        to: string[];
        marketActivityId: number;
    }) {
        let subject = '您的签核提醒';
        if (!Helpers.Env.isProduction()) {
            subject = `[TEST] ${subject}`;
        }

        const url =
            this.marketActivityApprovalWebUrl +
            `?id=${params.marketActivityId}`;

        if (params.marketActivityStatus === EnumMarketActivityStatus.Approved) {
            subject = subject + ':您有一筆審核通過的签核';
        }
        const body = `您好，<br>您有一筆待查看的市場活動 ${url}</br>`;
        await Utils.Mailer.sendByAccount({
            account: 'notify',
            to: params.to.join(','),
            subject: subject,
            body: body,
        });
    }
}
