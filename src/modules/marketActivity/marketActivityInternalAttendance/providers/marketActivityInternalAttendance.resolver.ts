import { Inject, Service } from 'typedi';
import { <PERSON>rgs, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import { MarketActivityExpertFeeItem } from '@/common/graphql/model/impl/public/marketActivityExpertFeeItem.impl';
import {
    MarketActivityInternalAttendanceSearchArgs,
    MarketActivityInternalAttendanceSearchResult,
} from '../types/marketActivityInternalAttendance.gql.type';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityInternalAttendanceService } from './marketActivityInternalAttendance.service';
import { CostCenter } from '@/common/graphql/model/impl/public/costCenter.impl';
import { MarketActivityInternalAttendance } from '@/common/graphql/model/impl/public/marketActivityInternalAttendance.impl';
import { In } from 'typeorm';
import { CostCenterService } from '@/modules/costCenter/providers/costCenter.service';
import { DeptService } from '@/modules/dept/providers/dept.service';
import { Department } from '@/common/graphql/model/impl/public/department.impl';

@Service()
@Resolver((of) => MarketActivityInternalAttendance)
export class MarketActivityInternalAttendanceResolver {
    @Inject()
    private marketActivityInternalAttendanceService: MarketActivityInternalAttendanceService;
    @Inject()
    private costCenterService: CostCenterService;
    @Inject()
    private departmentService: DeptService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityInternalAttendanceSearchResult)
    async marketActivityInternalAttendances(
        @Args() params: MarketActivityInternalAttendanceSearchArgs,
    ): Promise<MarketActivityInternalAttendanceSearchResult> {
        const result =
            await this.marketActivityInternalAttendanceService.search({
                ...params.filters,
                ...params,
            });
        return <MarketActivityInternalAttendanceSearchResult>result;
    }

    @FieldResolver((returns) => CostCenter, { nullable: true })
    async costCenter(
        @Root()
        marketActivityInternalAttendance: MarketActivityInternalAttendance,
    ): Promise<CostCenter | null> {
        const result = await this.costCenterService.findOne(
            marketActivityInternalAttendance.costCenterId,
        );
        return result;
    }

    @FieldResolver((returns) => Department, { nullable: true })
    async department(
        @Root()
        marketActivityInternalAttendance: MarketActivityInternalAttendance,
    ): Promise<Department | null> {
        const result = await this.departmentService.findOne(
            marketActivityInternalAttendance.departmentId,
        );
        return result;
    }
}
