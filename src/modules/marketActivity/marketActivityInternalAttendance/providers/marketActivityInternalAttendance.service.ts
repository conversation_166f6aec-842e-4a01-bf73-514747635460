import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
} from '../types/marketActivityInternalAttendance.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityInternalAttendance } from '@clinico/typeorm-persistence/models/public/marketActivityInternalAttendance.model';

@Service()
export class MarketActivityInternalAttendanceService extends CommonService<MarketActivityInternalAttendance> {
    private marketActivityInternalAttendanceRepo =
        ClinicoDataSource.getRepository(MarketActivityInternalAttendance);

    get commonDataNotFoundMessage(): string {
        return 'marketActivityInternalAttendance not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityInternalAttendance>> {
        const filters: FindOptionsWhere<MarketActivityInternalAttendance> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.marketActivityId) {
            filters.marketActivityId = params.marketActivityId;
        }
        if (params.departmentId) {
            filters.departmentId = params.departmentId;
        }
        if (params.costCenterId) {
            filters.costCenterId = params.costCenterId;
        }

        const data =
            await this.marketActivityInternalAttendanceRepo.findAndCount({
                where: filters,
                skip: params.offset,
                take: params.limit,
                order: {
                    id: 'asc',
                },
            });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(
        params: CreateParams,
        entityManager: EntityManager,
    ): Promise<MarketActivityInternalAttendance> {
        const marketActivityInternalAttendanceToCreate =
            this.marketActivityInternalAttendanceRepo.create({
                ...params,
            });
        await this.validate(marketActivityInternalAttendanceToCreate);
        const marketActivityInternalAttendance = await entityManager.save(
            marketActivityInternalAttendanceToCreate,
        );
        return marketActivityInternalAttendance;
    }

    async validate(
        marketActivityInternalAttendance: MarketActivityInternalAttendance,
    ): Promise<void> {
        const schema = Joi.object<MarketActivityInternalAttendance>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(marketActivityInternalAttendance, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
