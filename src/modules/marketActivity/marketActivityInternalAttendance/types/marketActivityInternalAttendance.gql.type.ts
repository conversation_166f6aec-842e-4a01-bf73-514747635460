import { MarketActivityInternalAttendance } from '@/common/graphql/model/impl/public/marketActivityInternalAttendance.impl';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    ObjectType,
} from 'type-graphql';

@InputType()
class MarketActivityInternalAttendanceSearchInput {
    @Field(() => ID, { nullable: true, description: '市场活动' })
    marketActivityId?: number;

    @Field(() => ID, { nullable: true, description: '部门' })
    departmentId?: number;

    @Field(() => ID, { nullable: true, description: '成本中心' })
    costCenterId?: number;
}
@InputType()
export class MarketActivityInternalAttendanceCreateInput {
    @Field((type) => ID, { nullable: true })
    departmentId?: number;

    @Field((type) => ID, { nullable: true })
    costCenterId?: number;

    @Field((type) => Int, {
        nullable: true,
        description: '實際內部參會人內部人數',
    })
    internalCount?: number;

    @Field((type) => Float, {
        nullable: true,
        description: '實際內部參會人餐會費用',
    })
    estimatedCost?: string;
}

@ArgsType()
export class MarketActivityInternalAttendanceSearchArgs extends CommonSearchArgs(
    MarketActivityInternalAttendanceSearchInput,
) {}

@ObjectType()
export class MarketActivityInternalAttendanceSearchResult extends PaginatedSearchResult(
    MarketActivityInternalAttendance,
) {}
