import { MarketActivityActualFeeItem } from '@/common/graphql/model/impl/public/marketActivityActualFeeItem.impl';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    ObjectType,
} from 'type-graphql';

@InputType()
class MarketActivityActualFeeItemSearchInput {
    @Field((type) => ID, { nullable: true })
    marketActivityId?: number;
}
@InputType()
export class MarketActivityActualFeeItemCreateInput {
    @Field((type) => ID, { nullable: true })
    feeTypeId?: number;

    @Field((type) => Float, { nullable: true })
    expense?: string;

    @Field({ nullable: true })
    memo?: string;
}

@ArgsType()
export class MarketActivityActualFeeItemSearchArgs extends CommonSearchArgs(
    MarketActivityActualFeeItemSearchInput,
) {}

@ObjectType()
export class MarketActivityActualFeeItemSearchResult extends PaginatedSearchResult(
    MarketActivityActualFeeItem,
) {}
