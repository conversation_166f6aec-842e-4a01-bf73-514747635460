import { Inject, Service } from 'typedi';
import { Args, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import {
    MarketActivityActualFeeItemSearchArgs,
    MarketActivityActualFeeItemSearchResult,
} from '../types/marketActivityActualFeeItem.gql.type';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityActualFeeItemService } from './marketActivityActualFeeItem.service';
import { MarketActivityFeeTypeService } from '../../marketActivityFeeType/providers/marketActivityFeeType.service';
import { MarketActivityFeeType } from '@/common/graphql/model/impl/public/marketActivityFeeType.impl';
import { MarketActivityActualFeeItem } from '@/common/graphql/model/impl/public/marketActivityActualFeeItem.impl';

@Service()
@Resolver((of) => MarketActivityActualFeeItem)
export class MarketActivityActualFeeItemResolver {
    @Inject()
    private MarketActivityActualFeeItemService: MarketActivityActualFeeItemService;
    @Inject()
    private marketActivityFeeTypeService: MarketActivityFeeTypeService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityActualFeeItemSearchResult)
    async MarketActivityActualFeeItems(
        @Args() params: MarketActivityActualFeeItemSearchArgs,
    ): Promise<MarketActivityActualFeeItemSearchResult> {
        const result = await this.MarketActivityActualFeeItemService.search({
            ...params.filters,
            ...params,
        });
        return <MarketActivityActualFeeItemSearchResult>result;
    }

    @FieldResolver((returns) => MarketActivityFeeType, { nullable: true })
    async feeType(
        @Root() MarketActivityActualFeeItemItem: MarketActivityActualFeeItem,
    ): Promise<MarketActivityFeeType | null> {
        const result = await this.marketActivityFeeTypeService.findOne(
            MarketActivityActualFeeItemItem.feeTypeId,
        );
        return result;
    }
}
