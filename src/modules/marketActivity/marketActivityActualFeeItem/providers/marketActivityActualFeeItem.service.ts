import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
} from '../types/marketActivityActualFeeItem.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityActualFeeItem } from '@clinico/typeorm-persistence/models/public/marketActivityActualFeeItem.model';

@Service()
export class MarketActivityActualFeeItemService extends CommonService<MarketActivityActualFeeItem> {
    private marketActivityActualFeeItemRepo = ClinicoDataSource.getRepository(
        MarketActivityActualFeeItem,
    );

    get commonDataNotFoundMessage(): string {
        return 'MarketActivityActualFeeItem not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityActualFeeItem>> {
        const filters: FindOptionsWhere<MarketActivityActualFeeItem> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.marketActivityId) {
            filters.marketActivityId = params.marketActivityId;
        }

        const data = await this.marketActivityActualFeeItemRepo.findAndCount({
            where: filters,
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'asc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(
        params: CreateParams,
        entityManager: EntityManager,
    ): Promise<MarketActivityActualFeeItem> {
        const marketActivityActualFeeItemToCreate =
            this.marketActivityActualFeeItemRepo.create({
                ...params,
            });
        await this.validate(marketActivityActualFeeItemToCreate);
        const marketActivityFeeItem = await entityManager.save(
            marketActivityActualFeeItemToCreate,
        );
        return marketActivityFeeItem;
    }

    async validate(
        marketActivityActualFeeItem: MarketActivityActualFeeItem,
    ): Promise<void> {
        const schema = Joi.object<MarketActivityActualFeeItem>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(marketActivityActualFeeItem, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
