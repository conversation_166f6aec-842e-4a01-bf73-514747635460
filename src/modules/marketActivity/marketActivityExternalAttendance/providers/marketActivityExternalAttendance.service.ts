import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
} from '../types/marketActivityExternalAttendance.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityExternalAttendance } from '@clinico/typeorm-persistence/models/public/marketActivityExternalAttendance.model';

@Service()
export class MarketActivityExternalAttendanceService extends CommonService<MarketActivityExternalAttendance> {
    private marketActivityExternalAttendanceRepo =
        ClinicoDataSource.getRepository(MarketActivityExternalAttendance);

    get commonDataNotFoundMessage(): string {
        return 'marketActivityExternalAttendance not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityExternalAttendance>> {
        const filters: FindOptionsWhere<MarketActivityExternalAttendance> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.marketActivityId) {
            filters.marketActivityId = params.marketActivityId;
        }
        if (params.customerId) {
            filters.customerId = params.customerId;
        }

        const data =
            await this.marketActivityExternalAttendanceRepo.findAndCount({
                where: filters,
                skip: params.offset,
                take: params.limit,
                order: {
                    id: 'asc',
                },
            });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(
        params: CreateParams,
        entityManager: EntityManager,
    ): Promise<MarketActivityExternalAttendance> {
        const marketActivityExternalAttendanceToCreate =
            this.marketActivityExternalAttendanceRepo.create({
                ...params,
            });
        await this.validate(marketActivityExternalAttendanceToCreate);
        const marketActivityExternalAttendance = await entityManager.save(
            marketActivityExternalAttendanceToCreate,
        );
        return marketActivityExternalAttendance;
    }

    async validate(
        marketActivityExternalAttendance: MarketActivityExternalAttendance,
    ): Promise<void> {
        const schema = Joi.object<MarketActivityExternalAttendance>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(marketActivityExternalAttendance, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
