import { Inject, Service } from 'typedi';
import { <PERSON>rgs, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import {
    MarketActivityExternalAttendanceSearchArgs,
    MarketActivityExternalAttendanceSearchResult,
} from '../types/marketActivityExternalAttendance.gql.type';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityExternalAttendanceService } from './marketActivityExternalAttendance.service';
import { CustomerService } from '@/modules/customer/customer/providers/customer.service';
import { MarketActivityExternalAttendance } from '@/common/graphql/model/impl/public/marketActivityExternalAttendance.impl';
import { Customer } from '@/common/graphql/model/impl/salesRepWorkstation/customer.impl';

@Service()
@Resolver((of) => MarketActivityExternalAttendance)
export class MarketActivityExternalAttendanceResolver {
    @Inject()
    private marketActivityExternalAttendanceService: MarketActivityExternalAttendanceService;
    @Inject()
    private customerService: CustomerService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityExternalAttendanceSearchResult)
    async marketActivityExternalAttendances(
        @Args() params: MarketActivityExternalAttendanceSearchArgs,
    ): Promise<MarketActivityExternalAttendanceSearchResult> {
        const result =
            await this.marketActivityExternalAttendanceService.search({
                ...params.filters,
                ...params,
            });
        return <MarketActivityExternalAttendanceSearchResult>result;
    }

    @FieldResolver((returns) => Customer, { nullable: true })
    async customer(
        @Root()
        marketActivityExternalAttendance: MarketActivityExternalAttendance,
    ): Promise<Customer | null> {
        const result = await this.customerService.findOne(
            marketActivityExternalAttendance.customerId,
        );
        return result;
    }
}
