import { Inject, Service } from 'typedi';
import { EntityManager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
} from '../types/marketActivityExternalRegistration.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityExternalRegistration } from '@clinico/typeorm-persistence/models/public/marketActivityExternalRegistration.model';

@Service()
export class MarketActivityExternalRegistrationService extends CommonService<MarketActivityExternalRegistration> {
    private marketActivityExternalRegistrationRepo =
        ClinicoDataSource.getRepository(MarketActivityExternalRegistration);

    get commonDataNotFoundMessage(): string {
        return 'marketActivityExternalRegistration not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityExternalRegistration>> {
        const filters: FindOptionsWhere<MarketActivityExternalRegistration> =
            {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.marketActivityId) {
            filters.marketActivityId = params.marketActivityId;
        }
        if (params.customerId) {
            filters.customerId = params.customerId;
        }

        const data =
            await this.marketActivityExternalRegistrationRepo.findAndCount({
                where: filters,
                skip: params.offset,
                take: params.limit,
                order: {
                    id: 'asc',
                },
            });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(
        params: CreateParams,
        entityManager: EntityManager,
    ): Promise<MarketActivityExternalRegistration> {
        const marketActivityExternalRegistrationToCreate =
            this.marketActivityExternalRegistrationRepo.create({
                ...params,
            });
        await this.validate(marketActivityExternalRegistrationToCreate);
        const marketActivityExternalRegistration = await entityManager.save(
            marketActivityExternalRegistrationToCreate,
        );
        return marketActivityExternalRegistration;
    }

    async validate(
        marketActivityExternalRegistration: MarketActivityExternalRegistration,
    ): Promise<void> {
        const schema = Joi.object<MarketActivityExternalRegistration>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(marketActivityExternalRegistration, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
