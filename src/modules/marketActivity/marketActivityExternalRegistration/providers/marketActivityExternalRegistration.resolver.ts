import { Inject, Service } from 'typedi';
import { <PERSON>rgs, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import {
    MarketActivityExternalRegistrationSearchArgs,
    MarketActivityExternalRegistrationSearchResult,
} from '../types/marketActivityExternalRegistration.gql.type';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityExternalRegistrationService } from './marketActivityExternalRegistration.service';
import { MarketActivityExternalRegistration } from '@/common/graphql/model/impl/public/marketActivityExternalRegistration.impl';
import { CustomerService } from '@/modules/customer/customer/providers/customer.service';
import { Customer } from '@/common/graphql/model/impl/salesRepWorkstation/customer.impl';

@Service()
@Resolver((of) => MarketActivityExternalRegistration)
export class MarketActivityExternalRegistrationResolver {
    @Inject()
    private marketActivityExternalRegistrationService: MarketActivityExternalRegistrationService;
    @Inject()
    private customerService: CustomerService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityExternalRegistrationSearchResult)
    async marketActivityExternalRegistrations(
        @Args() params: MarketActivityExternalRegistrationSearchArgs,
    ): Promise<MarketActivityExternalRegistrationSearchResult> {
        const result =
            await this.marketActivityExternalRegistrationService.search({
                ...params.filters,
                ...params,
            });
        return <MarketActivityExternalRegistrationSearchResult>result;
    }

    @FieldResolver((returns) => Customer, { nullable: true })
    async customer(
        @Root()
        marketActivityExternalRegistration: MarketActivityExternalRegistration,
    ): Promise<Customer | null> {
        const result = await this.customerService.findOne(
            marketActivityExternalRegistration.customerId,
        );
        return result;
    }
}
