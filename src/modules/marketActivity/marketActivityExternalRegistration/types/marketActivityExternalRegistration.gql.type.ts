import { MarketActivityExternalRegistration } from '@/common/graphql/model/impl/public/marketActivityExternalRegistration.impl';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    ObjectType,
} from 'type-graphql';

@InputType()
class MarketActivityExternalRegistrationeSearchInput {
    @Field(() => ID, { nullable: true, description: '市场活动' })
    marketActivityId?: number;

    @Field(() => ID, { nullable: true, description: '客户' })
    customerId?: number;
}
@InputType()
export class MarketActivityExternalRegistrationCreateInput {
    @Field((type) => ID, { nullable: true })
    customerId?: number;

    @Field((type) => Int, {
        nullable: true,
        description: '市场活动参会人參會客戶人數',
    })
    customerCount?: number;
}

@ArgsType()
export class MarketActivityExternalRegistrationSearchArgs extends CommonSearchArgs(
    MarketActivityExternalRegistrationeSearchInput,
) {}

@ObjectType()
export class MarketActivityExternalRegistrationSearchResult extends PaginatedSearchResult(
    MarketActivityExternalRegistration,
) {}
