import { MarketActivityInternalRegistration } from '@/common/graphql/model/impl/public/marketActivityInternalRegistration.impl';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import {
    ArgsType,
    Field,
    Float,
    ID,
    InputType,
    Int,
    ObjectType,
} from 'type-graphql';

@InputType()
class MarketActivityInternalRegistrationSearchInput {
    @Field(() => ID, { nullable: true, description: '市场活动' })
    marketActivityId?: number;

    @Field(() => ID, { nullable: true, description: '部门' })
    departmentId?: number;

    @Field(() => ID, { nullable: true, description: '成本中心' })
    costCenterId?: number;
}
@InputType()
export class MarketActivityInternalRegistrationCreateInput {
    @Field((type) => ID, { nullable: true })
    departmentId?: number;

    @Field((type) => ID, { nullable: true })
    costCenterId?: number;

    @Field((type) => Int, { nullable: true, description: '內部參會人內部人數' })
    internalCount?: number;

    @Field((type) => Float, {
        nullable: true,
        description: '內部參會人餐會費用',
    })
    estimatedCost?: string;
}

@ArgsType()
export class MarketActivityInternalRegistrationSearchArgs extends CommonSearchArgs(
    MarketActivityInternalRegistrationSearchInput,
) {}

@ObjectType()
export class MarketActivityInternalRegistrationSearchResult extends PaginatedSearchResult(
    MarketActivityInternalRegistration,
) {}
