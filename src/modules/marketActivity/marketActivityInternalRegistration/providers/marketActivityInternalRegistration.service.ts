import { Inject, Service } from 'typedi';
import { Entity<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
} from '../types/marketActivityInternalRegistration.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { MarketActivityInternalRegistration } from '@clinico/typeorm-persistence/models/public/marketActivityInternalRegistration.model';

@Service()
export class MarketActivityInternalRegistrationService extends CommonService<MarketActivityInternalRegistration> {
    private marketActivityInternalRegistrationRepo =
        ClinicoDataSource.getRepository(MarketActivityInternalRegistration);

    get commonDataNotFoundMessage(): string {
        return 'marketActivityInternalRegistration not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityInternalRegistration>> {
        const filters: FindOptionsWhere<MarketActivityInternalRegistration> =
            {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.marketActivityId) {
            filters.marketActivityId = params.marketActivityId;
        }
        if (params.departmentId) {
            filters.departmentId = params.departmentId;
        }
        if (params.costCenterId) {
            filters.costCenterId = params.costCenterId;
        }

        const data =
            await this.marketActivityInternalRegistrationRepo.findAndCount({
                where: filters,
                skip: params.offset,
                take: params.limit,
                order: {
                    id: 'asc',
                },
            });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(
        params: CreateParams,
        entityManager: EntityManager,
    ): Promise<MarketActivityInternalRegistration> {
        const marketActivityInternalRegistrationToCreate =
            this.marketActivityInternalRegistrationRepo.create({
                ...params,
            });
        await this.validate(marketActivityInternalRegistrationToCreate);
        const marketActivityInternalRegistration = await entityManager.save(
            marketActivityInternalRegistrationToCreate,
        );
        return marketActivityInternalRegistration;
    }

    async validate(
        marketActivityInternalRegistration: MarketActivityInternalRegistration,
    ): Promise<void> {
        const schema = Joi.object<MarketActivityInternalRegistration>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(marketActivityInternalRegistration, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
