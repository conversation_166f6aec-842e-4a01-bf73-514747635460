import { Inject, Service } from 'typedi';
import { Args, FieldResolver, Query, Resolver, Root } from 'type-graphql';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { CostCenter } from '@/common/graphql/model/impl/public/costCenter.impl';
import { CostCenterService } from '@/modules/costCenter/providers/costCenter.service';
import { DeptService } from '@/modules/dept/providers/dept.service';
import { Department } from '@/common/graphql/model/impl/public/department.impl';
import { MarketActivityInternalRegistrationService } from './marketActivityInternalRegistration.service';
import { MarketActivityInternalRegistration } from '@/common/graphql/model/impl/public/marketActivityInternalRegistration.impl';
import {
    MarketActivityInternalRegistrationSearchArgs,
    MarketActivityInternalRegistrationSearchResult,
} from '../types/marketActivityInternalRegistration.gql.type';

@Service()
@Resolver((of) => MarketActivityInternalRegistration)
export class MarketActivityInternalRegistrationResolver {
    @Inject()
    private marketActivityInternalRegistrationService: MarketActivityInternalRegistrationService;
    @Inject()
    private costCenterService: CostCenterService;
    @Inject()
    private departmentService: DeptService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityInternalRegistrationSearchResult)
    async marketActivityInternalAttendances(
        @Args() params: MarketActivityInternalRegistrationSearchArgs,
    ): Promise<MarketActivityInternalRegistrationSearchResult> {
        const result =
            await this.marketActivityInternalRegistrationService.search({
                ...params.filters,
                ...params,
            });
        return <MarketActivityInternalRegistrationSearchResult>result;
    }

    @FieldResolver((returns) => CostCenter, { nullable: true })
    async costCenter(
        @Root()
        marketActivityInternalRegistration: MarketActivityInternalRegistration,
    ): Promise<CostCenter | null> {
        const result = await this.costCenterService.findOne(
            marketActivityInternalRegistration.costCenterId,
        );
        return result;
    }

    @FieldResolver((returns) => Department, { nullable: true })
    async department(
        @Root()
        marketActivityInternalRegistration: MarketActivityInternalRegistration,
    ): Promise<Department | null> {
        const result = await this.departmentService.findOne(
            marketActivityInternalRegistration.departmentId,
        );
        return result;
    }
}
