import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { MarketActivityExpertLevel } from '@/common/graphql/model/impl/public/marketActivityExpertLevel.impl';

@InputType()
class MarketActivityExpertLevelSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => String, { nullable: true })
    name?: string;
}

@ArgsType()
export class MarketActivityExpertLevelSearchArgs extends CommonSearchArgs(
    MarketActivityExpertLevelSearchInput,
) {}

@ObjectType()
export class MarketActivityExpertLevelSearchResult extends PaginatedSearchResult(
    MarketActivityExpertLevel,
) {}
