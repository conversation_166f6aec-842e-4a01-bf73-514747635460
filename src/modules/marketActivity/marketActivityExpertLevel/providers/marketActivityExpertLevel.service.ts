import { Service } from 'typedi';
import { En<PERSON>ty<PERSON>anager, FindOptionsWhere, In } from 'typeorm';
import { CommonService } from '@/common/providers/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { SearchParams } from '../types/marketActivityExpertLevel.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { MarketActivityExpertLevel } from '@clinico/typeorm-persistence/models/public/marketActivityExpertLevel.model';

@Service()
export class MarketActivityExpertLevelService extends CommonService<MarketActivityExpertLevel> {
    private marketActivityExpertLevelRepo = ClinicoDataSource.getRepository(
        MarketActivityExpertLevel,
    );

    get commonDataNotFoundMessage(): string {
        return 'MarketActivityExpertLevelService not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<MarketActivityExpertLevel>> {
        const filters: FindOptionsWhere<MarketActivityExpertLevel> = {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.name) {
            filters.name = params.name;
        }
        filters.deleted = false;

        const data = await this.marketActivityExpertLevelRepo.findAndCount({
            where: {
                ...filters,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'desc',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }
}
