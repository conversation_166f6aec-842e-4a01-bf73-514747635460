import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    MarketActivityExpertLevelSearchArgs,
    MarketActivityExpertLevelSearchResult,
} from '../types/marketActivityExpertLevel.gql.type';
import { MarketActivityExpertLevelService } from './marketActivityExpertLevel.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MarketActivityExpertLevel } from '@/common/graphql/model/impl/public/marketActivityExpertLevel.impl';

@Service()
@Resolver((of) => MarketActivityExpertLevel)
export class MarketActivityExpertLevelResolver {
    @Inject()
    private marketActivityExpertLevelService: MarketActivityExpertLevelService;

    @UserAuthInterceptor()
    @Query(() => MarketActivityExpertLevelSearchResult)
    async marketActivityExpertLevels(
        @Args() params: MarketActivityExpertLevelSearchArgs,
    ): Promise<MarketActivityExpertLevelSearchResult> {
        const result = await this.marketActivityExpertLevelService.search({
            ...params.filters,
            ...params,
        });
        return <MarketActivityExpertLevelSearchResult>result;
    }
}
