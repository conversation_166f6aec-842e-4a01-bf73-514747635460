import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    BusinessProductSearchArgs,
    BusinessProductSearchResult,
    BusinessProductCreateInput,
    BusinessProductUpdateInput,
} from '../types/businessProduct.gql.type';
import { BusinessProductService } from './businessProduct.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { BusinessProduct } from '@/common/graphql/model/impl/salesRepWorkstation/businessProduct.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { RegionService } from '@/modules/region/providers/region.service';
import { Region } from '@/common/graphql/model/impl/public/region.impl';
import { SalesTeam } from '@/common/graphql/model/impl/salesRepWorkstation/salesTeam.impl';
import { SalesTeamService } from '@/modules/salesTeam/salesTeam/providers/salesTeam.service';
import { Material } from '@/common/graphql/model/impl/inventory/material.impl';
import { MaterialService } from '@/modules/material/material/providers/material.service';
import { ProductTeam } from '@/common/graphql/model/impl/salesRepWorkstation/productTeam.impl';
import { ProductTeamService } from '@/modules/productTeam/providers/productTeam.service';
import { ProductLineService } from '@/modules/productLine/providers/productLine.service';
import { ProductLine } from '@/common/graphql/model/impl/salesRepWorkstation/productLine.impl';

@Service()
@Resolver((of) => BusinessProduct)
export class BusinessProductResolver {
    @Inject()
    private businessProductService: BusinessProductService;
    @Inject()
    private regionService: RegionService;
    @Inject()
    private salesTeamService: SalesTeamService;
    @Inject()
    private productTeamService: ProductTeamService;
    @Inject('MaterialService')
    private materialService: MaterialService;
    @Inject()
    private productLineService: ProductLineService;

    @UserAuthInterceptor()
    @Query(() => BusinessProductSearchResult)
    async businessProducts(
        @Args() params: BusinessProductSearchArgs,
    ): Promise<BusinessProductSearchResult> {
        const result = await this.businessProductService.search({
            ...params.filters,
            ...params,
        });
        return <BusinessProductSearchResult>result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessProduct)
    async createBusinessProduct(
        @Arg('input') input: BusinessProductCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessProduct> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessProductService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessProduct)
    async updateBusinessProduct(
        @Arg('input') input: BusinessProductUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessProduct> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessProductService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => Boolean)
    async deleteBusinessProduct(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessProductService.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => Region, { nullable: true })
    async region(
        @Root() businessProduct: BusinessProduct,
    ): Promise<Region | null> {
        const result = await this.regionService.findOne(
            businessProduct.salesTeamGroup.regionId,
        );
        return result;
    }

    @FieldResolver((returns) => [SalesTeam])
    async salesTeams(
        @Root() businessProduct: BusinessProduct,
    ): Promise<SalesTeam[]> {
        const result = await this.salesTeamService.findByIds(
            businessProduct.salesTeamsBusinessProducts.map(
                (el) => el.salesTeamId,
            ),
        );
        return result;
    }

    @FieldResolver((returns) => [ProductTeam])
    async productTeams(
        @Root() businessProduct: BusinessProduct,
    ): Promise<ProductTeam[]> {
        const result = await this.productTeamService.findByIds(
            businessProduct.productTeamsBusinessProducts.map(
                (el) => el.productTeamId,
            ),
        );
        return result;
    }

    @FieldResolver((returns) => [Material])
    async materials(
        @Root() businessProduct: BusinessProduct,
    ): Promise<Material[]> {
        const result = await this.materialService.findByIds(
            businessProduct.businessProductsMaterials.map(
                (el) => el.materialId,
            ),
        );
        return result;
    }

    @FieldResolver((returns) => [ProductLine], {
        nullable: true,
        description: '产品线',
    })
    async productLine(
        @Root() businessProduct: BusinessProduct,
    ): Promise<ProductLine[]> {
        const result = await this.productLineService.findByIds(
            businessProduct.businessProductsToProductLines.map(
                (el) => el.productLineId,
            ),
        );
        return result;
    }
}
