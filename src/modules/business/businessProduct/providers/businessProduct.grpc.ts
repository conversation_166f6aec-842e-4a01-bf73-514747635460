import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import Container from 'typedi';
import {
    CreateResult,
    UpdateParams,
    UpdateResult,
} from '../types/businessProduct.grpc.type';
import { CreateParams } from '../types/businessProduct.type';
import { BusinessProductService } from './businessProduct.service';

const businessProductService = Container.get(BusinessProductService);
export const BusinessProduct = {
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;
        const businessProduct = await businessProductService.create(params);
        ctx.res = Helpers.Json.success(<CreateResult>{
            id: businessProduct.id,
        });
    },
    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const businessProduct = await businessProductService.update({
            ...params,
            name: params.name ?? <any>null,
            viewOrder: params.viewOrder ?? <any>null,
            typeId: params.typeId ?? <any>null,
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{
            id: businessProduct.id,
        });
    },
};
