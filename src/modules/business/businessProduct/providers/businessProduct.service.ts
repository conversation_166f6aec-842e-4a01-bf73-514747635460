import { Service } from 'typedi';
import {
    And,
    <PERSON><PERSON><PERSON><PERSON>anager,
    FindOptionsWhere,
    ILike,
    In,
    IsNull,
    Raw,
} from 'typeorm';
import Joi from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { BusinessProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessProduct.model';
import { BusinessProductsToProductLine } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessProductsToProductLine.model';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
    UpdateParams,
    DeleteParams,
} from '../types/businessProduct.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { SalesTeamGroup } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import _, { isNull } from 'lodash';
import { SalesTeamsBusinessProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamsBusinessProduct.model';
import { CompetitorsBusinessProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/competitorsBusinessProduct.model';
import { BusinessProductsMaterial } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessProductsMaterial.model';
import { ProductTeamsBusinessProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/productTeamsBusinessProduct.model';
import { Helpers } from '@clinico/clinico-node-framework';
import { TypeOrmHelper } from '@/common/helpers/typeOrm.helper';

@Service()
export class BusinessProductService extends CommonService<BusinessProduct> {
    private businessProductRepo =
        ClinicoDataSource.getRepository(BusinessProduct);

    get commonDataNotFoundMessage(): string {
        return 'BusinessProduct not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<BusinessProduct>> {
        const filters: FindOptionsWhere<BusinessProduct> = {};
        const salesTeamGroupFilters: FindOptionsWhere<SalesTeamGroup> = {};
        const salesTeamsBusinessProductFilters: FindOptionsWhere<SalesTeamsBusinessProduct> =
            {};
        const competitorsBusinessProductFilters: FindOptionsWhere<CompetitorsBusinessProduct> =
            {};
        const businessProductsMaterialFilters: FindOptionsWhere<BusinessProductsMaterial> =
            {};
        const productTeamsBusinessProductFilters: FindOptionsWhere<ProductTeamsBusinessProduct> =
            {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.regionIds) {
            salesTeamGroupFilters.regionId = In(params.regionIds);
        }
        if (!_.isEmpty(salesTeamGroupFilters)) {
            salesTeamGroupFilters.deleted = false;
        }
        if (params.salesTeamGroupIds) {
            filters.salesTeamGroupId = In(params.salesTeamGroupIds);
        }
        if (params.salesTeamId) {
            salesTeamsBusinessProductFilters.salesTeamId = params.salesTeamId;
        }
        if (params.salesTeamIds) {
            salesTeamsBusinessProductFilters.salesTeamId = In(
                params.salesTeamIds,
            );
        }
        if (params.typeId) {
            filters.typeId = params.typeId;
        }
        if (params.name) {
            filters.name = ILike(`%${params.name}%`);
        }
        if (params.brand) {
            filters.brand = ILike(`%${params.brand}%`);
        }
        if (_.isBoolean(params.isActive)) {
            filters.isActive = params.isActive;
        }
        if (params.productLineIds && params.productLineIds.length) {
            filters.businessProductsToProductLines = {
                productLineId: In(params.productLineIds),
                productLine: {
                    deleted: false,
                },
            };
        }

        filters.deleted = false;

        // competitorsBusinessProductFilters
        if (params.competitorId) {
            competitorsBusinessProductFilters.competitorId =
                params.competitorId;
        }

        // businessProductsMaterialFilters
        if (params.materialIds) {
            businessProductsMaterialFilters.materialId = In(params.materialIds);
        }

        // productTeamsBusinessProductFilters
        if (params.productTeamIds) {
            // 對應PM團隊 & 沒有任何PM團隊 的商機商品
            productTeamsBusinessProductFilters.productTeamId =
                TypeOrmHelper.InOrNull(params.productTeamIds);
        }

        const data = await this.businessProductRepo.findAndCount({
            where: {
                ...filters,
                salesTeamGroup: salesTeamGroupFilters,
                productTeamsBusinessProducts:
                    productTeamsBusinessProductFilters,
                salesTeamsBusinessProducts: !_.isEmpty(
                    salesTeamsBusinessProductFilters,
                )
                    ? salesTeamsBusinessProductFilters
                    : {},
                competitorsBusinessProducts: !_.isEmpty(
                    competitorsBusinessProductFilters,
                )
                    ? competitorsBusinessProductFilters
                    : {},
                businessProductsMaterials: !_.isEmpty(
                    businessProductsMaterialFilters,
                )
                    ? businessProductsMaterialFilters
                    : {},
            },
            relations: {
                salesTeamGroup: true,
                productTeamsBusinessProducts: true,
                salesTeamsBusinessProducts: true,
                businessProductsMaterials: true,
                type: true,
                businessProductsToProductLines: true,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                viewOrder: 'ASC',
                id: 'DESC',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(params: CreateParams): Promise<BusinessProduct> {
        const businessProductToCreate = this.businessProductRepo.create(params);
        const id = await ClinicoDataSource.transaction(async (manager) => {
            const businessProduct = await manager.save(businessProductToCreate);
            await this.mutate(manager, businessProduct, params);
            await this.validate(businessProduct);
            return businessProduct.id;
        });
        return await this.findOneOrError(id);
    }

    async update(params: UpdateParams): Promise<BusinessProduct> {
        const businessProduct = await this.businessProductRepo.findOneOrFail({
            where: {
                id: params.id,
            },
            relations: {
                salesTeamsBusinessProducts: true,
                productTeamsBusinessProducts: true,
                businessProductsMaterials: true,
            },
        });
        const businessProductToUpdate = await this.businessProductRepo.create({
            ...businessProduct,
            ...params,
            id: businessProduct.id,
        });

        const id = await ClinicoDataSource.transaction(async (manager) => {
            const businessProduct = await manager.save(businessProductToUpdate);
            await this.mutate(manager, businessProduct, params);
            await this.validate(businessProduct);
            return businessProduct.id;
        });
        return await this.findOneOrError(id);
    }

    async mutate(
        manager: EntityManager,
        businessProduct: BusinessProduct,
        params: CreateParams | UpdateParams,
    ): Promise<void> {
        // 業務團隊關聯
        if (Array.isArray(params.salesTeamIds)) {
            await manager.remove(
                businessProduct.salesTeamsBusinessProducts || [],
            );
            for (const salesTeamId of params.salesTeamIds) {
                await manager.save(SalesTeamsBusinessProduct, {
                    businessProductId: businessProduct.id,
                    salesTeamId: salesTeamId,
                });
            }
        }

        // PM團隊關聯
        if (Array.isArray(params.productTeamIds)) {
            await manager.remove(
                businessProduct.productTeamsBusinessProducts || [],
            );
            for (const productTeamId of params.productTeamIds) {
                await manager.save(ProductTeamsBusinessProduct, {
                    businessProductId: businessProduct.id,
                    productTeamId: productTeamId,
                });
            }
        }

        // 料號關聯
        if (Array.isArray(params.materialIds)) {
            await manager.remove(
                businessProduct.businessProductsMaterials || [],
            );
            for (const materialId of params.materialIds) {
                await manager.save(BusinessProductsMaterial, {
                    businessProductId: businessProduct.id,
                    materialId: materialId,
                });
            }
        }

        // productLine關聯
        if (Array.isArray(params.productLineIds)) {
            const businessProductsToProductLine = await manager.find(
                BusinessProductsToProductLine,
                {
                    where: {
                        businessProductId: businessProduct.id,
                    },
                },
            );
            await manager.remove(businessProductsToProductLine);
            for (const productLineId of params.productLineIds) {
                await manager.save(BusinessProductsToProductLine, {
                    businessProductId: businessProduct.id,
                    productLineId: productLineId,
                });
            }
        }
    }

    async delete(params: DeleteParams): Promise<void> {
        const businessProductToDelete = await this.findOneOrError(params.id);
        businessProductToDelete.deleted = true;
        await this.businessProductRepo.save(businessProductToDelete);
    }

    async validate(businessProduct: BusinessProduct): Promise<void> {
        const schema = Joi.object<BusinessProduct>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(businessProduct, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
