import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import Container from 'typedi';
import {
    CreateResult,
    UpdateParams,
    UpdateResult,
} from '../types/businessOpportunity.grpc.type';
import { CreateParams } from '../types/businessOpportunity.type';
import { BusinessOpportunityService } from './businessOpportunity.service';

const businessOpportunityService = Container.get(BusinessOpportunityService);
export const BusinessOpportunity = {
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;
        const businessOpportunity = await businessOpportunityService.create(
            params,
        );
        ctx.res = Helpers.Json.success(<CreateResult>{
            id: businessOpportunity.id,
        });
    },
    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const businessOpportunity = await businessOpportunityService.update({
            ...params,
            salesTeamGroupId: params.salesTeamGroupId ?? <any>null,
            name: params.name ?? <any>null,
            viewOrder: params.viewOrder ?? <any>null,
            code: params.code ?? <any>null,
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{
            id: businessOpportunity.id,
        });
    },
};
