import { Service } from 'typedi';
import { FindOptionsWhere, ILike, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { BusinessOpportunity } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessOpportunity.model';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
    UpdateParams,
    DeleteParams,
} from '../types/businessOpportunity.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { SalesTeamGroup } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import _ from 'lodash';

@Service()
export class BusinessOpportunityService extends CommonService<BusinessOpportunity> {
    private businessOpportunityRepo =
        ClinicoDataSource.getRepository(BusinessOpportunity);

    get commonDataNotFoundMessage(): string {
        return 'BusinessOpportunity not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<BusinessOpportunity>> {
        const filters: FindOptionsWhere<BusinessOpportunity> = {};
        const salesTeamGroupFilters: FindOptionsWhere<SalesTeamGroup> = {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.regionIds) {
            salesTeamGroupFilters.regionId = In(params.regionIds);
        }
        if (!_.isEmpty(salesTeamGroupFilters)) {
            salesTeamGroupFilters.deleted = false;
        }
        if (params.salesTeamGroupIds) {
            filters.salesTeamGroupId = In(params.salesTeamGroupIds);
        }
        if (params.code) {
            filters.code = ILike(`%${params.code}%`);
        }
        if (_.isBoolean(params.enabled)) {
            filters.enabled = params.enabled;
        }
        filters.deleted = false;

        const data = await this.businessOpportunityRepo.findAndCount({
            where: {
                ...filters,
                salesTeamGroup: salesTeamGroupFilters,
            },
            relations: {
                salesTeamGroup: true,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                viewOrder: 'ASC',
                id: 'DESC',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }
    async create(params: CreateParams): Promise<BusinessOpportunity> {
        const businessOpportunityToCreate =
            this.businessOpportunityRepo.create(params);
        await this.validate(businessOpportunityToCreate);
        const businessOpportunity = await this.businessOpportunityRepo.save(
            businessOpportunityToCreate,
        );
        return businessOpportunity;
    }

    async update(params: UpdateParams): Promise<BusinessOpportunity> {
        const businessOpportunityToUpdate = await this.findOneOrError(
            params.id,
        );
        const businessOpportunity = await this.businessOpportunityRepo.save({
            ...businessOpportunityToUpdate,
            ...params,
            id: businessOpportunityToUpdate.id,
        });
        return businessOpportunity;
    }

    async delete(params: DeleteParams): Promise<void> {
        const businessOpportunityToDelete = await this.findOneOrError(
            params.id,
        );
        businessOpportunityToDelete.deleted = true;
        await this.businessOpportunityRepo.save(businessOpportunityToDelete);
    }

    async validate(businessOpportunity: BusinessOpportunity): Promise<void> {
        const schema = Joi.object<BusinessOpportunity>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(businessOpportunity, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
