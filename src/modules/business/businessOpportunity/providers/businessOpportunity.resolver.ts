import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    BusinessOpportunitySearchArgs,
    BusinessOpportunitySearchResult,
    BusinessOpportunityCreateInput,
    BusinessOpportunityUpdateInput,
} from '../types/businessOpportunity.gql.type';
import { BusinessOpportunityService } from './businessOpportunity.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { BusinessOpportunity } from '@/common/graphql/model/impl/salesRepWorkstation/businessOpportunity.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { RegionService } from '@/modules/region/providers/region.service';
import { Region } from '@/common/graphql/model/impl/public/region.impl';

@Service()
@Resolver((of) => BusinessOpportunity)
export class BusinessOpportunityResolver {
    @Inject()
    private businessOpportunityService: BusinessOpportunityService;
    @Inject()
    private regionService: RegionService;

    @UserAuthInterceptor()
    @Query(() => BusinessOpportunitySearchResult)
    async businessOpportunities(
        @Args() params: BusinessOpportunitySearchArgs,
    ): Promise<BusinessOpportunitySearchResult> {
        const result = await this.businessOpportunityService.search({
            ...params.filters,
            ...params,
        });
        return <BusinessOpportunitySearchResult>result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessOpportunity)
    async createBusinessOpportunity(
        @Arg('input') input: BusinessOpportunityCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessOpportunity> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessOpportunityService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessOpportunity)
    async updateBusinessOpportunity(
        @Arg('input') input: BusinessOpportunityUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessOpportunity> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessOpportunityService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => Boolean)
    async deleteBusinessOpportunity(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessOpportunityService.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => Region, { nullable: true })
    async region(
        @Root() businessOpportunity: BusinessOpportunity,
    ): Promise<Region | null> {
        const result = await this.regionService.findOne(
            businessOpportunity.salesTeamGroup.regionId,
        );
        return result;
    }
}
