import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { BusinessOpportunity } from '@/common/graphql/model/impl/salesRepWorkstation/businessOpportunity.impl';

@InputType()
class BusinessOpportunitySearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => [ID], { nullable: true })
    regionIds: number[];

    @Field((type) => [ID], { nullable: true })
    salesTeamGroupIds?: number[];

    @Field({ nullable: true })
    code: string;

    @Field((type) => Boolean, { nullable: true })
    enabled?: boolean;
}

@ArgsType()
export class BusinessOpportunitySearchArgs extends CommonSearchArgs(
    BusinessOpportunitySearchInput,
) {}

@InputType()
export class BusinessOpportunityCreateInput {
    @Field((type) => ID)
    salesTeamGroupId: number;

    @Field()
    name: string;

    @Field((type) => Int, { nullable: true })
    viewOrder?: number;

    @Field({ nullable: true })
    code?: string;
}

@InputType()
export class BusinessOpportunityUpdateInput {
    @Field((type) => ID)
    id: number;

    @Field((type) => ID, { nullable: true })
    salesTeamGroupId?: number;

    @Field({ nullable: true })
    name?: string;

    @Field((type) => Int, { nullable: true })
    viewOrder?: number;

    @Field({ nullable: true })
    code?: string;
}

@ObjectType()
export class BusinessOpportunitySearchResult extends PaginatedSearchResult(
    BusinessOpportunity,
) {}
