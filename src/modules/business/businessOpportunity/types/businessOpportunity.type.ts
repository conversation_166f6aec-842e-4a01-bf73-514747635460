import {
    CommonSearchParams,
    CommonCreateParams,
    CommonUpdateParams,
    CommonDeletedParams,
} from '@/common/types/common.type';

export type SearchParams = CommonSearchParams & {
    code?: string;
    enabled?: boolean;
};

export type CreateParams = CommonCreateParams & {
    salesTeamGroupId: number;
    name: string;
    viewOrder?: number;
    code?: string;
};

export type UpdateParams = CommonUpdateParams & {
    salesTeamGroupId?: number;
    name?: string;
    viewOrder?: number;
    code?: string;
};

export type DeleteParams = CommonDeletedParams;
