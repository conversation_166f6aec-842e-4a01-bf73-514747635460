import Koa from 'koa';
import { Inject, Service } from 'typedi';
import { UserPayload } from '@/modules/auth/types/auth.type';
import {
    Args,
    Ctx,
    Query,
    Resolver,
    FieldResolver,
    Root,
    Mutation,
    Arg,
    ID,
    Float,
} from 'type-graphql';
import {
    BusinessPrimaryUserBatchUpdateInput,
    BusinessCreateInput,
    BusinessSearchArgs,
    BusinessSearchInput,
    BusinessSearchResult,
    BusinessUpdateInput,
    BusinessSalesTeamUnitBatchUpdateInput,
} from '../types/business.gql.type';
import { BusinessService } from './business.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { Business } from '@/common/graphql/model/impl/salesRepWorkstation/business.impl';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { UserService } from '@/modules/user/providers/user.service';
import { BusinessType } from '@/common/graphql/model/impl/salesRepWorkstation/businessType.impl';
import { BusinessTypeService } from '@/modules/business/businessType/providers/businessType.service';
import { BusinessOpportunityService } from '@/modules/business/businessOpportunity/providers/businessOpportunity.service';
import { BusinessOpportunity } from '@/common/graphql/model/impl/salesRepWorkstation/businessOpportunity.impl';
import { CustomerService } from '@/modules/customer/customer/providers/customer.service';
import { Customer } from '@/common/graphql/model/impl/salesRepWorkstation/customer.impl';
import { BusinessStatusService } from '@/modules/business/businessStatus/providers/businessStatus.service';
import { BusinessStatus } from '@/common/graphql/model/impl/salesRepWorkstation/businessStatus.impl';
import { SalesTeamService } from '@/modules/salesTeam/salesTeam/providers/salesTeam.service';
import { SalesTeam } from '@/common/graphql/model/impl/salesRepWorkstation/salesTeam.impl';
import { BusinessMemoService } from '@/modules/business/businessMemo/providers/businessMemo.service';
import { BusinessMemo } from '@/common/graphql/model/impl/salesRepWorkstation/businessMemo.impl';
import { ContactPersonService } from '@/modules/contactPerson/contactPerson/providers/contactPerson.service';
import { ContactPerson } from '@/common/graphql/model/impl/salesRepWorkstation/contactPerson.impl';
import { BusinessPropertyService } from '../../businessProperty/businessProperty/providers/businessProperty.service';
import { BusinessProperty } from '@/common/graphql/model/impl/salesRepWorkstation/businessProperty.impl';
import { VisitService } from '@/modules/visit/visit/providers/visit.service';
import { Visit } from '@/common/graphql/model/impl/salesRepWorkstation/visit.impl';
import { Competitor } from '@/common/graphql/model/impl/salesRepWorkstation/competitor.impl';
import { CompetitorService } from '@/modules/competitor/providers/competitor.service';
import { RegionService } from '@/modules/region/providers/region.service';
import { Region } from '@/common/graphql/model/impl/public/region.impl';
import { EyeServiceOrder } from '@/common/graphql/model/impl/maintenance/eyeServiceOrder.impl';
import { EyeServiceOrderService } from '@/modules/eyeServiceOrder/eyeServiceOrder/providers/eyeServiceOrder.service';
import { BusinessLosingReason } from '@/common/graphql/model/impl/salesRepWorkstation/businessLosingReason.impl';
import { BusinessLosingReasonService } from '../../businessLosingReason/providers/businessLosingReason.service';
import { BusinessSalesMethodService } from '../../businessSalesMethod/providers/businessSalesMethod.service';
import { BusinessSalesMethod } from '@/common/graphql/model/impl/salesRepWorkstation/businessSalesMethod.impl';
import { SalesTeamUnit } from '@/common/graphql/model/impl/salesRepWorkstation/salesTeamUnit.impl';
import { SalesTeamUnitService } from '@/modules/salesTeam/salesTeamUnit/providers/salesTeamUnit.service';
import { BusinessesUser } from '@/common/graphql/model/impl/salesRepWorkstation/businessesUser.impl';
import { BusinessesUserService } from '../../businessesUser/providers/businessesUser.service';

@Service()
@Resolver((of) => Business)
export class BusinessResolver {
    @Inject()
    private businessService: BusinessService;
    @Inject()
    private businessTypeService: BusinessTypeService;
    @Inject()
    private businessOpportunityService: BusinessOpportunityService;
    @Inject()
    private businessStatusService: BusinessStatusService;
    @Inject()
    private businessMemoService: BusinessMemoService;
    @Inject()
    private businessPropertyService: BusinessPropertyService;
    @Inject()
    private businessLosingReasonService: BusinessLosingReasonService;
    @Inject()
    private businessSalesMethodService: BusinessSalesMethodService;
    @Inject()
    private businessesUserService: BusinessesUserService;
    @Inject()
    private userService: UserService;
    @Inject()
    private customerService: CustomerService;
    @Inject('ContactPersonService')
    private contactPersonService: ContactPersonService;
    @Inject()
    private salesTeamService: SalesTeamService;
    @Inject()
    private salesTeamUnitService: SalesTeamUnitService;
    @Inject()
    private visitService: VisitService;
    @Inject()
    private competitorService: CompetitorService;
    @Inject()
    private regionService: RegionService;
    @Inject()
    private eyeServiceOrderService: EyeServiceOrderService;

    @UserAuthInterceptor('business.read')
    @Query(() => BusinessSearchResult)
    async businesses(
        @Args() params: BusinessSearchArgs,
    ): Promise<BusinessSearchResult> {
        const result = await this.businessService.search({
            ...params.filters,
            ...params.sorts,
            ...params,
        });
        return <BusinessSearchResult>result;
    }

    @UserAuthInterceptor('business.read')
    @Query(() => Float, { description: '欲出貨金額總數' })
    async businessBudgetAmountSum(
        @Arg('filters') params: BusinessSearchInput,
    ): Promise<number> {
        const result = await this.businessService.budgetAmountSum(params);
        return result;
    }

    @UserAuthInterceptor('business.create')
    @Mutation((returns) => Business)
    async createBusiness(
        @Arg('input') input: BusinessCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Business> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor('business.update')
    @Mutation((returns) => Business)
    async updateBusiness(
        @Arg('input') input: BusinessUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<Business> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor('business.batch_update')
    @Mutation((returns) => [Business], { description: '批次修改商機負責人' })
    async batchUpdateBusinessPrimaryUser(
        @Arg('input') input: BusinessPrimaryUserBatchUpdateInput,
    ): Promise<Business[]> {
        const result =
            await this.businessService.batchUpdateBusinessPrimaryUser(input);
        return result;
    }

    @UserAuthInterceptor('business.batch_update')
    @Mutation((returns) => [Business], {
        description: '批次修改商機負責業務位置',
    })
    async batchUpdateBusinessSalesTeamUnit(
        @Arg('input') input: BusinessSalesTeamUnitBatchUpdateInput,
    ): Promise<Business[]> {
        const result =
            await this.businessService.batchUpdateBusinessSalesTeamUnit(input);
        return result;
    }

    @UserAuthInterceptor('business.delete')
    @Mutation((returns) => Boolean)
    async deleteBusiness(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessService.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => Region, { nullable: true })
    async region(@Root() business: Business): Promise<Region | null> {
        const result = await this.regionService.findOne(
            business.salesTeamGroup.regionId,
        );
        return result;
    }

    @FieldResolver((returns) => SalesTeam, { nullable: true })
    async salesTeam(@Root() business: Business): Promise<SalesTeam | null> {
        const result = await this.salesTeamService.findOne(
            business.salesTeamId,
        );
        return result;
    }

    @FieldResolver((returns) => BusinessType, { nullable: true })
    async type(@Root() business: Business): Promise<BusinessType | null> {
        const result = await this.businessTypeService.findOne(business.typeId);
        return result;
    }

    @FieldResolver((returns) => BusinessOpportunity, {
        nullable: true,
        description: '贏單機會',
    })
    async winningOpportunity(
        @Root() business: Business,
    ): Promise<BusinessOpportunity | null> {
        const result = await this.businessOpportunityService.findOne(
            business.winningOpportunityId,
        );
        return result;
    }

    @FieldResolver((returns) => BusinessOpportunity, {
        nullable: true,
        description: '購買機會',
    })
    async buyingOpportunity(
        @Root() business: Business,
    ): Promise<BusinessOpportunity | null> {
        const result = await this.businessOpportunityService.findOne(
            business.buyingOpportunityId,
        );
        return result;
    }

    @FieldResolver((returns) => BusinessStatus, { nullable: true })
    async status(@Root() business: Business): Promise<BusinessStatus | null> {
        const result = await this.businessStatusService.findOne(
            business.statusId,
        );
        return result;
    }

    @FieldResolver((returns) => Customer, { nullable: true })
    async customer(@Root() business: Business): Promise<Customer | null> {
        const result = await this.customerService.findOne(business.customerId);
        return result;
    }

    @FieldResolver((returns) => SalesTeamUnit, { nullable: true })
    async salesTeamUnit(
        @Root() business: Business,
    ): Promise<SalesTeamUnit | null> {
        const result = await this.salesTeamUnitService.findOne(
            business.salesTeamUnitId,
        );
        return result;
    }

    @FieldResolver((returns) => User, {
        nullable: true,
        deprecationReason: '改用 salesTeamUnit',
    })
    async primaryUser(@Root() business: Business): Promise<User | null> {
        const result = await this.userService.findOne(business.primaryUserId);
        return result;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async createdUser(@Root() business: Business): Promise<User | null> {
        const result = await this.userService.findOne(business.createdUserId);
        return result;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async updatedUser(@Root() business: Business): Promise<User | null> {
        const result = await this.userService.findOne(business.updatedUserId);
        return result;
    }

    @FieldResolver((returns) => [BusinessesUser], {
        description: '跨部門負責人員',
    })
    async users(@Root() business: Business): Promise<BusinessesUser[]> {
        const result = await this.businessesUserService.search({
            businessId: business.id,
        });
        return result.rows;
    }

    @FieldResolver((returns) => [BusinessMemo])
    async memos(@Root() business: Business): Promise<BusinessMemo[]> {
        const result = await this.businessMemoService.search({
            businessId: business.id,
        });
        return result.rows;
    }

    @FieldResolver((returns) => [ContactPerson])
    async primaryContactPeople(
        @Root() business: Business,
    ): Promise<ContactPerson[]> {
        const result = await this.contactPersonService.findByIds(
            business.businessesPrimaryContactPeople.map(
                (el) => el.contactPersonId,
            ),
        );
        return result;
    }

    @FieldResolver((returns) => [BusinessProperty])
    async properties(@Root() business: Business): Promise<BusinessProperty[]> {
        const result = await this.businessPropertyService.findByIds(
            business.businessesProperties.map((el) => el.businessPropertyId),
        );
        return result;
    }

    @FieldResolver((returns) => [Visit])
    async visits(@Root() business: Business): Promise<Visit[]> {
        const result = await this.visitService.search({
            businessId: business.id,
        });
        return result.rows;
    }

    @FieldResolver((returns) => [Competitor])
    async competitors(@Root() business: Business): Promise<Competitor[]> {
        const result = await this.competitorService.findByIds(
            business.businessesCompetitors.map((el) => el.competitorId),
        );
        return result;
    }

    @FieldResolver((returns) => [EyeServiceOrder])
    async eyeServiceOrders(
        @Root() business: Business,
    ): Promise<EyeServiceOrder[]> {
        const result = await this.eyeServiceOrderService.search({
            businessId: business.id,
        });
        return result.rows;
    }

    @FieldResolver((returns) => [BusinessLosingReason])
    async losingReasons(
        @Root() business: Business,
    ): Promise<BusinessLosingReason[]> {
        const result = await this.businessLosingReasonService.findByIds(
            business.businessesLosingReasons.map(
                (el) => el.businessLosingReasonId,
            ),
        );
        return result;
    }

    @FieldResolver((returns) => BusinessSalesMethod, {
        nullable: true,
        description: '商機銷售方式',
    })
    async salesMethod(
        @Root() business: Business,
    ): Promise<BusinessSalesMethod | null> {
        const result = await this.businessSalesMethodService.findOne(
            business.salesMethodId,
        );
        return result;
    }

    @FieldResolver((returns) => Customer, {
        nullable: true,
        description: '經銷商',
    })
    async dealer(@Root() business: Business): Promise<Customer | null> {
        const result = await this.customerService.findOne(business.dealerId);
        return result;
    }
}
