import { Inject, Service } from 'typedi';
import {
    Between,
    EntityManager,
    FindOptionsWhere,
    ILike,
    In,
    LessThanOrEqual,
    MoreThanOrEqual,
    FindOptionsRelations,
    FindOptionsOrder,
    FindOptionsOrderValue,
} from 'typeorm';
import Joi from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    BusinessPrimaryUserBatchUpdateParams,
    BusinessSalesTeamUnitBatchUpdateParams,
    CreateParams,
    DeleteParams,
    EnumBusinessTransactionOpportunity,
    SearchParams,
    UpdateParams,
} from '../types/business.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { BusinessesPrimaryContactPerson } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesPrimaryContactPerson.model';
import { BusinessesCompetitor } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesCompetitor.model';
import { BusinessesProperty } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesProperty.model';
import { BusinessesToDealProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesToDealProduct.model';
import { BusinessesToBudgetProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';
import {
    EnumSalesTeamGroupCode,
    SalesTeamGroup,
} from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamGroup.model';
import _ from 'lodash';
import { CodeService } from '@/modules/code/providers/code.service';
import { EnumCodeType } from '@/modules/code/types/code.interface';
import { SalesTeamGroupService } from '@/modules/salesTeam/salesTeamGroup/providers/salesTeamGroup.service';
import { BusinessesUser } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesUser.model';
import { Customer } from '@clinico/typeorm-persistence/models/salesRepWorkstation/customer.model';
import {
    BusinessStatus,
    EnumBusinessStatusType,
} from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessStatus.model';
import { BusinessesLosingReason } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesLosingReason.model';
import { BusinessOpportunity } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessOpportunity.model';
import { IdHelper } from '@/common/helpers/id.helper';
import { SalesTeamUnit } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamUnit.model';
import { SalesTeamsUser } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamsUser.model';
import { SalesTeamService } from '@/modules/salesTeam/salesTeam/providers/salesTeam.service';
import { EnumBusinessSortName } from '../types/business.enum';
import { EnumSortDirection } from '@/common/enums/common.enum';
import Big from 'big.js';
import {
    EnumEyeServiceOrderStatus,
    EyeServiceOrder,
} from '@clinico/typeorm-persistence/models/maintenance/eyeServiceOrder.model';
import { EyeServiceOrderService } from '@/modules/eyeServiceOrder/eyeServiceOrder/providers/eyeServiceOrder.service';
import { EnumEyeFixedAssetsRentalStatus } from '@clinico/typeorm-persistence/models/public/eyeFixedAssetRentalRecord.model';
import { EnumEyeServiceOrderTypeCode } from '@clinico/typeorm-persistence/models/maintenance/eyeServiceOrderType.model';
import { EyeFixedAsset } from '@clinico/typeorm-persistence/models/public/eyeFixedAsset.model';
import { EyeQuotationOrder } from '@clinico/typeorm-persistence/models/public/eyeQuotationOrder.model';
import { BusinessBudgetProductValidator } from './validators/businessBudgetProduct.validator';

@Service()
export class BusinessService extends CommonService<Business> {
    @Inject()
    private salesTeamGroupService: SalesTeamGroupService;
    @Inject()
    private codeService: CodeService;
    @Inject()
    private salesTeamService: SalesTeamService;
    @Inject()
    private eyeServiceOrderService: EyeServiceOrderService;

    @Inject()
    private businessBudgetProductValidator: BusinessBudgetProductValidator;

    private businessRepo = ClinicoDataSource.getRepository(Business);

    get commonDataNotFoundMessage(): string {
        return 'Business not found';
    }

    async search(params: SearchParams): Promise<CommonSearchResult<Business>> {
        let ids: number[] | undefined = undefined;

        const filters: FindOptionsWhere<Business> = {};
        const salesTeamGroupFilters: FindOptionsWhere<SalesTeamGroup> = {};
        const businessStatusFilters: FindOptionsWhere<BusinessStatus> = {};
        const businessesToDealProductFilters: FindOptionsWhere<BusinessesToDealProduct> =
            {};
        const businessesToBudgetProductFilters: FindOptionsWhere<BusinessesToBudgetProduct> =
            {};
        const customerFilters: FindOptionsWhere<Customer> = {};
        const salesTeamUnitFilters: FindOptionsWhere<SalesTeamUnit> = {};
        const salesTeamsUserFilters: FindOptionsWhere<SalesTeamsUser> = {};
        const eyeFixedAssetFilters: FindOptionsWhere<EyeFixedAsset> = {};
        const eyeQuotationOrderFilters: FindOptionsWhere<EyeQuotationOrder> =
            {};
        const businessesPrimaryContactPeople: FindOptionsWhere<BusinessesPrimaryContactPerson> =
            {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            ids = IdHelper.intersectionIds(ids, params.ids);
        }
        if (params.regionIds) {
            salesTeamGroupFilters.regionId = In(params.regionIds);
        }
        if (!_.isEmpty(salesTeamGroupFilters)) {
            salesTeamGroupFilters.deleted = false;
        }
        if (params.salesTeamGroupIds) {
            filters.salesTeamGroupId = In(params.salesTeamGroupIds);
        }
        if (params.typeId) {
            filters.typeId = params.typeId;
        }
        if (params.code) {
            filters.code = ILike(`%${params.code}%`);
        }
        if (params.orderCode) {
            filters.orderCode = params.orderCode;
        }
        if (params.expectedClosedDate1) {
            filters.expectedClosedDate = MoreThanOrEqual(
                params.expectedClosedDate1,
            );
        }
        if (params.expectedClosedDate2) {
            filters.expectedClosedDate = LessThanOrEqual(
                params.expectedClosedDate2,
            );
        }
        if (params.expectedClosedDate1 && params.expectedClosedDate2) {
            filters.expectedClosedDate = Between(
                params.expectedClosedDate1,
                params.expectedClosedDate2,
            );
        }
        if (params.closedDate1) {
            filters.closedDate = MoreThanOrEqual(params.closedDate1);
        }
        if (params.closedDate2) {
            filters.closedDate = LessThanOrEqual(params.closedDate2);
        }
        if (params.closedDate1 && params.closedDate2) {
            filters.closedDate = Between(
                params.closedDate1,
                params.closedDate2,
            );
        }
        if (params.createdDate1) {
            filters.createdAt = MoreThanOrEqual(params.createdDate1);
        }
        if (params.createdDate2) {
            filters.createdAt = LessThanOrEqual(params.createdDate2);
        }
        if (params.createdDate1 && params.createdDate2) {
            filters.createdAt = Between(
                params.createdDate1,
                params.createdDate2,
            );
        }
        if (params.updatedDate1) {
            filters.updatedAt = MoreThanOrEqual(params.updatedDate1);
        }
        if (params.updatedDate2) {
            filters.updatedAt = LessThanOrEqual(params.updatedDate2);
        }
        if (params.updatedDate1 && params.updatedDate2) {
            filters.updatedAt = Between(
                params.updatedDate1,
                params.updatedDate2,
            );
        }
        if (params.winningOpportunityId) {
            filters.winningOpportunityId = params.winningOpportunityId;
        }
        if (params.winningOpportunityIds) {
            filters.winningOpportunityId = In(params.winningOpportunityIds);
        }
        if (params.transactionOpportunity) {
            const businessesIds = await this.transactionOpportunitySearch(
                params.transactionOpportunity,
            );
            ids = IdHelper.intersectionIds(ids, businessesIds);
        }
        if (params.statusId) {
            filters.statusId = params.statusId;
        }
        if (params.statusIds) {
            filters.statusId = In(params.statusIds);
        }
        if (params.statusType) {
            businessStatusFilters.type = params.statusType;
        }
        if (params.statusTypes) {
            businessStatusFilters.type = In(params.statusTypes);
        }
        if (params.primaryUserId) {
            salesTeamUnitFilters.userId = params.primaryUserId;
        }
        if (params.primaryUserIds) {
            salesTeamUnitFilters.userId = In(params.primaryUserIds);
        }
        if (params.salesTeamUnitId) {
            salesTeamUnitFilters.id = params.salesTeamUnitId;
        }
        if (params.salesTeamUnitIds) {
            salesTeamUnitFilters.id = In(params.salesTeamUnitIds);
        }
        if (params.dealerId) {
            filters.dealerId = params.dealerId;
        }
        if (params.salesMethodIds) {
            filters.salesMethodId = In(params.salesMethodIds);
        }
        filters.deleted = false;

        // businessesToBudgetProductFilters
        if (params.budgetProductIds) {
            businessesToBudgetProductFilters.budgetProductId = In(
                params.budgetProductIds,
            );
        }

        // businessesToDealProductFilters
        if (params.dealProductIds) {
            businessesToDealProductFilters.dealProductId = In(
                params.dealProductIds,
            );
        }

        // businessesPrimaryContactPeopleFilters
        if (params.primaryContactPersonId) {
            businessesPrimaryContactPeople.contactPersonId =
                params.primaryContactPersonId;
        }

        // customerFilters
        if (params.customerId) {
            filters.customerId = params.customerId;
        }
        if (params.customerIds) {
            filters.customerId = In(params.customerIds);
        }
        if (params.customerCode) {
            customerFilters.code = ILike(`%${params.customerCode}%`);
        }
        if (params.customerName) {
            customerFilters.name = ILike(`%${params.customerName}%`);
        }
        if (params.customerBusinessCode) {
            customerFilters.businessCode = ILike(
                `%${params.customerBusinessCode}%`,
            );
        }
        if (params.customerMedicalCode) {
            customerFilters.medicalCode = ILike(
                `%${params.customerMedicalCode}%`,
            );
        }
        if (params.customerCategoryId) {
            customerFilters.categoryId = params.customerCategoryId;
        }

        // salesTeamsUserFilters
        if (params.salesTeamId) {
            salesTeamsUserFilters.salesTeamId = params.salesTeamId;
        }

        if (params.salesTeamTopId) {
            const salesTeams = await this.salesTeamService.findChildren(
                params.salesTeamTopId,
            );
            salesTeamsUserFilters.salesTeamId = In(
                salesTeams.map((el) => el.id),
            );
        }

        // eyeFixedAssetFilters
        if (params.eyeFixedAssetId) {
            eyeFixedAssetFilters.id = params.eyeFixedAssetId;
        }

        // eyeQuotationOrderFilters
        if (params.eyeQuotationOrderId) {
            eyeQuotationOrderFilters.id = params.eyeQuotationOrderId;
        }

        // fuzzy search
        if (params.searchAll) {
            const businessesIds = await this.fuzzySearch(params.searchAll);
            ids = IdHelper.intersectionIds(ids, businessesIds);
        }

        // ids: 商機的ids是由多種交集組成，所以會在最後查詢
        if (_.isArray(ids)) {
            filters.id = In(IdHelper.uniqIds(ids ?? []));
        }

        // isForExport: 針對匯出的join
        const exportRelations: FindOptionsRelations<Business> = {};
        if (params.isForExport === true) {
            // 客戶
            exportRelations.customer = true;
            // 聯絡人
            exportRelations.businessesPrimaryContactPeople = {
                contactPerson: true,
            };
            exportRelations.primaryUser = {
                salesTeamsUsers: true,
            };
            exportRelations.salesTeamUnit = {
                salesTeam: true,
                user: true,
            };
        }

        // Sort: 排序
        const orderRelations: FindOptionsRelations<Business> = {};
        const orders: FindOptionsOrder<Business> = {};
        for (const sort of params.sorts || []) {
            // 排序方向
            let orderKey: FindOptionsOrderValue = 'ASC';
            if (sort.direction === EnumSortDirection.DESC) {
                orderKey = 'DESC';
            }
            switch (sort.name) {
                case EnumBusinessSortName.Code:
                    orders.code = orderKey;
                    break;
                case EnumBusinessSortName.WinningOpportunityName:
                    orderRelations.winningOpportunity = true;
                    orders.winningOpportunity = {
                        name: orderKey,
                    };
                    break;
                case EnumBusinessSortName.ExpectedClosedDate:
                    orders.expectedClosedDate = orderKey;
                    break;
                case EnumBusinessSortName.StatusBuyingOpportunity:
                    orderRelations.status = true;
                    orders.status = {
                        buyingOpportunity: orderKey,
                    };
                    break;
                case EnumBusinessSortName.CustomerName:
                    orderRelations.customer = true;
                    orders.customer = {
                        name: orderKey,
                    };
                    break;
                case EnumBusinessSortName.BudgetProductName:
                    orders.businessesToBudgetProducts = {
                        budgetProduct: {
                            name: orderKey,
                        },
                    };
                    break;
                case EnumBusinessSortName.PrimaryUserCode:
                    orderRelations.primaryUser = true;
                    orders.primaryUser = {
                        code: orderKey,
                    };
                    break;
                case EnumBusinessSortName.PrimaryUserName:
                    orderRelations.primaryUser = true;
                    orders.primaryUser = {
                        name: orderKey,
                    };
                    break;
                case EnumBusinessSortName.PrimaryUserSalesTeamName:
                    orderRelations.primaryUser = {
                        salesTeamsUsers: {
                            salesTeam: true,
                        },
                    };
                    orders.primaryUser = {
                        salesTeamsUsers: {
                            salesTeam: {
                                name: orderKey,
                            },
                        },
                    };
                    break;
            }
        }

        const data = await this.businessRepo.findAndCount({
            where: {
                ...filters,
                salesTeamGroup: salesTeamGroupFilters,
                customer: !_.isEmpty(customerFilters) ? customerFilters : {},
                status: !_.isEmpty(businessStatusFilters)
                    ? businessStatusFilters
                    : {},
                businessesToBudgetProducts: !_.isEmpty(
                    businessesToBudgetProductFilters,
                )
                    ? businessesToBudgetProductFilters
                    : {},
                businessesToDealProducts: !_.isEmpty(
                    businessesToDealProductFilters,
                )
                    ? businessesToDealProductFilters
                    : {},
                businessesPrimaryContactPeople: !_.isEmpty(
                    businessesPrimaryContactPeople,
                )
                    ? businessesPrimaryContactPeople
                    : {},
                salesTeamUnit: !_.isEmpty(salesTeamUnitFilters)
                    ? salesTeamUnitFilters
                    : {},
                primaryUser: !_.isEmpty(salesTeamsUserFilters)
                    ? {
                          salesTeamsUsers: salesTeamsUserFilters,
                      }
                    : {},
                eyeServiceOrders: !_.isEmpty(eyeFixedAssetFilters)
                    ? {
                          eyeFixedAssetRentalRecords: {
                              eyeFixedAssetRentalRecordItems: {
                                  eyeFixedAsset: eyeFixedAssetFilters,
                              },
                          },
                      }
                    : {},
                eyeQuotationOrderBusinesses: !_.isEmpty(
                    eyeQuotationOrderFilters,
                )
                    ? {
                          eyeQuotationOrder: eyeQuotationOrderFilters,
                      }
                    : {},
            },
            relations: {
                // 業務團隊
                salesTeamGroup: true,
                // 商機屬性
                businessesProperties: true,
                // 主要聯絡人
                businessesPrimaryContactPeople: true,
                // 競爭對手
                businessesCompetitors: true,
                // 商機與負責（協助）業務關聯
                businessesUsers: true,
                // 預算商品
                businessesToDealProducts: {
                    dealProduct: true,
                },
                // 成交商品
                businessesToBudgetProducts: {
                    budgetProduct: true,
                },
                // 丟單原因
                businessesLosingReasons: true,
                // for export
                ...exportRelations,
                // for order
                ...orderRelations,
            },
            skip: params.offset,
            take: params.limit,
            order: !_.isEmpty(orders)
                ? orders
                : {
                      expectedClosedDate: 'ASC',
                      id: 'DESC',
                  },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async fuzzySearch(key: string): Promise<number[]> {
        const businesses = await ClinicoDataSource.getRepository(Business)
            .createQueryBuilder('b')
            .leftJoinAndSelect(Customer, 'c', 'b.customer_id = c.id')
            .where(
                `   b.code ILIKE :key 
                 OR c.name ILIKE :key
                 `,
                { key: `%${key}%` },
            )
            .getMany();
        return businesses.map((el) => el.id);
    }

    // 欲出貨金額總數
    async budgetAmountSum(params: SearchParams): Promise<number> {
        const businesses = await this.search(params);
        if (businesses.count === 0) return 0;
        const sum = businesses.rows.reduce(
            (acc, cur) =>
                acc.add(!_.isNil(cur.budgetAmount) ? cur.budgetAmount : 0),
            Big(0),
        );
        return sum.toNumber();
    }

    /**
     * 成交機會
     *
     * 高 = 贏單機會 (99%) + 結案狀況 (99%-確認採購)
     * 中高 = 贏單機會 (75%) + 結案狀況 (99%-確認採購) 或 贏單機會 (99%) + 結案狀況 (75%-編列預算)
     * 中 = 贏單機會 (75%) + 結案狀況 (75%-編列預算)
     */
    async transactionOpportunitySearch(
        transactionOpportunity: EnumBusinessTransactionOpportunity,
    ): Promise<number[]> {
        const businessQuery = ClinicoDataSource.getRepository(Business)
            .createQueryBuilder('b')
            .leftJoinAndSelect(
                BusinessOpportunity,
                'o',
                'o.id = b.winning_opportunity_id',
            )
            .leftJoinAndSelect(BusinessStatus, 's', 's.id = b.status_id');

        switch (transactionOpportunity) {
            case EnumBusinessTransactionOpportunity.High:
                businessQuery.where(`
                        o.code = '99%'
                    and s.buying_opportunity = '99%'
                `);
                break;
            case EnumBusinessTransactionOpportunity.MediumHigh:
                businessQuery.where(`
                    (
                        o.code = '75%'
                    and s.buying_opportunity = '99%'
                    ) or (
                        o.code = '99%'
                    and s.buying_opportunity = '75%'
                    )
                `);
                break;
            case EnumBusinessTransactionOpportunity.Medium:
                businessQuery.where(`
                        o.code = '75%'
                    and s.buying_opportunity = '75%'
                `);
                break;
            case EnumBusinessTransactionOpportunity.Others:
                businessQuery.where(`
                        o.code not in ('75%', '99%')
                    or s.buying_opportunity not in ('75%', '99%')
                `);
                break;
        }

        const businesses = await businessQuery.getMany();
        return businesses.map((el) => el.id);
    }

    async create(params: CreateParams): Promise<Business> {
        const salesTeamGroup = await this.salesTeamGroupService.findOneOrError(
            params.salesTeamGroupId,
        );
        const businessToCreate = this.businessRepo.create(params);

        // transaction
        const business = await ClinicoDataSource.transaction(
            async (manager) => {
                // Generate Code
                businessToCreate.code = await this.codeService.nextDailyCode(
                    {
                        type: EnumCodeType.Business,
                        regionId: salesTeamGroup.regionId,
                    },
                    manager,
                );

                const business = await manager.save(businessToCreate);
                await this.mutate(manager, business, params);

                await this.validate(business);
                return business;
            },
        );
        return await this.findOneOrError(business.id);
    }

    async update(params: UpdateParams): Promise<Business> {
        const businessBeforeUpdate = await this.findOneOrError(params.id);
        const businessToUpdate = this.businessRepo.create({
            ...businessBeforeUpdate,
            ...params,
            id: businessBeforeUpdate.id,
        });
        const salesTeamGroup = await this.salesTeamGroupService.findOneOrError(
            businessToUpdate.salesTeamGroupId,
        );

        // transaction
        await ClinicoDataSource.transaction(async (manager) => {
            const business = await manager.save(businessToUpdate);
            await this.mutate(manager, business, params);
            await this.validate(business);

            // http://asking.clinico.com.tw/issues/104345
            if (salesTeamGroup.code === EnumSalesTeamGroupCode.CHN_EYE) {
                await this.businessBudgetProductValidator.validate(business);
            }

            /**
             * 當「中國業務團隊」的商機「成單機會」變更為 「0%」時，觸發以下事件:
             *   - 將關聯的服務單狀態改為「已完工」，但須滿足以下條件：
             *     1. 類別為[固資租借]
             *     2. 服務狀態為[新建立]
             *     3. 租借紀錄狀態為[待租借]
             */
            /** 中國業務團隊 */
            const cn_salesTeamGroupId = 4;
            /** 商機狀態: 0% - 丢单 */
            const close_businessStatus = await manager.findOneByOrFail(
                BusinessStatus,
                {
                    salesTeamGroupId: cn_salesTeamGroupId,
                    type: EnumBusinessStatusType.ClosedInLosing,
                },
            );
            if (
                // 業務團隊群組是 中國業務團隊
                business.salesTeamGroupId === cn_salesTeamGroupId &&
                // 有更新到 status
                params.statusId &&
                // 商機「成單機會」變更為「0%」
                IdHelper.idParse(businessBeforeUpdate.statusId) !==
                    IdHelper.idParse(close_businessStatus.id) &&
                IdHelper.idParse(params.statusId) ===
                    IdHelper.idParse(close_businessStatus.id)
            ) {
                const eyeServiceOrderResult =
                    await this.eyeServiceOrderService.search({
                        businessId: business.id,
                        // 類別為[固資租借]
                        eyeServiceOrderTypeCodes: [
                            EnumEyeServiceOrderTypeCode.FixedAssetRental,
                        ],
                        // 服務狀態為[新建立]
                        statuses: [EnumEyeServiceOrderStatus.New],
                        // 租借紀錄狀態為[待租借]
                        eyeFixedAssetsRentalStatuses: [
                            EnumEyeFixedAssetsRentalStatus.Waiting,
                        ],
                    });
                for (const eyeServiceOrder of eyeServiceOrderResult.rows) {
                    await manager.update(EyeServiceOrder, eyeServiceOrder.id, {
                        status: EnumEyeServiceOrderStatus.Done,
                    });
                }
            }
        });
        return await this.findOneOrError(businessToUpdate.id);
    }

    async batchUpdateBusinessPrimaryUser(
        params: BusinessPrimaryUserBatchUpdateParams,
    ): Promise<Business[]> {
        await ClinicoDataSource.transaction(async (manager) => {
            const businesses = await manager.findBy(Business, {
                id: In(params.businessIds),
            });

            for (const business of businesses) {
                business.primaryUserId = params.targetUserId;
                await manager.save(business);
            }
        });
        return (await this.search({ ids: params.businessIds })).rows;
    }

    async batchUpdateBusinessSalesTeamUnit(
        params: BusinessSalesTeamUnitBatchUpdateParams,
    ): Promise<Business[]> {
        await ClinicoDataSource.transaction(async (manager) => {
            const businesses = await manager.findBy(Business, {
                id: In(params.businessIds),
            });

            for (const business of businesses) {
                business.salesTeamUnitId = params.targetSalesTeamUnitId;
                await manager.save(business);
            }
        });
        return (await this.search({ ids: params.businessIds })).rows;
    }

    async mutate(
        manager: EntityManager,
        business: Business,
        params: CreateParams | UpdateParams,
    ): Promise<void> {
        //商機屬性
        if (Array.isArray(params.propertyIds)) {
            await manager.remove(business.businessesProperties || []);
            for (const id of params.propertyIds) {
                await manager.save(BusinessesProperty, {
                    businessId: business.id,
                    businessPropertyId: id,
                });
            }
        }
        // 競爭對手
        if (Array.isArray(params.competitorIds)) {
            await manager.remove(business.businessesCompetitors || []);
            for (const id of params.competitorIds) {
                await manager.save(BusinessesCompetitor, {
                    businessId: business.id,
                    competitorId: id,
                });
            }
        }
        // 主要聯絡人
        if (Array.isArray(params.primaryContactPersonIds)) {
            await manager.remove(business.businessesPrimaryContactPeople || []);
            for (const id of params.primaryContactPersonIds) {
                await manager.save(BusinessesPrimaryContactPerson, {
                    businessId: business.id,
                    contactPersonId: id,
                });
            }
        }
        // 商機成交商品
        if (Array.isArray(params.dealProducts)) {
            await manager.remove(business.businessesToDealProducts || []);
            for (const dealProduct of params.dealProducts) {
                await manager.save(BusinessesToDealProduct, {
                    businessId: business.id,
                    dealProductId: dealProduct.dealProductId,
                    qty: dealProduct.qty ?? 1,
                });
            }
        }
        // 商機預算商品
        if (Array.isArray(params.budgetProducts)) {
            await manager.remove(business.businessesToBudgetProducts || []);
            for (const budgetProduct of params.budgetProducts) {
                await manager.save(BusinessesToBudgetProduct, {
                    businessId: business.id,
                    budgetProductId: budgetProduct.budgetProductId,
                    qty: budgetProduct.qty ?? 1,
                });
            }
        }
        // 商機與負責（協助）業務
        if (Array.isArray(params.users)) {
            await manager.remove(business.businessesUsers || []);
            for (const user of params.users) {
                await manager.save(BusinessesUser, {
                    businessId: business.id,
                    userId: user.userId,
                    salesTeamId: user.salesTeamId,
                });
            }
        }
        // 丟單原因
        if (Array.isArray(params.losingReasonIds)) {
            await manager.remove(business.businessesLosingReasons || []);
            for (const id of params.losingReasonIds) {
                await manager.save(BusinessesLosingReason, {
                    businessId: business.id,
                    businessLosingReasonId: id,
                });
            }
        }
    }

    async delete(params: DeleteParams): Promise<void> {
        const businessToDelete = await this.findOneOrError(params.id);
        businessToDelete.updatedUserId = params.deletedUserId;
        businessToDelete.deleted = true;
        await this.businessRepo.save(businessToDelete);
    }

    async validate(business: Business): Promise<void> {
        const schema = Joi.object<Business>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(business, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
