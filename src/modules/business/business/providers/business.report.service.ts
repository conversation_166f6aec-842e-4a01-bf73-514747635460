import moment from 'moment';
import Big from 'big.js';
import { Inject, Service } from 'typedi';
import { ExcelService } from '@/common/providers/excel.service';
import { CompetitorService } from '@/modules/competitor/providers/competitor.service';
import { SalesTeamService } from '@/modules/salesTeam/salesTeam/providers/salesTeam.service';
import { UserService } from '@/modules/user/providers/user.service';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import { BusinessLosingReasonService } from '../../businessLosingReason/providers/businessLosingReason.service';
import { BusinessOpportunityService } from '../../businessOpportunity/providers/businessOpportunity.service';
import { BusinessProductService } from '../../businessProduct/providers/businessProduct.service';
import { BusinessPropertyService } from '../../businessProperty/businessProperty/providers/businessProperty.service';
import { BusinessStatusService } from '../../businessStatus/providers/businessStatus.service';
import { BusinessTypeService } from '../../businessType/providers/businessType.service';
import { SearchParams } from '../types/business.type';
import { BusinessService } from './business.service';
import configs from '@/configs';
import _ from 'lodash';
import { SalesTeam } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeam.model';
import { CustomerTypeService } from '@/modules/customer/customerType/providers/customerType.service';
import { CustomerCategoryService } from '@/modules/customer/customerCategory/providers/customerCategory.service';

@Service()
export class BusinessReportService {
    @Inject()
    private businessService: BusinessService;
    @Inject()
    private businessTypeService: BusinessTypeService;
    @Inject()
    private businessOpportunityService: BusinessOpportunityService;
    @Inject()
    private businessStatusService: BusinessStatusService;
    @Inject()
    private businessLosingReasonService: BusinessLosingReasonService;
    @Inject()
    private businessProductService: BusinessProductService;
    @Inject()
    private businessPropertyService: BusinessPropertyService;
    @Inject()
    private competitorService: CompetitorService;
    @Inject()
    private userService: UserService;
    @Inject()
    private salesTeamService: SalesTeamService;
    @Inject()
    private customerTypeService: CustomerTypeService;
    @Inject()
    private customerCategoryService: CustomerCategoryService;

    async excel(params: SearchParams): Promise<Buffer> {
        const data = await this.data(params);
        const generator = new Utils.Excel2Templater();
        await generator.load(
            configs.templateFolder + `/excels/business/business.xlsx`,
        );

        generator.sheet('sheet1');
        generator.fillRows({
            startRowIndex: 3,
            data: data,
            cellOptions: {
                font: {
                    size: 10,
                    name: 'Arial',
                },
                alignment: {
                    horizontal: 'left',
                    vertical: 'justify',
                },
            },
        });
        return await generator.saveToBuffer();
    }

    async data(params: SearchParams) {
        const business = await this.businessService.search({
            ...params,
            isForExport: true,
        });

        const types = await this.businessTypeService.findAll();
        const opportunities = await this.businessOpportunityService.findAll();
        const statuses = await this.businessStatusService.findAll();
        const losingReasons = await this.businessLosingReasonService.findAll();
        const products = await this.businessProductService.findAll();
        const props = await this.businessPropertyService.findAll();
        const competitors = await this.competitorService.findAll();
        const users = await this.userService.findAll();
        const saleTeams = await this.salesTeamService.findAll();
        const customerTypes = await this.customerTypeService.findAll();
        const customerCategories = await this.customerCategoryService.findAll();

        const getUser = (userId: number): string | null => {
            const user = users.find((el) => el.id === userId);
            return user ? `${user?.name}(${user?.code})` : null;
        };

        const findSalesTeamAndParents = (salesTeamId?: number) => {
            if (!salesTeamId) return [];
            const result: SalesTeam[] = [];
            const findTeam = (currentId) => {
                const team = saleTeams.find((t) => t.id === currentId);
                if (team) {
                    result.unshift(team); // 插入到陣列前面，以保持順序
                    if (team.parentId !== null) {
                        findTeam(team.parentId);
                    }
                }
            };
            findTeam(salesTeamId);
            return result;
        };

        const response = business.rows.map((b) => {
            const primaryUserSalesTeamId = saleTeams.find(
                (el) => el.id == b.salesTeamUnit?.salesTeam?.id,
            )?.id;
            const primaryUserSalesTeams = findSalesTeamAndParents(
                primaryUserSalesTeamId,
            );

            return {
                商機編號: b.code ?? '-',
                內容: b.content ?? '-',
                商機類別: types.find((el) => el.id === b.typeId)?.name ?? '-',
                贏單機會:
                    opportunities.find((el) => el.id === b.winningOpportunityId)
                        ?.name ?? '-',
                成單機會:
                    statuses
                        .filter((el) => el.id === b.statusId)
                        .map(
                            (el) => `${el.buyingOpportunity} - ${el.name}`,
                        )[0] || '-',
                丟單原因:
                    losingReasons.find(
                        (el) =>
                            el.id ===
                            b.businessesLosingReasons[0]
                                ?.businessLosingReasonId,
                    )?.name ?? '-',
                欲購買商品_廠牌_1:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[0]?.budgetProductId,
                    )?.brand ?? '-',
                欲購買商品_商品_1:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[0]?.budgetProductId,
                    )?.name ?? '-',
                欲購買商品_數量_1: b.businessesToBudgetProducts[0]?.qty ?? '-',
                欲購買商品_廠牌_2:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[1]?.budgetProductId,
                    )?.brand ?? '-',
                欲購買商品_商品_2:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[1]?.budgetProductId,
                    )?.name ?? '-',
                欲購買商品_數量_2: b.businessesToBudgetProducts[1]?.qty ?? '-',
                欲購買商品_廠牌_3:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[2]?.budgetProductId,
                    )?.brand ?? '-',
                欲購買商品_商品_3:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[2]?.budgetProductId,
                    )?.name ?? '-',
                欲購買商品_數量_3: b.businessesToBudgetProducts[2]?.qty ?? '-',
                欲購買商品_廠牌_4:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[3]?.budgetProductId,
                    )?.brand ?? '-',
                欲購買商品_商品_4:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[3]?.budgetProductId,
                    )?.name ?? '-',
                欲購買商品_數量_4: b.businessesToBudgetProducts[3]?.qty ?? '-',
                欲購買商品_廠牌_5:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[4]?.budgetProductId,
                    )?.brand ?? '-',
                欲購買商品_商品_5:
                    products.find(
                        (el) =>
                            el.id ===
                            b.businessesToBudgetProducts[4]?.budgetProductId,
                    )?.name ?? '-',
                欲購買商品_數量_5: b.businessesToBudgetProducts[4]?.qty ?? '-',
                預計結束日期: b.expectedClosedDate ?? '-',
                預計結束日期_年: b.expectedClosedDate
                    ? moment(b.expectedClosedDate).year()
                    : '-',
                預計結束日期_月: b.expectedClosedDate
                    ? moment(b.expectedClosedDate).format('M')
                    : '-',
                報價金額: b.budgetAmount
                    ? new Big(b.budgetAmount).toString()
                    : '-',
                預算範圍:
                    props
                        .filter((el) => {
                            return (
                                el.type.code === 'Customer_Budget_Range' &&
                                b.businessesProperties.find(
                                    (p) => p.businessPropertyId === el.id,
                                )
                            );
                        })
                        .map((el) => el.name)
                        .join(' ,') || '-',
                競爭對手:
                    b.businessesCompetitors
                        .map((el) => {
                            const competitor = competitors.find(
                                (c) => c.id === el.competitorId,
                            );
                            return `${competitor?.brand}/${competitor?.name}/${competitor?.model}(代理商: ${competitor?.agents})`;
                        })
                        .join(' ,') || '-',
                需求科室:
                    props
                        .filter((el) => {
                            return (
                                el.type.code === 'Section_In_Demand' &&
                                b.businessesProperties.find(
                                    (p) => p.businessPropertyId === el.id,
                                )
                            );
                        })
                        .map((el) => el.name)
                        .join(' ,') || '-',
                客戶編號: b.customer?.code ?? '-',
                客戶名稱: b.customer?.name ?? '-',
                客户类型:
                    customerTypes.find((el) => el.id === b.customer?.typeId)
                        ?.name ?? '-',
                客户分类:
                    customerCategories.find(
                        (el) => el.id === b.customer?.categoryId,
                    )?.name ?? '-',
                客戶地址: b.customer?.address ?? '-',
                聯絡人:
                    b.businessesPrimaryContactPeople
                        .map((el) => el.contactPerson.name)
                        .join(' ,') || '-',
                訂單編號: b.orderCode || '-',
                成交商品:
                    b.businessesToDealProducts
                        .map((el) => {
                            const product = products.find(
                                (p) => p.id === el.dealProductId,
                            );
                            return `[${product?.brand}]${product?.name}`;
                        })
                        .join(' ,') || '-',
                結束日期: b.closedDate ?? '-',
                成交價格: b.saleAmount ? new Big(b.saleAmount).toString() : '-',
                负责业务位置: b.salesTeamUnit?.name ?? '-',
                事業部: primaryUserSalesTeams[0]?.name ?? '-',
                大區: primaryUserSalesTeams[1]?.name ?? '-',
                辦事處: primaryUserSalesTeams[2]?.name ?? '-',
                跨部門協助人:
                    b.businessesUsers
                        .map((el) => {
                            const user = users.find((u) => u.id === el.userId);
                            const salesTeam = saleTeams.find(
                                (s) => s.id === el.salesTeamId,
                            );
                            return `${salesTeam?.name}-${user?.name}(${user?.code})`;
                        })
                        .join(' ,') || '-',
                建立人員: getUser(b.createdUserId) ?? '-',
                建立時間:
                    Helpers.Date.formatDateTime(moment(b.createdAt)) ?? '-',
                最後編輯人員: getUser(b.updatedUserId) ?? '-',
                最後編輯時間:
                    Helpers.Date.formatDateTime(moment(b.updatedAt)) ?? '-',
            };
        });

        return response;
    }
}
