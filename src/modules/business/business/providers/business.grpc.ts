import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import moment from 'moment';
import Container from 'typedi';
import {
    CreateParams,
    CreateResult,
    UpdateParams,
    UpdateResult,
} from '../types/business.grpc.type';
import { DeleteParams } from '../types/business.type';
import { BusinessService } from './business.service';

const businessService = Container.get(BusinessService);
export const Business = {
    //新增
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;
        const business = await businessService.create({
            ...params,
            expectedClosedDate:
                params.expectedClosedDate &&
                params.expectedClosedDate.seconds != 0
                    ? moment.unix(params.expectedClosedDate.seconds).toDate()
                    : undefined,
            closedDate:
                params.closedDate && params.closedDate.seconds != 0
                    ? moment.unix(params.closedDate.seconds).toDate()
                    : undefined,
        });
        ctx.res = Helpers.Json.success(<CreateResult>{ id: business.id });
    },
    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const business = await businessService.update({
            ...params,
            typeId: params.typeId ?? <any>null,
            title: params.title ?? <any>null,
            content: params.content ?? <any>null,
            orderCode: params.orderCode ?? <any>null,
            eyeQuotationOrderCode: params.eyeQuotationOrderCode ?? <any>null,
            budgetAmount: params.budgetAmount ?? <any>null,
            dealAmount: params.dealAmount ?? <any>null,
            winningOpportunityId: params.winningOpportunityId ?? <any>null,
            buyingOpportunityId: params.buyingOpportunityId ?? <any>null,
            losingReason: params.losingReason ?? <any>null,
            losingImprovementPlan: params.losingImprovementPlan ?? <any>null,
            statusId: params.statusId ?? <any>null,
            customerId: params.customerId ?? <any>null,
            salesTeamId: params.salesTeamId ?? <any>null,
            salesTeamUnitId: params.salesTeamUnitId ?? <any>null,
            primaryUserId: params.primaryUserId ?? <any>null,
            saleAmount: params.saleAmount ?? <any>null,
            // relations
            budgetProducts: params.budgetProducts ?? <any>[],
            dealProducts: params.dealProducts ?? <any>[],
            users: params.users ?? <any>[],
            propertyIds: params.propertyIds ?? <any>[],
            primaryContactPersonIds: params.primaryContactPersonIds ?? <any>[],
            competitorIds: params.competitorIds ?? <any>[],
            losingReasonIds: params.losingReasonIds ?? <any>[],
            salesMethodId: params.salesMethodId ?? <any>null,
            dealerId: params.dealerId ?? <any>null,
            customerMemo: params.customerMemo ?? <any>null,
            // date
            expectedClosedDate:
                params.expectedClosedDate &&
                params.expectedClosedDate.seconds != 0
                    ? moment.unix(params.expectedClosedDate.seconds).toDate()
                    : <any>null,
            closedDate:
                params.closedDate && params.closedDate.seconds != 0
                    ? moment.unix(params.closedDate?.seconds).toDate()
                    : <any>null,
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{ id: business.id });
    },
    delete: async (ctx: Context<any>) => {
        const params = <DeleteParams>ctx.req;
        await businessService.delete(params);
        ctx.res = Helpers.Json.success();
    },
};
