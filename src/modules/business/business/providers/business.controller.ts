import {
    Body,
    Ctx,
    JsonController,
    Post,
    UseBefore,
} from 'routing-controllers';
import Koa from 'koa';
import Container from 'typedi';
import { OpenAPI } from 'routing-controllers-openapi';
import { BusinessReportService } from './business.report.service';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import moment from 'moment';
import { lowerCase } from 'lodash';
import { SearchParams } from '../types/business.type';

const businessReportService = Container.get(BusinessReportService);

/**
 * Endpoint for user
 */
@JsonController('/business')
@UseBefore(UserAuthKoaInterceptor)
export class BusinessController {
    @OpenAPI({
        summary: '商機匯出',
    })
    @Post('/export')
    async export(
        @Ctx() ctx: Koa.Context,
        @Body() params: SearchParams,
    ): Promise<Koa.Context> {
        const fileName = `businesss_${moment().format('YYYY-MM-DD_HHmm')}.xlsx`;
        const body = await businessReportService.excel({
            ...params,
        });

        ctx.set('Content-filename', fileName);
        ctx.set('Content-disposition', 'attachment; filename=' + fileName);
        ctx.set('Content-type', 'application/vnd.ms-excel');
        ctx.body = body;
        return ctx;
    }
}
