import _ from 'lodash';
import { Service } from 'typedi';
import { SelectQueryBuilder } from 'typeorm';

import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';
import { BusinessProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessProduct.model';
import {
    BusinessStatus,
    EnumBusinessStatusType,
} from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessStatus.model';
import { BusinessesToBudgetProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';
import { Customer } from '@clinico/typeorm-persistence/models/salesRepWorkstation/customer.model';

@Service()
export class BusinessBudgetProductValidator {
    /**
     * http://asking.clinico.com.tw/issues/104345
     *
     * 相同的預算商品，不可以同時出現在同一位客戶的其他商機（未結案）。
     */
    async validate(business: Business): Promise<void> {
        const currProductIds = business.businessesToBudgetProducts.map(
            (val) => val.budgetProductId,
        );
        // 沒有預算商品時，則不驗證
        if (currProductIds.length === 0) {
            return;
        }

        const qb = this.buildQuery(business);

        type Row = { [key: string]: any };
        const rows = await qb.getRawMany<Row>();
        const grouped = _.groupBy(rows, 'id');
        for (const [id, vals] of Object.entries(grouped)) {
            const otherBusiness = _.get(vals, '[0]', {} as Row);
            const otherProductIds = vals.map((val) => val.budget_product_id);

            // 比對商品有無。若無商品時，則通過驗證
            if (otherProductIds.length === 0) {
                continue;
            }

            // 比對商品數量。若數量不同時，則通過驗證
            if (currProductIds.length !== otherProductIds.length) {
                continue;
            }

            // 比對商品順序是否相同。若順序相同時，則禁止新增及變更。否則通過驗證
            let isSame = true;
            for (let i = 0; i < currProductIds.length; i++) {
                if (currProductIds[i] !== otherProductIds[i]) {
                    isSame = false;
                    break;
                }
            }

            if (isSame) {
                const businessCode = otherBusiness.code || '-';
                const msg = `此客户已重复创建或修改「相同的欲购买商品」商机 (重复商机编号: ${businessCode})`;
                throw new BaseError(msg, 400);
            }
        }
    }

    private buildQuery(business: Business): SelectQueryBuilder<Business> {
        const repo = ClinicoDataSource.getRepository(Business);
        const qb = repo.createQueryBuilder('b');

        // Build select fields
        qb.select('b.id');
        qb.addSelect('b.code');
        qb.addSelect('b2bp.budget_product_id');

        // Build join relations
        qb.innerJoin(
            BusinessStatus,
            's',
            'b.status_id = s.id AND s.deleted = false',
        );
        qb.innerJoin(Customer, 'c', 'b.customer_id = c.id');
        qb.leftJoin(
            BusinessesToBudgetProduct,
            'b2bp',
            'b2bp.business_id = b.id',
        );
        qb.innerJoin(
            BusinessProduct,
            'bp',
            'b2bp.budget_product_id = bp.id AND bp.deleted = false',
        );

        // Build where condition
        // 未刪除的商機
        qb.where(`b.deleted = false`);
        // 同一個業務團隊組織的商機
        qb.andWhere(`b.sales_team_group_id = :salesTeamGroupId`, {
            salesTeamGroupId: business.salesTeamGroupId,
        });
        // 排除自身商機
        qb.andWhere(`b.id != :businessId`, {
            businessId: business.id,
        });
        // 同一位客戶
        qb.andWhere(`c.id = :customerId`, {
            customerId: business.customerId,
        });
        // 跟進中的商機
        qb.andWhere(`s.type = :statusType`, {
            statusType: EnumBusinessStatusType.InProgress,
        });

        return qb;
    }
}
