import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';
import { BusinessesToBudgetProduct } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';

import { BusinessBudgetProductValidator } from '../businessBudgetProduct.validator';

describe('BusinessBudgetProductValidator', () => {
    describe('validate', () => {
        let service: BusinessBudgetProductValidator;
        beforeEach(() => {
            service = new BusinessBudgetProductValidator();
            jest.spyOn(service as any, 'buildQuery').mockImplementation(() => {
                const mocked = [
                    {
                        id: 1,
                        code: 'CN-B24061800001',
                        customer_id: 1,
                        budget_product_id: 1,
                    },
                    {
                        id: 1,
                        code: 'CN-B24061800001',
                        customer_id: 1,
                        budget_product_id: 2,
                    },
                    {
                        id: 1,
                        code: 'CN-B24061800001',
                        customer_id: 1,
                        budget_product_id: 3,
                    },
                    {
                        id: 2,
                        code: 'CN-B24061800002',
                        customer_id: 1,
                        budget_product_id: 1,
                    },
                    {
                        id: 2,
                        code: 'CN-B24061800002',
                        customer_id: 1,
                        budget_product_id: 2,
                    },
                ];
                return { getRawMany: () => mocked } as any;
            });
        });

        let business: Business;
        beforeEach(() => {
            business = new Business();
            business.id = 3;
            business.code = 'CN-B24061800003';
            business.customerId = 1;
            business.businessesToBudgetProducts = [];
        });

        test('should be passed if budget product is empty', async () => {
            await expect(service.validate(business)).resolves.not.toThrow();
        });

        test('should throw error if budget products do match (part 2)', async () => {
            const budgetProduct1 = new BusinessesToBudgetProduct();
            budgetProduct1.budgetProductId = 1;
            business.businessesToBudgetProducts.push(budgetProduct1);

            const budgetProduct2 = new BusinessesToBudgetProduct();
            budgetProduct2.budgetProductId = 2;
            business.businessesToBudgetProducts.push(budgetProduct2);

            const budgetProduct3 = new BusinessesToBudgetProduct();
            budgetProduct3.budgetProductId = 3;
            business.businessesToBudgetProducts.push(budgetProduct3);

            const msg = `此客户已重复创建或修改「相同的欲购买商品」商机 (重复商机编号: CN-B24061800001)`;
            await expect(service.validate(business)).rejects.toThrowError(msg);
        });

        test('should throw error if budget products do match (part 2)', async () => {
            const budgetProduct1 = new BusinessesToBudgetProduct();
            budgetProduct1.budgetProductId = 1;
            business.businessesToBudgetProducts.push(budgetProduct1);

            const budgetProduct2 = new BusinessesToBudgetProduct();
            budgetProduct2.budgetProductId = 2;
            business.businessesToBudgetProducts.push(budgetProduct2);

            const msg = `此客户已重复创建或修改「相同的欲购买商品」商机 (重复商机编号: CN-B24061800002)`;
            await expect(service.validate(business)).rejects.toThrowError(msg);
        });

        test('should throw error if budget products do match but customer does not match', async () => {
            business.customerId = 2;

            const budgetProduct1 = new BusinessesToBudgetProduct();
            budgetProduct1.budgetProductId = 1;
            business.businessesToBudgetProducts.push(budgetProduct1);

            const budgetProduct2 = new BusinessesToBudgetProduct();
            budgetProduct2.budgetProductId = 2;
            business.businessesToBudgetProducts.push(budgetProduct2);

            const budgetProduct3 = new BusinessesToBudgetProduct();
            budgetProduct3.budgetProductId = 3;
            business.businessesToBudgetProducts.push(budgetProduct3);

            const msg = `此客户已重复创建或修改「相同的欲购买商品」商机 (重复商机编号: CN-B24061800001)`;
            await expect(service.validate(business)).rejects.toThrowError(msg);
        });

        test('should be passed if budget products do match but its sorted ones does not match', async () => {
            const budgetProduct1 = new BusinessesToBudgetProduct();
            budgetProduct1.budgetProductId = 3;
            business.businessesToBudgetProducts.push(budgetProduct1);

            const budgetProduct2 = new BusinessesToBudgetProduct();
            budgetProduct1.budgetProductId = 2;
            business.businessesToBudgetProducts.push(budgetProduct2);

            const budgetProduct3 = new BusinessesToBudgetProduct();
            budgetProduct1.budgetProductId = 1;
            business.businessesToBudgetProducts.push(budgetProduct3);

            await expect(service.validate(business)).resolves.not.toThrow();
        });
    });
});
