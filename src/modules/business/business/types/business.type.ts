import {
    CommonCreateParams,
    CommonDeletedParams,
    CommonSearchParams,
    CommonUpdateParams,
} from '@/common/types/common.type';
import { EnumBusinessStatusType } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessStatus.model';

export enum EnumBusinessTransactionOpportunity {
    /** 高 */
    High = 'High',
    /** 中高 */
    MediumHigh = 'MediumHigh',
    /** 中 */
    Medium = 'Medium',
    /** 其他 */
    Others = 'Others',
}

export type SearchParams = CommonSearchParams & {
    typeId?: number;
    // business.primaryUser.salesTeamId
    salesTeamId?: number;
    // 業務團隊頂層id
    salesTeamTopId?: number;
    code?: string;
    orderCode?: string;
    eyeQuotationOrderCode?: string;
    // 預計結案日期
    expectedClosedDate1?: Date;
    expectedClosedDate2?: Date;
    // 實際結案日期
    closedDate1?: Date;
    closedDate2?: Date;
    // 建立日期
    createdDate1?: Date;
    createdDate2?: Date;
    // 更新日期
    updatedDate1?: Date;
    updatedDate2?: Date;

    winningOpportunityId?: number;
    winningOpportunityIds?: number[];
    transactionOpportunity?: EnumBusinessTransactionOpportunity;
    statusId?: number;
    statusIds?: number[];
    statusType?: EnumBusinessStatusType;
    statusTypes?: EnumBusinessStatusType[];
    primaryUserId?: number;
    primaryUserIds?: number[];
    salesTeamUnitId?: number;
    salesTeamUnitIds?: number[];
    dealProductIds?: number[];
    budgetProductIds?: number[];
    salesMethodIds?: number[];

    // relations
    customerId?: number;
    customerIds?: number[];
    customerCode?: string;
    customerName?: string;
    customerMedicalCode?: string;
    customerBusinessCode?: string;
    customerCategoryId?: number;
    dealerId?: number;
    eyeFixedAssetId?: number;
    eyeQuotationOrderId?: number;

    // 模糊搜尋: 客戶名稱、商機編號
    searchAll?: string;

    primaryContactPersonId?: number;
};

export type CreateParams = CommonCreateParams & {
    salesTeamGroupId: number;
    typeId?: number;
    title?: string;
    content?: string;
    orderCode?: string;
    eyeQuotationOrderCode?: string;
    budgetAmount?: string;
    dealAmount?: string;
    expectedClosedDate?: Date;
    closedDate?: Date;
    winningOpportunityId?: number;
    buyingOpportunityId?: number;
    losingReason?: string;
    losingImprovementPlan?: string;
    statusId?: number;
    customerId?: number;
    salesTeamId?: number;
    salesTeamUnitId?: number;
    /** @deprecated 由 salesTeamUnitId 取代 */
    primaryUserId?: number;
    saleAmount?: string;
    salesMethodId?: number;
    dealerId?: number;
    customerMemo?: string;

    // relations
    // 商機屬性
    propertyIds?: number[];
    // 主要聯絡人
    primaryContactPersonIds?: number[];
    // 競爭對手
    competitorIds?: number[];
    // 成交商品
    dealProducts?: {
        dealProductId: number;
        qty?: number;
    }[];
    // 預算商品
    budgetProducts?: {
        budgetProductId: number;
        qty?: number;
    }[];
    // 負責（協助）業務
    users?: {
        userId: number;
        salesTeamId?: number;
    }[];
    // 丟單原因
    losingReasonIds?: number[];
};

export type UpdateParams = CommonUpdateParams & {
    typeId?: number;
    title?: string;
    content?: string;
    orderCode?: string;
    eyeQuotationOrderCode?: string;
    budgetAmount?: string;
    dealAmount?: string;
    expectedClosedDate?: Date;
    closedDate?: Date;
    winningOpportunityId?: number;
    buyingOpportunityId?: number;
    losingReason?: string;
    losingImprovementPlan?: string;
    statusId?: number;
    customerId?: number;
    salesTeamId?: number;
    salesTeamUnitId?: number;
    /** @deprecated 由 salesTeamUnitId 取代 */
    primaryUserId?: number;
    saleAmount?: string;
    salesMethodId?: number;
    dealerId?: number;
    customerMemo?: string;

    // relations
    // 商機屬性
    propertyIds?: number[];
    // 主要聯絡人
    primaryContactPersonIds?: number[];
    // 競爭對手
    competitorIds?: number[];
    // 成交商品
    dealProducts?: {
        dealProductId: number;
        qty?: number;
    }[];
    // 預算商品
    budgetProducts?: {
        budgetProductId: number;
        qty?: number;
    }[];
    // 負責（協助）業務
    users?: {
        userId: number;
        salesTeamId?: number;
    }[];
    // 丟單原因
    losingReasonIds?: number[];
};

// 批次調整商機負責人
export type BusinessPrimaryUserBatchUpdateParams = {
    // 商機（複數）
    businessIds: number[];
    // 目標負責人
    targetUserId: number;
};

// 批次調整商機負責業務位置
export type BusinessSalesTeamUnitBatchUpdateParams = {
    // 商機（複數）
    businessIds: number[];
    // 目標負責業務位置
    targetSalesTeamUnitId: number;
};

export type DeleteParams = CommonDeletedParams;
