import {
    ArgsType,
    Field,
    ID,
    InputType,
    Int,
    ObjectType,
    registerEnumType,
} from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { Business } from '@/common/graphql/model/impl/salesRepWorkstation/business.impl';
import { DateResolver } from 'graphql-scalars';
import { BusinessBudgetProductInput } from '../../businessesToBudgetProduct/types/businessesToBudgetProduct.gql.type';
import { BusinessDealProductInput } from '../../businessesToDealProduct/types/businessesToDealProduct.gql.type';
import { EnumBusinessStatusType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessStatus.model';
import { EnumBusinessTransactionOpportunity } from './business.type';
import { EnumBusinessSortName } from './business.enum';
import { EnumSortDirection } from '@/common/enums/common.enum';

registerEnumType(EnumBusinessTransactionOpportunity, {
    name: 'EnumBusinessTransactionOpportunity',
    description: '成交機會',
    valuesConfig: {
        High: { description: '高' },
        MediumHigh: { description: '中高' },
        Medium: { description: '中' },
        Others: { description: '其他' },
    },
});

@InputType()
export class BusinessSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => [ID], { nullable: true })
    regionIds: number[];

    @Field((type) => [ID], { nullable: true, description: '業務團隊組織' })
    salesTeamGroupIds?: number[];

    @Field((type) => ID, { nullable: true, description: '負責業務團隊' })
    salesTeamId?: number;

    @Field((type) => ID, { nullable: true, description: '負責業務頂層團隊' })
    salesTeamTopId?: number;

    @Field((type) => ID, { nullable: true, description: '類型' })
    typeId?: number;

    @Field({ nullable: true, description: '編號' })
    code?: string;

    @Field({ nullable: true, description: '訂單編號（鼎新、道一雲、用友）' })
    orderCode?: string;

    @Field({ nullable: true, description: '眼科報價單編號' })
    eyeQuotationOrderCode?: string;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '預計結束日期（起）',
    })
    expectedClosedDate1?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '預計結束日期（迄）',
    })
    expectedClosedDate2?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '實際結束日期（起）',
    })
    closedDate1?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '實際結束日期（迄）',
    })
    closedDate2?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '建立日期（起）',
    })
    createdDate1?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '建立日期（迄）',
    })
    createdDate2?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '更新日期（起）',
    })
    updatedDate1?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '更新日期（迄）',
    })
    updatedDate2?: Date;

    @Field((type) => ID, { nullable: true, description: '贏單機會' })
    winningOpportunityId?: number;

    @Field((type) => [ID], { nullable: true, description: '贏單機會（複數）' })
    winningOpportunityIds?: number[];

    @Field(() => EnumBusinessTransactionOpportunity, {
        nullable: true,
        description: '成交機會',
    })
    transactionOpportunity?: EnumBusinessTransactionOpportunity;

    @Field((type) => ID, { nullable: true, description: '狀態' })
    statusId?: number;

    @Field((type) => [ID], { nullable: true, description: '狀態（複數）' })
    statusIds?: number[];

    @Field((type) => EnumBusinessStatusType, {
        nullable: true,
        description: '狀態類型',
    })
    statusType?: EnumBusinessStatusType;

    @Field((type) => [EnumBusinessStatusType], {
        nullable: true,
        description: '狀態類型（複數）',
    })
    statusTypes?: EnumBusinessStatusType[];

    @Field((type) => ID, { nullable: true, description: '主要負責業務' })
    primaryUserId?: number;

    @Field((type) => [ID], {
        nullable: true,
        description: '主要負責業務（複數）',
    })
    primaryUserIds?: number[];

    @Field((type) => ID, { nullable: true, description: '主要負責業務位置' })
    salesTeamUnitId?: number;

    @Field((type) => [ID], {
        nullable: true,
        description: '主要負責業務位置（複數）',
    })
    salesTeamUnitIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '成交商品' })
    dealProductIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '欲購買商品' })
    budgetProductIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '銷售方式' })
    salesMethodIds?: number[];

    @Field((type) => ID, { nullable: true, description: '客戶' })
    customerId?: number;

    @Field((type) => [ID], { nullable: true, description: '客戶（複數）' })
    customerIds?: number[];

    @Field({ nullable: true, description: '客戶編號' })
    customerCode?: string;

    @Field({ nullable: true, description: '客戶名稱' })
    customerName?: string;

    @Field({ nullable: true, description: '客戶醫事編號' })
    customerMedicalCode?: string;

    @Field({ nullable: true, description: '客戶統一編號' })
    customerBusinessCode?: string;

    @Field((type) => ID, { nullable: true, description: '經銷商' })
    dealerId?: number;

    @Field((type) => ID, { nullable: true, description: '固定資產' })
    eyeFixedAssetId?: number;

    @Field((type) => ID, { nullable: true, description: '眼科報價單' })
    eyeQuotationOrderId?: number;

    @Field({
        nullable: true,
        description: '模糊搜尋: 客戶名稱、商機編號',
    })
    searchAll?: string;

    @Field((type) => ID, { nullable: true, description: '主要聯絡人' })
    primaryContactPersonId?: number;

    @Field(() => ID, { nullable: true, description: '客戶分類' })
    customerCategoryId?: number;
}

@InputType('BusinessSortInput')
export class SortInput {
    @Field(() => EnumBusinessSortName)
    name: EnumBusinessSortName;

    @Field(() => EnumSortDirection)
    direction: EnumSortDirection;
}

@ArgsType()
export class BusinessSearchArgs extends CommonSearchArgs(BusinessSearchInput) {
    @Field(() => [SortInput], { nullable: true })
    sorts?: SortInput[];
}

@InputType()
export class BusinessCreateInput {
    @Field((type) => ID)
    salesTeamGroupId: number;

    @Field((type) => ID, { nullable: true, description: '類型' })
    typeId?: number;

    @Field({ nullable: true, description: '標題' })
    title?: string;

    @Field({ nullable: true, description: '內容' })
    content?: string;

    @Field({ nullable: true, description: '訂單編號（鼎新、道一雲、用友）' })
    orderCode?: string;

    @Field({ nullable: true, description: '眼科報價單編號' })
    eyeQuotationOrderCode?: string;

    @Field({ nullable: true, description: '客戶預算金額' })
    budgetAmount?: string;

    @Field({ nullable: true, description: '雙方成交金額' })
    dealAmount?: string;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '預計結束日期',
    })
    expectedClosedDate?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '實際結束日期',
    })
    closedDate?: Date;

    @Field((type) => ID, { nullable: true, description: '贏單機會' })
    winningOpportunityId?: number;

    @Field({ nullable: true, description: '丟單原因' })
    losingReason?: string;

    @Field({ nullable: true, description: '丟單改善計畫' })
    losingImprovementPlan?: string;

    @Field((type) => ID, { nullable: true, description: '狀態' })
    statusId?: number;

    @Field((type) => ID, { nullable: true, description: '客戶' })
    customerId?: number;

    @Field((type) => ID, { nullable: true, description: '負責業務團隊' })
    salesTeamId?: number;

    @Field((type) => ID, { nullable: true, description: '業務團隊位置' })
    salesTeamUnitId?: number;

    @Field((type) => ID, {
        nullable: true,
        description: '主要負責業務',
        deprecationReason: '改用 salesTeamUnitId',
    })
    primaryUserId?: number;

    @Field({ nullable: true, description: '業務銷售金額' })
    saleAmount?: string;

    // relations
    @Field((type) => [ID], { nullable: true, description: '商機屬性' })
    propertyIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '主要聯絡人' })
    primaryContactPersonIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '競爭對手' })
    competitorIds?: number[];

    @Field((type) => [BusinessDealProductInput], {
        nullable: true,
        description: '商機與成交商品關聯',
    })
    dealProducts?: BusinessDealProductInput[];

    @Field((type) => [BusinessBudgetProductInput], {
        nullable: true,
        description: '商機與預算商品關聯',
    })
    budgetProducts?: BusinessBudgetProductInput[];

    @Field((type) => [BusinessUserInput], {
        nullable: true,
        description: '商機與負責（協助）業務關聯',
    })
    users?: BusinessUserInput[];

    @Field((type) => [ID], { nullable: true, description: '丟單原因' })
    losingReasonIds?: number[];

    @Field((type) => ID, { nullable: true, description: '銷售方式' })
    salesMethodId?: number;

    @Field((type) => ID, { nullable: true, description: '經銷商' })
    dealerId?: number;

    @Field({ nullable: true, description: '客户备注' })
    customerMemo?: string;
}

@InputType()
export class BusinessUpdateInput {
    @Field((type) => ID)
    id: number;

    @Field((type) => ID, { nullable: true, description: '類型' })
    typeId?: number;

    @Field({ nullable: true, description: '標題' })
    title?: string;

    @Field({ nullable: true, description: '內容' })
    content?: string;

    @Field({ nullable: true, description: '訂單編號（鼎新、道一雲、用友）' })
    orderCode?: string;

    @Field({ nullable: true, description: '眼科報價單編號' })
    eyeQuotationOrderCode?: string;

    @Field({ nullable: true, description: '客戶預算金額' })
    budgetAmount?: string;

    @Field({ nullable: true, description: '雙方成交金額' })
    dealAmount?: string;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '預計結束日期',
    })
    expectedClosedDate?: Date;

    @Field((type) => DateResolver, {
        nullable: true,
        description: '實際結束日期',
    })
    closedDate?: Date;

    @Field((type) => ID, { nullable: true, description: '贏單機會' })
    winningOpportunityId?: number;

    @Field({ nullable: true, description: '丟單原因' })
    losingReason?: string;

    @Field({ nullable: true, description: '丟單改善計畫' })
    losingImprovementPlan?: string;

    @Field((type) => ID, { nullable: true, description: '狀態' })
    statusId?: number;

    @Field((type) => ID, { nullable: true, description: '客戶' })
    customerId?: number;

    @Field((type) => ID, { nullable: true, description: '負責業務團隊' })
    salesTeamId?: number;

    @Field((type) => ID, { nullable: true, description: '業務團隊位置' })
    salesTeamUnitId?: number;

    @Field((type) => ID, {
        nullable: true,
        description: '主要負責業務',
        deprecationReason: '改用 salesTeamUnitId',
    })
    primaryUserId?: number;

    @Field({ nullable: true, description: '業務銷售金額' })
    saleAmount?: string;

    // relations
    @Field((type) => [ID], { nullable: true, description: '商機屬性' })
    propertyIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '主要聯絡人' })
    primaryContactPersonIds?: number[];

    @Field((type) => [ID], { nullable: true, description: '競爭對手' })
    competitorIds?: number[];

    @Field((type) => [BusinessDealProductInput], {
        nullable: true,
        description: '商機與成交商品關聯',
    })
    dealProducts?: BusinessDealProductInput[];

    @Field((type) => [BusinessBudgetProductInput], {
        nullable: true,
        description: '商機與預算商品關聯',
    })
    budgetProducts?: BusinessBudgetProductInput[];

    @Field((type) => [BusinessUserInput], {
        nullable: true,
        description: '商機與負責（協助）業務關聯',
    })
    users?: BusinessUserInput[];

    @Field((type) => [ID], { nullable: true, description: '丟單原因' })
    losingReasonIds?: number[];

    @Field((type) => ID, { nullable: true, description: '銷售方式' })
    salesMethodId?: number;

    @Field((type) => ID, { nullable: true, description: '經銷商' })
    dealerId?: number;

    @Field({ nullable: true, description: '客户备注' })
    customerMemo?: string;
}

@InputType('BusinessPrimaryUserBatchUpdateInput', {
    description: '批次調整商機負責人',
})
export class BusinessPrimaryUserBatchUpdateInput {
    @Field((type) => [ID], { description: '商機（複數）' })
    businessIds: number[];

    @Field((type) => ID, { description: '目標負責人' })
    targetUserId: number;
}

@InputType('BusinessSalesTeamUnitBatchUpdateInput', {
    description: '批次調整商機負責業務位置',
})
export class BusinessSalesTeamUnitBatchUpdateInput {
    @Field((type) => [ID], { description: '商機（複數）' })
    businessIds: number[];

    @Field((type) => ID, { description: '目標負責業務位置' })
    targetSalesTeamUnitId: number;
}

@InputType()
class BusinessUserInput {
    @Field((type) => ID)
    userId: number;

    @Field((type) => ID, { nullable: true })
    salesTeamId?: number;
}

@ObjectType()
export class BusinessSearchResult extends PaginatedSearchResult(Business) {}
