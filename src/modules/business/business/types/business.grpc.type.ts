import { Timestamp } from '@/common/gens/google/protobuf/timestamp';
import { CommonUpdateParams } from '@/common/types/common.type';

export type CreateParams = {
    regionId?: number;
    createdUserId: number;
    salesTeamGroupId: number;
    typeId?: number;
    title?: string;
    content?: string;
    orderCode?: string;
    eyeQuotationOrderCode?: string;
    budgetAmount?: string;
    dealAmount?: string;
    expectedClosedDate?: Timestamp;
    closedDate?: Timestamp;
    winningOpportunityId?: number;
    buyingOpportunityId?: number;
    losingReason?: string;
    losingImprovementPlan?: string;
    statusId?: number;
    customerId?: number;
    salesTeamId?: number;
    salesTeamUnitId?: number;
    primaryUserId?: number;
    saleAmount?: string;
    salesMethodId?: number;
    dealerId?: number;
    customerMemo?: string;

    // relations
    propertyIds?: number[];
    primaryContactPersonIds?: number[];
    competitorIds?: number[];
    dealProducts?: {
        dealProductId: number;
        qty?: number;
    }[];
    budgetProducts?: {
        budgetProductId: number;
        qty?: number;
    }[];
    users?: {
        userId: number;
        salesTeamId?: number;
    }[];
    losingReasonIds?: number[];
};

export type UpdateParams = CommonUpdateParams & {
    typeId: number | null;
    title: string | null;
    content: string | null;
    orderCode: string | null;
    eyeQuotationOrderCode?: string | null;
    budgetAmount: string | null;
    dealAmount: string | null;
    expectedClosedDate: Timestamp | null;
    closedDate: Timestamp | null;
    winningOpportunityId: number | null;
    buyingOpportunityId: number | null;
    losingReason: string | null;
    losingImprovementPlan: string | null;
    statusId: number | null;
    customerId: number | null;
    salesTeamId: number | null;
    salesTeamUnitId: number | null;
    primaryUserId: number | null;
    saleAmount: string | null;
    salesMethodId: string | null;
    dealerId: string | null;
    customerMemo: string | null;

    // relations
    propertyIds?: number[];
    primaryContactPersonIds?: number[];
    competitorIds?: number[];
    dealProducts?: {
        dealProductId: number;
        qty?: number;
    }[];
    budgetProducts?: {
        budgetProductId: number;
        qty?: number;
    }[];
    users?: {
        userId: number;
        salesTeamId?: number;
    }[];
    losingReasonIds?: number[];
};

export type CreateResult = {
    id: number;
};

export type UpdateResult = {
    id: number;
};
