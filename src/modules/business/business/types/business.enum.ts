import { registerEnumType } from 'type-graphql';

export enum EnumBusinessSortName {
    Code,
    WinningOpportunityName,
    ExpectedClosedDate,
    StatusBuyingOpportunity,
    CustomerName,
    BudgetProductName,
    PrimaryUserCode,
    PrimaryUserName,
    PrimaryUserSalesTeamName,
}
registerEnumType(EnumBusinessSortName, {
    name: 'EnumBusinessSortName',
    description: '商機排序名稱',
    valuesConfig: {
        Code: { description: '編號' },
        WinningOpportunityName: { description: '贏單機會_名稱' },
        ExpectedClosedDate: { description: '預計結束日期' },
        StatusBuyingOpportunity: { description: '成單機會_購買機會' },
        CustomerName: { description: '客戶_名稱' },
        BudgetProductName: { description: '預算商品_名稱' },
        PrimaryUserCode: { description: '主要負責業務_編號' },
        PrimaryUserName: { description: '主要負責業務_姓名' },
        PrimaryUserSalesTeamName: { description: '主要負責業務_業務團隊_名稱' },
    },
});
