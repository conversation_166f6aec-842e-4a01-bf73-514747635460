import { Service } from 'typedi';
import { EntityManager, FindOptionsWhere, ILike, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import _ from 'lodash';
import { CommonService } from '@/common/providers/common.service';
import { BusinessLosingReason } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessLosingReason.model';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
    UpdateParams,
    DeleteParams,
} from '../types/businessLosingReason.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { SalesTeamGroup } from '@clinico/typeorm-persistence/models/salesRepWorkstation/salesTeamGroup.model';

@Service()
export class BusinessLosingReasonService extends CommonService<BusinessLosingReason> {
    private businessLosingReasonRepo =
        ClinicoDataSource.getRepository(BusinessLosingReason);

    get commonDataNotFoundMessage(): string {
        return 'BusinessLosingReason not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<BusinessLosingReason>> {
        const filters: FindOptionsWhere<BusinessLosingReason> = {};
        const salesTeamGroupFilters: FindOptionsWhere<SalesTeamGroup> = {};

        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.regionIds) {
            salesTeamGroupFilters.regionId = In(params.regionIds);
        }
        if (!_.isEmpty(salesTeamGroupFilters)) {
            salesTeamGroupFilters.deleted = false;
        }
        if (params.salesTeamGroupIds) {
            salesTeamGroupFilters.id = In(params.salesTeamGroupIds);
        }
        if (params.name) {
            filters.name = ILike(`%${params.name}%`);
        }
        filters.deleted = false;

        const data = await this.businessLosingReasonRepo.findAndCount({
            where: {
                ...filters,
                salesTeamGroup: salesTeamGroupFilters,
            },
            relations: {
                salesTeamGroup: true,
            },
            skip: params.offset,
            take: params.limit,
            order: {
                viewOrder: 'ASC',
                id: 'DESC',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }

    async create(params: CreateParams): Promise<BusinessLosingReason> {
        const businessLosingReasonToCreate =
            this.businessLosingReasonRepo.create(params);
        const id = await ClinicoDataSource.transaction(async (manager) => {
            const businessLosingReason = await manager.save(
                businessLosingReasonToCreate,
            );
            await this.mutate(manager, businessLosingReason, params);
            await this.validate(businessLosingReason);
            return businessLosingReason.id;
        });
        return await this.findOneOrError(id);
    }

    async update(params: UpdateParams): Promise<BusinessLosingReason> {
        const businessLosingReason = await this.findOneOrError(params.id);
        const businessLosingReasonToUpdate =
            await this.businessLosingReasonRepo.create({
                ...businessLosingReason,
                ...params,
                id: businessLosingReason.id,
            });

        const id = await ClinicoDataSource.transaction(async (manager) => {
            const businessLosingReason = await manager.save(
                businessLosingReasonToUpdate,
            );
            await this.mutate(manager, businessLosingReason, params);
            await this.validate(businessLosingReason);
            return businessLosingReason.id;
        });
        return await this.findOneOrError(id);
    }

    async mutate(
        manager: EntityManager,
        businessLosingReason: BusinessLosingReason,
        params: CreateParams | UpdateParams,
    ): Promise<void> {
        // TODO: mutate
    }

    async delete(params: DeleteParams): Promise<void> {
        const businessLosingReasonToDelete = await this.findOneOrError(
            params.id,
        );
        businessLosingReasonToDelete.deleted = true;
        await this.businessLosingReasonRepo.save(businessLosingReasonToDelete);
    }

    async validate(businessLosingReason: BusinessLosingReason): Promise<void> {
        const schema = Joi.object<BusinessLosingReason>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(businessLosingReason, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
