import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    BusinessLosingReasonSearchArgs,
    BusinessLosingReasonSearchResult,
    BusinessLosingReasonCreateInput,
    BusinessLosingReasonUpdateInput,
} from '../types/businessLosingReason.gql.type';
import { BusinessLosingReasonService } from './businessLosingReason.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { BusinessLosingReason } from '@/common/graphql/model/impl/salesRepWorkstation/businessLosingReason.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { RegionService } from '@/modules/region/providers/region.service';
import { Region } from '@/common/graphql/model/impl/public/region.impl';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { UserService } from '@/modules/user/providers/user.service';

@Service()
@Resolver((of) => BusinessLosingReason)
export class BusinessLosingReasonResolver {
    @Inject()
    private businessLosingReasonService: BusinessLosingReasonService;
    @Inject()
    private userService: UserService;
    @Inject()
    private regionService: RegionService;

    @UserAuthInterceptor()
    @Query(() => BusinessLosingReasonSearchResult)
    async businessLosingReasons(
        @Args() params: BusinessLosingReasonSearchArgs,
    ): Promise<BusinessLosingReasonSearchResult> {
        const result = await this.businessLosingReasonService.search({
            ...params.filters,
            ...params,
        });
        return <BusinessLosingReasonSearchResult>result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessLosingReason)
    async createBusinessLosingReason(
        @Arg('input') input: BusinessLosingReasonCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessLosingReason> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessLosingReasonService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessLosingReason)
    async updateBusinessLosingReason(
        @Arg('input') input: BusinessLosingReasonUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessLosingReason> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessLosingReasonService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => Boolean)
    async deleteBusinessLosingReason(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessLosingReasonService.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => Region, { nullable: true })
    async region(
        @Root() businessLosingReason: BusinessLosingReason,
    ): Promise<Region | null> {
        const result = await this.regionService.findOne(
            businessLosingReason.salesTeamGroup.regionId,
        );
        return result;
    }
}
