import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { BusinessLosingReason } from '@/common/graphql/model/impl/salesRepWorkstation/businessLosingReason.impl';

@InputType()
class BusinessLosingReasonSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => [ID], { nullable: true })
    regionIds?: number[];

    @Field((type) => [ID], { nullable: true })
    salesTeamGroupIds?: number[];

    @Field({ nullable: true, description: '名稱' })
    name?: string;
}

@ArgsType()
export class BusinessLosingReasonSearchArgs extends CommonSearchArgs(
    BusinessLosingReasonSearchInput,
) {}

@InputType()
export class BusinessLosingReasonCreateInput {
    @Field({ description: '名稱' })
    name: string;

    @Field((type) => Int, { nullable: true, description: '檢視順序' })
    viewOrder?: number;

    @Field((type) => ID, { description: '業務團隊組織' })
    salesTeamGroupId: number;
}

@InputType()
export class BusinessLosingReasonUpdateInput {
    @Field((type) => ID)
    id: number;

    @Field({ nullable: true, description: '名稱' })
    name?: string;

    @Field((type) => Int, { nullable: true, description: '檢視順序' })
    viewOrder?: number;
}

@ObjectType()
export class BusinessLosingReasonSearchResult extends PaginatedSearchResult(
    BusinessLosingReason,
) {}
