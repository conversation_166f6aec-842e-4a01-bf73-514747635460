import { Service } from 'typedi';
import { FindOptionsWhere, In } from 'typeorm';
import Jo<PERSON> from 'joi';
import { CommonService } from '@/common/providers/common.service';
import { BusinessMemo } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessMemo.model';
import { CommonSearchResult } from '@/common/types/common.type';
import {
    SearchParams,
    CreateParams,
    UpdateParams,
    DeleteParams,
} from '../types/businessMemo.type';
import { PageInfoHelper } from '@/common/helpers/pageInfo.helper';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';

@Service()
export class BusinessMemoService extends CommonService<BusinessMemo> {
    private businessMemoRepo = ClinicoDataSource.getRepository(BusinessMemo);

    get commonDataNotFoundMessage(): string {
        return 'BusinessMemo not found';
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<BusinessMemo>> {
        const filters: FindOptionsWhere<BusinessMemo> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids) {
            filters.id = In(params.ids);
        }
        if (params.businessId) {
            filters.businessId = params.businessId;
        }
        filters.deleted = false;

        const data = await this.businessMemoRepo.findAndCount({
            where: filters,
            skip: params.offset,
            take: params.limit,
            order: {
                id: 'DESC',
            },
        });

        const result = this.toFindAndCountResult(data);
        return {
            pageInfo: PageInfoHelper.generate({
                searchParams: params,
                totalCount: result.count,
            }),
            ...result,
        };
    }
    async create(params: CreateParams): Promise<BusinessMemo> {
        const businessMemoToCreate = this.businessMemoRepo.create(params);
        await this.validate(businessMemoToCreate);
        const businessMemo = await this.businessMemoRepo.save(
            businessMemoToCreate,
        );
        return businessMemo;
    }

    async update(params: UpdateParams): Promise<BusinessMemo> {
        const businessMemoToUpdate = await this.findOneOrError(params.id);
        const businessMemo = await this.businessMemoRepo.save({
            ...businessMemoToUpdate,
            ...params,
            id: businessMemoToUpdate.id,
        });
        return businessMemo;
    }

    async delete(params: DeleteParams): Promise<void> {
        const businessMemoToDelete = await this.findOneOrError(params.id);
        businessMemoToDelete.updatedUserId = params.deletedUserId;
        businessMemoToDelete.deleted = true;
        await this.businessMemoRepo.save(businessMemoToDelete);
    }

    async validate(businessMemo: BusinessMemo): Promise<void> {
        const schema = Joi.object<BusinessMemo>().keys({
            // TODO
        });

        try {
            await schema.validateAsync(businessMemo, {
                allowUnknown: true,
            });
        } catch (error) {
            throw new BaseError(error.message, 400);
        }
    }
}
