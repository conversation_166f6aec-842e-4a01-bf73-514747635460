import Koa from 'koa';
import { Inject, Service } from 'typedi';
import {
    Arg,
    Args,
    Ctx,
    Query,
    Mutation,
    Resolver,
    ID,
    FieldResolver,
    Root,
} from 'type-graphql';
import {
    BusinessMemoSearchArgs,
    BusinessMemoSearchResult,
    BusinessMemoCreateInput,
    BusinessMemoUpdateInput,
} from '../types/businessMemo.gql.type';
import { BusinessMemoService } from './businessMemo.service';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { BusinessMemo } from '@/common/graphql/model/impl/salesRepWorkstation/businessMemo.impl';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { User } from '@/common/graphql/model/impl/public/user.impl';
import { UserService } from '@/modules/user/providers/user.service';
import { BusinessService } from '../../business/providers/business.service';
import { Business } from '@/common/graphql/model/impl/salesRepWorkstation/business.impl';

@Service()
@Resolver((of) => BusinessMemo)
export class BusinessMemoResolver {
    @Inject()
    private businessMemoService: BusinessMemoService;
    @Inject()
    private userService: UserService;
    @Inject()
    private businessService: BusinessService;

    @UserAuthInterceptor()
    @Query(() => BusinessMemoSearchResult)
    async businessMemos(
        @Args() params: BusinessMemoSearchArgs,
    ): Promise<BusinessMemoSearchResult> {
        const result = await this.businessMemoService.search({
            ...params.filters,
            ...params,
        });
        return <BusinessMemoSearchResult>result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessMemo)
    async createBusinessMemo(
        @Arg('input') input: BusinessMemoCreateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessMemo> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessMemoService.create({
            ...input,
            createdUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => BusinessMemo)
    async updateBusinessMemo(
        @Arg('input') input: BusinessMemoUpdateInput,
        @Ctx() ctx: Koa.Context,
    ): Promise<BusinessMemo> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessMemoService.update({
            ...input,
            updatedUserId: payload.id,
        });

        return result;
    }

    @UserAuthInterceptor()
    @Mutation((returns) => Boolean)
    async deleteBusinessMemo(
        @Arg('id', (type) => ID) id: number,
        @Ctx() ctx: Koa.Context,
    ): Promise<boolean> {
        const payload = ctx.req['user'] as UserPayload;
        const result = await this.businessMemoService.delete({
            id: id,
            deletedUserId: payload.id,
        });
        return true;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async createdUser(
        @Root() businessMemo: BusinessMemo,
    ): Promise<User | null> {
        const result = await this.userService.findOne(
            businessMemo.createdUserId,
        );
        return result;
    }

    @FieldResolver((returns) => User, { nullable: true })
    async updatedUser(
        @Root() businessMemo: BusinessMemo,
    ): Promise<User | null> {
        const result = await this.userService.findOne(
            businessMemo.updatedUserId,
        );
        return result;
    }

    @FieldResolver((returns) => Business, { nullable: true })
    async business(
        @Root() businessMemo: BusinessMemo,
    ): Promise<Business | null> {
        const result = await this.businessService.findOne(
            businessMemo.businessId,
        );
        return result;
    }
}
