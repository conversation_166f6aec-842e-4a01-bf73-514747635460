import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import Container from 'typedi';
import {
    CreateResult,
    UpdateParams,
    UpdateResult,
} from '../types/businessMemo.grpc.type';
import { CreateParams, DeleteParams } from '../types/businessMemo.type';
import { BusinessMemoService } from './businessMemo.service';

const businessMemoService = Container.get(BusinessMemoService);
export const BusinessMemo = {
    create: async (ctx: Context<any>) => {
        const params = <CreateParams>ctx.req;
        const businessMemo = await businessMemoService.create(params);
        ctx.res = Helpers.Json.success(<CreateResult>{ id: businessMemo.id });
    },
    update: async (ctx: Context<any>) => {
        const params = <UpdateParams>ctx.req;
        const businessMemo = await businessMemoService.update({
            ...params,
            title: params.title ?? <any>null,
            content: params.content ?? <any>null,
            businessId: params.businessId ?? <any>null,
        });
        ctx.res = Helpers.Json.success(<UpdateResult>{ id: businessMemo.id });
    },
    delete: async (ctx: Context<any>) => {
        const params = <DeleteParams>ctx.req;
        await businessMemoService.delete(params);
        ctx.res = Helpers.Json.success();
    },
};
