import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import {
    CommonSearchArgs,
    PaginatedSearchResult,
} from '@/common/types/common.gql.type';
import { BusinessMemo } from '@/common/graphql/model/impl/salesRepWorkstation/businessMemo.impl';

@InputType()
class BusinessMemoSearchInput {
    @Field((type) => ID, { nullable: true })
    id?: number;

    @Field((type) => ID, { nullable: true })
    businessId: number;
}

@ArgsType()
export class BusinessMemoSearchArgs extends CommonSearchArgs(
    BusinessMemoSearchInput,
) {}

@InputType()
export class BusinessMemoCreateInput {
    @Field({ nullable: true })
    title?: string;

    @Field({ nullable: true })
    content?: string;

    @Field((type) => ID, { nullable: true })
    businessId?: number;
}

@InputType()
export class BusinessMemoUpdateInput {
    @Field((type) => ID)
    id: number;

    @Field({ nullable: true })
    title?: string;

    @Field({ nullable: true })
    content?: string;

    @Field((type) => ID, { nullable: true })
    businessId?: number;
}

@ObjectType()
export class BusinessMemoSearchResult extends PaginatedSearchResult(
    BusinessMemo,
) {}
