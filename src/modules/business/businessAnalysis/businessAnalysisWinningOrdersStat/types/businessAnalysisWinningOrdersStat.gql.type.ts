import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import { BusinessSearchInput } from '@/modules/business/business/types/business.gql.type';

@ObjectType()
export class BusinessAnalysisWinningOrdersStat {
    @Field(() => String, { description: '赢单机会「99%，75%，50%，25%」' })
    winningOpportunityCode: string;

    @Field(() => Int, { description: '当前月各赢单机会数量' })
    currentMonthCount: number;

    @Field(() => Int, { description: '上月各赢单机会数量' })
    lastMonthCount: number;
}

@InputType('BusinessAnalysisWinningOrdersStatSearchInput')
class FilterInput extends BusinessSearchInput {}

@ArgsType()
export class BusinessAnalysisWinningOrdersStatSearchArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
