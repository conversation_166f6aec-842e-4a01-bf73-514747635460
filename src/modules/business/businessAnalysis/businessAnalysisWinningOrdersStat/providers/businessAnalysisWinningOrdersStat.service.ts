import { Inject, Service } from 'typedi';
import _ from 'lodash';
import {
    BusinessAnalysisWinningOrdersStat,
    SearchParams,
} from '../types/businessAnalysisWinningOrdersStat.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BusinessService } from '@/modules/business/business/providers/business.service';
import { ArrayHelper } from '@/common/helpers/array.helper';
import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';

@Service()
export class BusinessAnalysisWinningOrdersStatService {
    @Inject()
    private businessService: BusinessService;

    async stat(
        params: SearchParams,
    ): Promise<BusinessAnalysisWinningOrdersStat[]> {
        const { rows } = await this.businessService.search(params);
        const sql = this.buildRawSQL(rows);
        type Row = { [key: string]: any };
        const items = await ClinicoDataSource.query<Row[]>(sql);

        const results: BusinessAnalysisWinningOrdersStat[] =
            this.transform(items);
        return results;
    }

    private transform(
        rows: { [key: string]: any }[],
    ): BusinessAnalysisWinningOrdersStat[] {
        const results: BusinessAnalysisWinningOrdersStat[] = rows.map(
            (row) => ({
                winningOpportunityCode: row.winning_opportunity_code,
                currentMonthCount: row.current_month_count,
                lastMonthCount: row.last_month_count,
            }),
        );

        return results;
    }

    private buildRawSQL(rows: Business[]): string {
        const sql = `
        SELECT
            bo1.code AS winning_opportunity_code
            ,COUNT(CASE WHEN DATE_PART('month', b.created_at) = DATE_PART('month', CURRENT_DATE) THEN 1 ELSE NULL END) AS current_month_count
            ,COUNT(CASE WHEN DATE_PART('month', b.created_at) = DATE_PART('month', CURRENT_DATE - INTERVAL '1 month') THEN 1 ELSE NULL END) AS last_month_count
        FROM
            sales_rep_workstation.businesses b
        LEFT JOIN sales_rep_workstation.business_statuses bs ON bs.ID = b.status_id
        LEFT JOIN sales_rep_workstation.business_opportunities bo1 ON bo1.ID = b.winning_opportunity_id
        WHERE b.id IN (${
            rows.length ? rows.map((r) => `'${r.id}'`).join(',') : '0'
        })
        AND bs.type = 'InProgress'
        AND b.created_at >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month')
        AND b.created_at < DATE_TRUNC('month', CURRENT_DATE + INTERVAL '1 month')
		AND bo1.code != '0%'
        GROUP BY
            bo1.code
        ORDER BY
            bo1.code
        `;
        return sql;
    }
}
