import { Inject, Service } from 'typedi';
import _ from 'lodash';
import {
    BusinessAnalysisProductRank,
    SearchParams,
} from '../types/businessAnalysisProductRank.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BusinessService } from '@/modules/business/business/providers/business.service';
import { ArrayHelper } from '@/common/helpers/array.helper';
import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';

@Service()
export class BusinessAnalysisProductRankService {
    @Inject()
    private businessService: BusinessService;

    async stat(params: SearchParams): Promise<BusinessAnalysisProductRank[]> {
        const { rows } = await this.businessService.search(params);
        const sql = this.buildRawSQL(rows);
        type Row = { [key: string]: any };
        const items = await ClinicoDataSource.query<Row[]>(sql);

        const results: BusinessAnalysisProductRank[] = this.transform(items);
        return results;
    }

    private transform(
        rows: { [key: string]: any }[],
    ): BusinessAnalysisProductRank[] {
        type Result = Omit<BusinessAnalysisProductRank, 'seq'>;
        const results: Omit<BusinessAnalysisProductRank, 'seq'>[] = rows.map(
            (row) => ({
                rank: Number(row.rank),
                businessProductId: row.id,
                qty: row.qty,
            }),
        );

        const sortedResults = ArrayHelper.assignSeq<Result>(results);
        return sortedResults;
    }

    private buildRawSQL(rows: Business[]): string {
        const sql = `
        WITH
        w_products AS (
            SELECT RANK() OVER (ORDER BY SUM(bp.qty) DESC, p.id) AS "rank"
                  ,p.id
                  ,SUM(bp.qty) AS qty
            FROM sales_rep_workstation.business_products p
            JOIN sales_rep_workstation.businesses_to_budget_products bp ON bp.budget_product_id = p.id
            JOIN sales_rep_workstation.businesses b ON bp.business_id = b.id
            WHERE b.id IN (${
                rows.length ? rows.map((r) => `'${r.id}'`).join(',') : '0'
            })
              AND p.deleted = false
            GROUP BY p.id
        )
        SELECT w."rank", w.id, w.qty
        FROM w_products w
        ORDER BY w."rank"
        `;
        return sql;
    }
}
