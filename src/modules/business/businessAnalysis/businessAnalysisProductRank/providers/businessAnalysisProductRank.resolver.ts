import { Inject, Service } from 'typedi';
import { <PERSON><PERSON><PERSON><PERSON>ver, Resolver, Root } from 'type-graphql';
import { BusinessAnalysisProductRank } from '../types/businessAnalysisProductRank.gql.type';
import { BusinessProduct } from '@/common/graphql/model/impl/salesRepWorkstation/businessProduct.impl';
import { BusinessProductService } from '../../../businessProduct/providers/businessProduct.service';

@Service()
@Resolver((of) => BusinessAnalysisProductRank)
export class BusinessAnalysisProductRankResolver {
    @Inject()
    private businessProductService: BusinessProductService;

    @FieldResolver((returns) => BusinessProduct)
    async businessProduct(
        @Root() businessAnalysisProductRank: BusinessAnalysisProductRank,
    ): Promise<BusinessProduct> {
        return await this.businessProductService.findOneOrError(
            businessAnalysisProductRank.businessProductId,
        );
    }
}
