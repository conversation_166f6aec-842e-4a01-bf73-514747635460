import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import { BusinessSearchInput } from '@/modules/business/business/types/business.gql.type';

@ObjectType()
export class BusinessAnalysisProductRank {
    @Field(() => Int, { description: '流水號' })
    seq: number;

    @Field(() => Int, { description: '排名' })
    rank: number;

    @Field(() => ID, { description: '商機商品' })
    businessProductId: number;

    @Field(() => Int, { description: '數量' })
    qty: number;
}

@InputType('BusinessAnalysisProductRankSearchInput')
class FilterInput extends BusinessSearchInput {}

@ArgsType()
export class BusinessAnalysisProductRankSearchArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
