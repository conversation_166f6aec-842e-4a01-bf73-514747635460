import { ArgsType, Field, ID, InputType, Int, ObjectType } from 'type-graphql';
import { BusinessSearchInput } from '@/modules/business/business/types/business.gql.type';

@ObjectType()
export class BusinessAnalysisProductStat {
    @Field(() => Int, { description: '流水號' })
    seq: number;

    @Field({ description: '年份' })
    year: string;

    @Field(() => Int, { description: '1月qty (台數)' })
    month1: number;

    @Field(() => Int, { description: '2月qty (台數)' })
    month2: number;

    @Field(() => Int, { description: '3月qty (台數)' })
    month3: number;

    @Field(() => Int, { description: '4月qty (台數)' })
    month4: number;

    @Field(() => Int, { description: '5月qty (台數)' })
    month5: number;

    @Field(() => Int, { description: '6月qty (台數)' })
    month6: number;

    @Field(() => Int, { description: '7月qty (台數)' })
    month7: number;

    @Field(() => Int, { description: '8月qty (台數)' })
    month8: number;

    @Field(() => Int, { description: '9月qty (台數)' })
    month9: number;

    @Field(() => Int, { description: '10月qty (台數)' })
    month10: number;

    @Field(() => Int, { description: '11月qty (台數)' })
    month11: number;

    @Field(() => Int, { description: '12月qty (台數)' })
    month12: number;

    @Field(() => Int, { description: 'total qty (台數)' })
    total: number;

    @Field(() => ID, { description: '商機商品' })
    businessProductId: number;
}

@InputType('BusinessAnalysisProductStatSearchInput')
class FilterInput extends BusinessSearchInput {}

@ArgsType()
export class BusinessAnalysisProductStatSearchArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
