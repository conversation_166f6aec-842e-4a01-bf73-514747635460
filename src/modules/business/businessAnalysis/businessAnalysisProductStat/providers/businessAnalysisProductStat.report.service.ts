import configs from '@/configs';
import moment from 'moment';
import _ from 'lodash';
import { Utils } from '@clinico/clinico-node-framework';
import { Inject, Service } from 'typedi';
import { BusinessAnalysisProductStatService } from './businessAnalysisProductStat.service';
import {
    BusinessAnalysisProductStat,
    SearchParams,
} from '../types/businessAnalysisProductStat.type';
import { EnumBusinessTransactionOpportunity } from '@/modules/business/business/types/business.type';
import { BusinessProductService } from '@/modules/business/businessProduct/providers/businessProduct.service';
import { BaseError } from '@clinico/base-error';
import { EnumBusinessStatusType } from '@clinico/typeorm-persistence/models/salesRepWorkstation/businessStatus.model';

@Service()
export class BusinessAnalysisProductStatReportService {
    @Inject()
    private businessProductService: BusinessProductService;
    @Inject()
    private businessAnalysisProductStatService: BusinessAnalysisProductStatService;

    async excel(params: SearchParams): Promise<Buffer> {
        const data = await this.data(params);
        const generator = new Utils.Excel2Templater();
        await generator.load(
            configs.templateFolder +
                `/excels/business/businessAnalysisProductStat.xlsx`,
        );

        generator.sheet('sheet1');
        generator.fillRows({
            startRowIndex: 3,
            data: data.map((el) => {
                return {
                    businessProductName: el.businessProductName,
                    year: el.year,
                    month: el.month,
                    high: el.highAmount || '',
                    mediumHigh: el.mediumHighAmount || '',
                    medium: el.mediumAmount || '',
                    others: el.othersAmount || '',
                    total: el.totalAmount || '',
                };
            }),
            cellOptions: {
                font: {
                    size: 10,
                    name: 'Arial',
                },
                alignment: {
                    horizontal: 'left',
                    vertical: 'justify',
                },
            },
        });
        return await generator.saveToBuffer();
    }

    async data(
        params: SearchParams,
    ): Promise<BusinessAnalysisProductStatReport[]> {
        await this.validate(params);

        // 資料基礎: 商機商品
        const { rows } = await this.businessProductService.search({
            ids: params.budgetProductIds,
        });
        const businessProducts = _.sortBy(rows, 'id');

        // 年份: 由「預計結束時間」判別
        const year1: number = parseInt(
            moment(params.expectedClosedDate1).format('YYYY'),
        );
        const year2: number = parseInt(
            moment(params.expectedClosedDate2).format('YYYY'),
        );

        // 分析資料
        const [highStats, mediumHighStats, mediumStats, othersStats] =
            await Promise.all([
                this.businessAnalysisProductStatService.stat({
                    ...params,
                    statusType: EnumBusinessStatusType.InProgress,
                    transactionOpportunity:
                        EnumBusinessTransactionOpportunity.High,
                }),
                this.businessAnalysisProductStatService.stat({
                    ...params,
                    statusType: EnumBusinessStatusType.InProgress,
                    transactionOpportunity:
                        EnumBusinessTransactionOpportunity.MediumHigh,
                }),
                this.businessAnalysisProductStatService.stat({
                    ...params,
                    statusType: EnumBusinessStatusType.InProgress,
                    transactionOpportunity:
                        EnumBusinessTransactionOpportunity.Medium,
                }),
                this.businessAnalysisProductStatService.stat({
                    ...params,
                    statusType: EnumBusinessStatusType.InProgress,
                    transactionOpportunity:
                        EnumBusinessTransactionOpportunity.Others,
                }),
            ]);

        const result: BusinessAnalysisProductStatReport[] = [];
        for (const businessProduct of businessProducts) {
            for (let year = year1; year <= year2; year++) {
                for (let month = 1; month <= 12; month++) {
                    const getAmount = (
                        stat: BusinessAnalysisProductStat[],
                    ): number => {
                        return (
                            stat.find(
                                (el) =>
                                    parseInt(el.year) === year &&
                                    el.businessProductId == businessProduct.id,
                            )?.[`month${month}`] || 0
                        );
                    };

                    const highAmount = getAmount(highStats);
                    const mediumHighAmount = getAmount(mediumHighStats);
                    const mediumAmount = getAmount(mediumStats);
                    const othersAmount = getAmount(othersStats);

                    const record: BusinessAnalysisProductStatReport = {
                        businessProductName: businessProduct.name,
                        year: year,
                        month: month,
                        highAmount: highAmount,
                        mediumHighAmount: mediumHighAmount,
                        mediumAmount: mediumAmount,
                        othersAmount: othersAmount,
                        totalAmount:
                            highAmount +
                            mediumHighAmount +
                            mediumAmount +
                            othersAmount,
                    };
                    result.push(record);
                }
            }
        }

        return result;
    }

    async validate(params: SearchParams): Promise<void> {
        if (!params.expectedClosedDate1) {
            throw new BaseError('請輸入預計結束日期(起)', 400);
        }
        if (!params.expectedClosedDate2) {
            throw new BaseError('請輸入預計結束日期(迄)', 400);
        }
    }
}
