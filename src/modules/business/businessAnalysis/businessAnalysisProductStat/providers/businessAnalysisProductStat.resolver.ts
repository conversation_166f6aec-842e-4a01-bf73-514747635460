import { Inject, Service } from 'typedi';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Resolver, Root } from 'type-graphql';
import { BusinessAnalysisProductStat } from '../types/businessAnalysisProductStat.gql.type';
import { BusinessProduct } from '@/common/graphql/model/impl/salesRepWorkstation/businessProduct.impl';
import { BusinessProductService } from '../../../businessProduct/providers/businessProduct.service';

@Service()
@Resolver((of) => BusinessAnalysisProductStat)
export class BusinessAnalysisProductStatResolver {
    @Inject()
    private businessProductService: BusinessProductService;

    @FieldResolver((returns) => BusinessProduct)
    async businessProduct(
        @Root() businessAnalysisProductStat: BusinessAnalysisProductStat,
    ): Promise<BusinessProduct> {
        return await this.businessProductService.findOneOrError(
            businessAnalysisProductStat.businessProductId,
        );
    }
}
