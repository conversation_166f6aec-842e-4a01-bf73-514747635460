import { Inject, Service } from 'typedi';
import _ from 'lodash';
import {
    BusinessAnalysisProductStat,
    SearchParams,
} from '../types/businessAnalysisProductStat.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BusinessService } from '@/modules/business/business/providers/business.service';
import { ArrayHelper } from '@/common/helpers/array.helper';
import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';

@Service()
export class BusinessAnalysisProductStatService {
    @Inject()
    private businessService: BusinessService;

    async stat(params: SearchParams): Promise<BusinessAnalysisProductStat[]> {
        const { rows } = await this.businessService.search(params);
        const sql = this.buildRawSQL(rows);
        type Row = { [key: string]: any };
        const items = await ClinicoDataSource.query<Row[]>(sql);

        const results: BusinessAnalysisProductStat[] = this.transform(items);
        return results;
    }

    private transform(
        rows: { [key: string]: any }[],
    ): BusinessAnalysisProductStat[] {
        type Result = Omit<BusinessAnalysisProductStat, 'seq'>;
        const results: Result[] = rows.map((row) => ({
            year: row.year,
            businessProductId: row.id,
            month1: Number(row.month1),
            month2: Number(row.month2),
            month3: Number(row.month3),
            month4: Number(row.month4),
            month5: Number(row.month5),
            month6: Number(row.month6),
            month7: Number(row.month7),
            month8: Number(row.month8),
            month9: Number(row.month9),
            month10: Number(row.month10),
            month11: Number(row.month11),
            month12: Number(row.month12),
            total: Number(row.total),
        }));

        const sortedResults = ArrayHelper.assignSeq<Result>(results);
        return sortedResults;
    }

    private buildRawSQL(rows: Business[]): string {
        const sql = `
        SELECT p.id
              ,DATE_PART('year', b.expected_closed_date) AS "year"
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 1 THEN bp.qty END) AS month1
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 2 THEN bp.qty END) AS month2
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 3 THEN bp.qty END) AS month3
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 4 THEN bp.qty END) AS month4
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 5 THEN bp.qty END) AS month5
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 6 THEN bp.qty END) AS month6
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 7 THEN bp.qty END) AS month7
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 8 THEN bp.qty END) AS month8
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 9 THEN bp.qty END) AS month9
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 10 THEN bp.qty END) AS month10
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 11 THEN bp.qty END) AS month11
              ,SUM(CASE WHEN DATE_PART('month', b.expected_closed_date) = 12 THEN bp.qty END) AS month12
              ,SUM(bp.qty) AS total
        FROM sales_rep_workstation.business_products              p
        JOIN sales_rep_workstation.businesses_to_budget_products bp ON bp.budget_product_id = p.id
        JOIN sales_rep_workstation.businesses                     b ON bp.business_id = b.id
        WHERE b.id IN (${
            rows.length ? rows.map((r) => `'${r.id}'`).join(',') : '0'
        })
          AND p.deleted = false
        GROUP BY p.id, DATE_PART('year', b.expected_closed_date)
        ORDER BY DATE_PART('year', b.expected_closed_date) ASC
                ,COUNT(DATE_PART('year', b.expected_closed_date)) DESC
        `;
        return sql;
    }
}
