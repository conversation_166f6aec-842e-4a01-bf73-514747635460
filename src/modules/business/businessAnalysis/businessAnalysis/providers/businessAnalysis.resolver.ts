import { Inject, Service } from 'typedi';
import { Args, FieldResolver, Query, Resolver } from 'type-graphql';
import { UserAuthInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { BusinessAnalysis } from '../types/businessAnalysis.gql.type';
import {
    BusinessAnalysisBusinessStat,
    BusinessAnalysisBusinessStatSearchArgs,
} from '../../businessAnalysisBusinessStat/types/businessAnalysisBusinessStat.gql.type';
import { BusinessAnalysisBusinessStatService } from '../../businessAnalysisBusinessStat/providers/businessAnalysisBusinessStat.service';
import {
    BusinessAnalysisProductRank,
    BusinessAnalysisProductRankSearchArgs,
} from '../../businessAnalysisProductRank/types/businessAnalysisProductRank.gql.type';
import { BusinessAnalysisProductRankService } from '../../businessAnalysisProductRank/providers/businessAnalysisProductRank.service';
import {
    BusinessAnalysisProductStat,
    BusinessAnalysisProductStatSearchArgs,
} from '../../businessAnalysisProductStat/types/businessAnalysisProductStat.gql.type';
import { BusinessAnalysisProductStatService } from '../../businessAnalysisProductStat/providers/businessAnalysisProductStat.service';
import { BusinessAnalysisWinningOrdersStatService } from '../../businessAnalysisWinningOrdersStat/providers/businessAnalysisWinningOrdersStat.service';
import {
    BusinessAnalysisWinningOrdersStat,
    BusinessAnalysisWinningOrdersStatSearchArgs,
} from '../../businessAnalysisWinningOrdersStat/types/businessAnalysisWinningOrdersStat.gql.type';

@Service()
@Resolver((of) => BusinessAnalysis)
export class BusinessAnalysisProductRankResolver {
    @Inject()
    private businessAnalysisBusinessStatService: BusinessAnalysisBusinessStatService;
    @Inject()
    private businessAnalysisProductRankService: BusinessAnalysisProductRankService;
    @Inject()
    private businessAnalysisProductStatService: BusinessAnalysisProductStatService;
    @Inject()
    private businessAnalysisWinningOrdersStatService: BusinessAnalysisWinningOrdersStatService;

    @UserAuthInterceptor('analysis.business')
    @Query(() => BusinessAnalysis, { name: 'businessAnalysis' })
    async stat(): Promise<BusinessAnalysis> {
        return {};
    }

    @FieldResolver((returns) => BusinessAnalysisBusinessStat, {
        description: '商機統計',
    })
    async businessStat(
        @Args() params: BusinessAnalysisBusinessStatSearchArgs,
    ): Promise<BusinessAnalysisBusinessStat> {
        const result = await this.businessAnalysisBusinessStatService.stat({
            ...params.filters,
            ...params,
        });
        return result;
    }

    @FieldResolver((returns) => [BusinessAnalysisProductRank], {
        description: '商機商品統計排名',
    })
    async businessProductRank(
        @Args() params: BusinessAnalysisProductRankSearchArgs,
    ): Promise<BusinessAnalysisProductRank[]> {
        const result = await this.businessAnalysisProductRankService.stat({
            ...params.filters,
            ...params,
        });
        return result;
    }

    @FieldResolver((returns) => [BusinessAnalysisProductStat], {
        description: '商機商品統計by月',
    })
    async businessProductStat(
        @Args() params: BusinessAnalysisProductStatSearchArgs,
    ): Promise<BusinessAnalysisProductStat[]> {
        const result = await this.businessAnalysisProductStatService.stat({
            ...params.filters,
            ...params,
        });
        return result;
    }

    @FieldResolver((returns) => [BusinessAnalysisWinningOrdersStat], {
        description: '商机赢单机会上月同期数量对比',
    })
    async businessWinningOrdersStat(
        @Args() params: BusinessAnalysisWinningOrdersStatSearchArgs,
    ): Promise<BusinessAnalysisWinningOrdersStat[]> {
        const result = await this.businessAnalysisWinningOrdersStatService.stat(
            {
                ...params.filters,
                ...params,
            },
        );
        return result;
    }
}
