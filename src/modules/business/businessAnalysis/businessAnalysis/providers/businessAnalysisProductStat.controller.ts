import {
    Body,
    Ctx,
    JsonController,
    Post,
    UseBefore,
} from 'routing-controllers';
import Koa from 'koa';
import Container from 'typedi';
import { OpenAPI } from 'routing-controllers-openapi';
import { BusinessAnalysisProductStatReportService } from '../../businessAnalysisProductStat/providers/businessAnalysisProductStat.report.service';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import moment from 'moment';
import { lowerCase } from 'lodash';
import { SearchParams } from '../../businessAnalysisProductStat/types/businessAnalysisProductStat.type';

const businessAnalysisProductStatReportService = Container.get(
    BusinessAnalysisProductStatReportService,
);

/**
 * Endpoint for user
 */
@JsonController('/businessAnalysis')
@UseBefore(UserAuthKoaInterceptor)
export class BusinessController {
    @OpenAPI({
        summary: '商機分析-成交機會清單匯出',
    })
    @Post('/productStat/export')
    async export(
        @Ctx() ctx: Koa.Context,
        @Body() params: SearchParams,
    ): Promise<any> {
        const fileName = `businessAnalysisProductStat_${moment().format(
            'YYYY-MM-DD_HHmm',
        )}.xlsx`;
        const body = await businessAnalysisProductStatReportService.excel({
            ...params,
        });

        ctx.set('Content-filename', fileName);
        ctx.set('Content-disposition', 'attachment; filename=' + fileName);
        ctx.set('Content-type', 'application/vnd.ms-excel');
        ctx.body = body;
        return ctx;
    }
}
