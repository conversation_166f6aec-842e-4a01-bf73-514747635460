import {
    ArgsType,
    Field,
    Float,
    InputType,
    Int,
    ObjectType,
} from 'type-graphql';
import { EnumBusinessStatType } from './businessAnalysisBusinessStat.enum';
import { BusinessSearchInput } from '@/modules/business/business/types/business.gql.type';

@ObjectType('BusinessAnalysisBusinessStat_Overall')
export class BusinessAnalysisBusinessStat_Overall {
    @Field(() => Int, { description: '數量' })
    count: number;

    @Field(() => Float, { description: '金額' })
    amount: string;
}

@ObjectType('BusinessAnalysisBusinessStat_Detail')
export class BusinessAnalysisBusinessStat_Detail {
    @Field(() => EnumBusinessStatType, { description: '類型' })
    type: EnumBusinessStatType;

    @Field(() => Int, { description: '數量' })
    count: number;

    @Field(() => Float, {
        nullable: true,
        description:
            '數量百分比（1: 100%, 0.1234: 12.34%, 0: 0%）。分母（總計數量）為 0 時，百分比顯示為空值',
    })
    countPercent: string | null;

    @Field(() => Float, { description: '金額' })
    amount: string;

    @Field(() => Float, {
        nullable: true,
        description:
            '金額百分比（1: 100%, 0.1234: 12.34%, 0: 0%）。分母（總計金額）為 0 時，百分比顯示為空值',
    })
    amountPercent: string | null;
}

@ObjectType('BusinessAnalysisBusinessStat')
export class BusinessAnalysisBusinessStat {
    @Field(() => BusinessAnalysisBusinessStat_Overall, {
        description: '總體統計',
    })
    overall: BusinessAnalysisBusinessStat_Overall;

    @Field(() => [BusinessAnalysisBusinessStat_Detail], {
        description: '細項統計',
    })
    details: BusinessAnalysisBusinessStat_Detail[];
}

@InputType('BusinessAnalysisBusinessStatFilterInput')
export class FilterInput extends BusinessSearchInput {}

@ArgsType()
export class BusinessAnalysisBusinessStatSearchArgs {
    @Field(() => FilterInput, { nullable: true })
    filters?: FilterInput;
}
