import { SearchParams as BusinessSearchParams } from '@/modules/business/business/types/business.type';
import { EnumBusinessStatType } from './businessAnalysisBusinessStat.enum';

export type BusinessAnalysisBusinessStat_Overall = {
    count: number;
    amount: string;
};

export type BusinessAnalysisBusinessStat_Detail = {
    type: EnumBusinessStatType;
    count: number;
    countPercent: string | null;
    amount: string;
    amountPercent: string | null;
};

export type BusinessAnalysisBusinessStat = {
    overall: BusinessAnalysisBusinessStat_Overall;
    details: BusinessAnalysisBusinessStat_Detail[];
};

export type SearchParams = BusinessSearchParams;
