import Big from 'big.js';
import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BusinessService } from '@/modules/business/business/providers/business.service';
import {
    BusinessAnalysisBusinessStat,
    BusinessAnalysisBusinessStat_Detail,
    BusinessAnalysisBusinessStat_Overall,
    SearchParams,
} from '../types/businessAnalysisBusinessStat.type';
import { EnumBusinessStatType } from '../types/businessAnalysisBusinessStat.enum';
import { Business } from '@clinico/typeorm-persistence/models/salesRepWorkstation/business.model';
import { BigHelper } from '@/common/helpers/big.helper';

@Service()
export class BusinessAnalysisBusinessStatService {
    @Inject()
    private businessService: BusinessService;

    async stat(params: SearchParams): Promise<BusinessAnalysisBusinessStat> {
        const { rows } = await this.businessService.search(params);
        const sql = this.buildRawSQL(rows);
        type Row = { [key: string]: any };
        const items = await ClinicoDataSource.query<Row[]>(sql);

        const results: BusinessAnalysisBusinessStat = this.transform(items);
        return results;
    }

    private transform(
        rows: { [key: string]: any }[],
    ): BusinessAnalysisBusinessStat {
        const mapToItems = _.groupBy(rows, 'type');
        const results: BusinessAnalysisBusinessStat = {
            overall: { count: 0, amount: '0' },
            details: [],
        };

        // 組裝 overall 層資料
        const overall: BusinessAnalysisBusinessStat_Overall = {
            count: 0,
            amount: '0',
        };
        const item = mapToItems['Overall']?.[0];
        if (item) {
            overall.count = new Big(item.count).toNumber();
            overall.amount = new Big(item.amount).toString();
        }
        results.overall = overall;

        // 組裝 details 層資料
        const types: EnumBusinessStatType[] = [
            EnumBusinessStatType.Winning,
            EnumBusinessStatType.Losing,
            EnumBusinessStatType.InProgress,
            EnumBusinessStatType.HighOpportunity,
            EnumBusinessStatType.MediumHighOpportunity,
            EnumBusinessStatType.MediumHighOpportunity_99_75,
            EnumBusinessStatType.MediumHighOpportunity_75_99,
            EnumBusinessStatType.MediumOpportunity,
            EnumBusinessStatType.OtherOpportunity,
            EnumBusinessStatType.Others,
        ];
        for (const type of types) {
            const detail: BusinessAnalysisBusinessStat_Detail = {
                type: type,
                count: 0,
                countPercent: null,
                amount: '0',
                amountPercent: null,
            };
            const item = mapToItems[type]?.[0];
            if (item) {
                const currentCount = new Big(item.count);
                detail.count = currentCount.toNumber();

                detail.countPercent = BigHelper.toPercentOrNull({
                    dividend: item.count,
                    divisor: results.overall.count,
                });

                const currentAmount = new Big(item.amount);
                detail.amount = currentAmount.toString();

                detail.amountPercent = BigHelper.toPercentOrNull({
                    dividend: item.amount,
                    divisor: results.overall.amount,
                });
            }
            results.details.push(detail);
        }
        return results;
    }

    private buildRawSQL(rows: Business[]): string {
        const sql = `
        WITH
        w_businesses AS (
            SELECT b.id
                  ,b.budget_amount
                  ,b.deal_amount
                  ,bs."type" AS status_type
                  ,bs.buying_opportunity
                  ,bo1.code AS winning_opportunity_code
            FROM sales_rep_workstation.businesses                  b
            LEFT JOIN sales_rep_workstation.business_statuses      bs  ON bs.id = b.status_id
            LEFT JOIN sales_rep_workstation.business_opportunities bo1 ON bo1.id = b.winning_opportunity_id
            WHERE b.id IN (${
                rows.length ? rows.map((r) => `'${r.id}'`).join(',') : `'0'`
            })
        ),
        w_overall_stat AS (
            SELECT 'Overall' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
        ),
        w_winning_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'ClosedInWinning'
        ),
        w_winning_stat AS (
            SELECT 'Winning' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_winning_list)
        ),
        w_losing_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'ClosedInLosing'
        ),
        w_losing_stat AS (
            SELECT 'Losing' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_losing_list)
        ),
        w_in_progress_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
        ),
        w_in_progress_stat AS (
            SELECT 'InProgress' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_in_progress_list)
        ),
        w_high_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND b.winning_opportunity_code = '99%'
              AND b.buying_opportunity = '99%'
        ),
        w_high_opportunity_stat AS (
            SELECT 'HighOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_high_opportunity_list)
        ),
        w_medium_high_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND ((b.winning_opportunity_code = '99%' AND b.buying_opportunity = '75%')
                OR (b.winning_opportunity_code = '75%' AND b.buying_opportunity = '99%'))
        ),
        w_medium_high_opportunity_stat AS (
            SELECT 'MediumHighOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_list)
        ),
        w_medium_high_opportunity_99_75_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code = '75%' AND b.buying_opportunity = '99%')
        ),
        w_medium_high_opportunity_99_75_stat AS (
            SELECT 'MediumHighOpportunity_99_75' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_99_75_list)
        ),
        w_medium_high_opportunity_75_99_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code = '99%' AND b.buying_opportunity = '75%')
        ),
        w_medium_high_opportunity_75_99_stat AS (
            SELECT 'MediumHighOpportunity_75_99' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_high_opportunity_75_99_list)
        ),
        w_medium_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND b.winning_opportunity_code = '75%'
              AND b.buying_opportunity = '75%'
        ),
        w_medium_opportunity_stat AS (
            SELECT 'MediumOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_medium_opportunity_list)
        ),
        w_other_opportunity_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.status_type = 'InProgress'
              AND (b.winning_opportunity_code NOT IN ('75%', '99%')
                OR b.buying_opportunity NOT IN ('75%', '99%'))
        ),
        w_other_opportunity_stat AS (
            SELECT 'OtherOpportunity' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_other_opportunity_list)
        ),
        w_others_list AS (
            SELECT b.id
            FROM w_businesses b
            WHERE b.id NOT IN (SELECT id FROM w_winning_list)
              AND b.id NOT IN (SELECT id FROM w_losing_list)
              AND b.id NOT IN (SELECT id FROM w_in_progress_list)
              AND b.id NOT IN (SELECT id FROM w_high_opportunity_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_99_75_list)
              AND b.id NOT IN (SELECT id FROM w_medium_high_opportunity_75_99_list)
              AND b.id NOT IN (SELECT id FROM w_medium_opportunity_list)
              AND b.id NOT IN (SELECT id FROM w_other_opportunity_list)
        ),
        w_others_stat AS (
            SELECT 'Others' AS "type"
                  ,COALESCE(COUNT(b.id), 0) AS "count"
                  ,COALESCE(SUM(b.budget_amount), 0) AS "amount"
            FROM w_businesses b
            WHERE b.id IN (SELECT id FROM w_others_list)
        )
        SELECT * FROM w_overall_stat
        UNION ALL
        SELECT * FROM w_winning_stat
        UNION ALL
        SELECT * FROM w_losing_stat
        UNION ALL
        SELECT * FROM w_in_progress_stat
        UNION ALL
        SELECT * FROM w_high_opportunity_stat
        UNION ALL
        SELECT * FROM w_medium_high_opportunity_stat
        UNION ALL
        SELECT * FROM w_medium_high_opportunity_99_75_stat
        UNION ALL
        SELECT * FROM w_medium_high_opportunity_75_99_stat
        UNION ALL
        SELECT * FROM w_medium_opportunity_stat
        UNION ALL
        SELECT * FROM w_other_opportunity_stat
        UNION ALL
        SELECT * FROM w_others_stat
        `;
        return sql;
    }
}
