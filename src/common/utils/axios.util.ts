import axios, { AxiosInstance, AxiosResponse } from 'axios';
import configs from '@/configs';

export class AxiosUtil {
    private static instance: AxiosInstance;

    static getInstance(): AxiosInstance {
        if (!this.instance) {
            this.instance = axios.create({
                timeout: configs.DOWNLOAD_TIMEOUT,
                maxContentLength: 100 * 1024 * 1024, // 100MB
                responseType: 'stream',
            });
        }
        return this.instance;
    }

    static async downloadFile(url: string): Promise<AxiosResponse> {
        try {
            const response = await this.getInstance().get(url);
            return response;
        } catch (error) {
            throw new Error(`Failed to download ${url}: ${error.message}`);
        }
    }
}
