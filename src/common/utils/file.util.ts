import * as fs from 'fs';
import * as path from 'path';
import configs from '@/configs';

export class FileUtil {
    /**
     * 确保临时目录存在
     */
    static ensureTempDir(): string {
        if (!fs.existsSync(configs.tempFolder)) {
            fs.mkdirSync(configs.tempFolder, { recursive: true });
        }
        return configs.tempFolder;
    }

    /**
     * 生成唯一文件名
     */
    static generateTempFilename(url: string): string {
        const ext = path.extname(new URL(url).pathname) || '.bin';
        return `${Date.now()}_${Math.random().toString(36).substring(2)}${ext}`;
    }

    /**
     * 清理旧临时文件
     */
    static cleanupTempFiles(): void {
        const now = Date.now();
        const files = fs.readdirSync(configs.tempFolder);

        files.forEach((file) => {
            const filePath = path.join(configs.tempFolder, file);
            const stat = fs.statSync(filePath);

            if (now - stat.mtimeMs > configs.TEMP_FILE_MAX_AGE) {
                fs.unlinkSync(filePath);
            }
        });
    }
}
