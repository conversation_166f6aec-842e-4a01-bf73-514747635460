import archiver from 'archiver';
import * as fs from 'fs';
import * as path from 'path';
import { FileUtil } from './file.util';
import configs from '@/configs';

export class ZipUtil {
    /**
     * 创建包含多个文件的ZIP
     */
    static async createZip(
        filename: string,
        files: Array<{ path: string; originalUrl: string }>,
    ): Promise<{ zipPath: string; filename: string }> {
        FileUtil.ensureTempDir();
        FileUtil.cleanupTempFiles();
        // 格式化当前日期为 YYYYMMDD
        const formattedDate = new Date()
            .toISOString()
            .slice(0, 10)
            .replace(/-/g, '');

        const folderName = `${filename}_${formattedDate}_${Date.now()}`;
        const zipFilename = `${folderName}.zip`;
        const zipPath = path.join(configs.tempFolder, zipFilename);
        const output = fs.createWriteStream(zipPath);
        const archive = archiver('zip', { zlib: { level: 9 } });

        return new Promise((resolve, reject) => {
            output.on('close', () => {
                resolve({ zipPath, filename: zipFilename });
            });

            archive.on('error', (err) => reject(err));
            archive.pipe(output);

            const outerFolderName = `${folderName}`;
            files.forEach((file) => {
                // 提取原始文件名并移除末尾UUID
                let baseName =
                    path.basename(
                        decodeURIComponent(new URL(file.originalUrl).pathname),
                    ) || `file_${Date.now()}`;

                // 移除文件名中的UUID部分
                const uuidPattern =
                    /-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(?=\.\w+$)/i; // eslint-disable-line no-useless-escape
                baseName = baseName.replace(uuidPattern, '');
                const regex = /eyeServiceOrders\/([^\/]+)\//; // eslint-disable-line no-useless-escape
                const match = file.originalUrl.match(regex);
                const folderName = match ? match[1] : 'other';
                const filePathInArchive = `${outerFolderName}/${folderName}/${baseName}`;
                archive.file(file.path, { name: filePathInArchive });
            });

            archive.finalize();
        });
    }
}
