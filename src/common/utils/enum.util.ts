//获取枚举对应的数字值
export class EnumUtil {
    static async getEnumValues<T>(
        enumType: any,
        input: T,
    ): Promise<EnumValueType<T>> {
        if (Array.isArray(input)) {
            // 处理数组情况
            const values = input
                .map((item) => enumType[item])
                .filter((value) => value !== undefined);
            return values as EnumValueType<T>;
        }

        // 处理单个值情况
        const value = enumType[input as string];
        return (value !== undefined ? value : null) as EnumValueType<T>;
    }
}

type EnumValueType<T> = T extends Array<any> ? number[] : number;
