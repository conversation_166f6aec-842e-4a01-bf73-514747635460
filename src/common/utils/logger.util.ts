import winston from 'winston';
import { Helpers } from '@clinico/clinico-node-framework';

let logger: winston.Logger;

export { logger };

export const initialize = (options?: winston.LoggerOptions) => {
    let initOptions = options;
    if (!initOptions) {
        initOptions = {
            level: 'debug',
            format: winston.format.json(),
            transports: [
                new winston.transports.File({
                    dirname: 'logs',
                    filename: 'error.log',
                    level: 'error',
                }),
                new winston.transports.File({
                    dirname: 'logs',
                    filename: 'system.log',
                    level: 'info',
                }),
            ],
        };
    }
    logger = winston.createLogger(initOptions);

    if (!Helpers.Env.isDevelopment()) {
        logger.add(
            new winston.transports.Console({
                format: winston.format.prettyPrint(),
            }),
        );
    }
};
