import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import { Province } from '@clinico/typeorm-persistence/models/public/province.model';
import { Region } from '@clinico/typeorm-persistence/models/public/region.model';
import _ from 'lodash';
import { Service } from 'typedi';

@Service()
export class ValidateService {
    private regionRepo = ClinicoDataSource.getRepository(Region);
    private provinceRepo = ClinicoDataSource.getRepository(Province);

    /**
     * 驗證公司是否在該地區內
     */
    async regionAndCompany(params: {
        regionId?: number;
        companyId?: number;
    }): Promise<void> {
        const results = await this.regionRepo.find({
            where: {
                id: params.regionId,
                companies: {
                    id: params.companyId,
                },
            },
        });
        if (results.length !== 1) {
            throw new BaseError('區域與公司關聯錯誤', 400);
        }
    }

    /**
     * 驗證 provinceId & cityId & districtId是否有關聯
     */
    async address(params: {
        provinceId?: number;
        cityId?: number;
        districtId?: number;
    }): Promise<void> {
        const results = await this.provinceRepo.find({
            where: {
                id: params.provinceId,
                cities: !_.isNil(params.cityId)
                    ? {
                          id: params.cityId,
                          districts: !_.isNil(params.districtId)
                              ? {
                                    id: params.districtId,
                                }
                              : {},
                      }
                    : {},
            },
        });
        if (
            results.length !== 1 &&
            (!_.isNil(params.provinceId) ||
                !_.isNil(params.cityId) ||
                !_.isNil(params.districtId))
        ) {
            throw new BaseError('地址錯誤: 省、市、區關聯錯誤', 400);
        }
    }
}
