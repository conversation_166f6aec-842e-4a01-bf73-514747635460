import { ArgsType, Field, InputType, Int, ObjectType } from 'type-graphql';
import { ClassType } from '../interfaces/common.interface';
import { GraphQLUpload, FileUpload } from '@clinico/graphql-upload';

@ArgsType()
export class PageArgs {
    @Field((type) => Int, { nullable: true, defaultValue: 0 })
    offset?: number;

    @Field((type) => Int, { nullable: true, defaultValue: 50 })
    limit?: number;
}

export const CommonSearchArgs = <T>(filters: ClassType<T>) => {
    @ArgsType()
    class CommonSearchArgsClass extends PageArgs {
        @Field((type) => filters)
        filters: T;
    }
    return CommonSearchArgsClass;
};

@ObjectType()
export class PageInfo {
    @Field((type) => Boolean)
    hasNextPage: boolean;

    @Field((type) => Boolean)
    hasPreviousPage: boolean;
}

export const PaginatedSearchResult = <T>(
    items: ClassType<T> | string | number | boolean,
) => {
    @ObjectType({ isAbstract: true })
    abstract class PaginatedSearchResultClass {
        @Field((type) => [items])
        rows: T[];

        @Field((type) => Int)
        count: number;

        @Field((type) => PageInfo)
        pageInfo: PageInfo;
    }
    return PaginatedSearchResultClass;
};

@InputType()
export class AttachmentCreateInput {
    @Field((type) => GraphQLUpload)
    file: Promise<FileUpload>;

    @Field({ description: '檔案名稱' })
    name: string;

    @Field({ nullable: true })
    memo?: string;
}

@ObjectType()
export class UserAgents {
    @Field({ nullable: true, description: '瀏覽器名稱' })
    browserName?: string;

    @Field({ nullable: true, description: '瀏覽器版本' })
    browserVersion?: string;

    @Field({ nullable: true, description: '作業系統名稱' })
    osName?: string;

    @Field({ nullable: true, description: '作業系統版本' })
    osVersion?: string;

    @Field({ nullable: true, description: '裝置類別' })
    deviceType?: string;

    @Field({ nullable: true, description: '裝置品牌' })
    deviceVendor?: string;

    @Field({ nullable: true, description: '裝置型號' })
    deviceModel?: string;
}
