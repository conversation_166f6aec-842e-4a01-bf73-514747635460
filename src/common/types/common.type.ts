import { ArgsDictionary, ResolverFilterData } from 'type-graphql';
import { Context as WsContext } from 'graphql-ws/lib/server';
import { WsContextExtra } from './websocket.type';
import { EnumSortDirection } from '../enums/common.enum';

export type CommonSearchParams = CommonIdParams &
    CommonLimitParams &
    CommonExportParams &
    CommonSortParams;

export type CommonIdParams = {
    id?: number;
    ids?: number[];
    regionIds?: number[];
    companyIds?: number[];
    salesTeamGroupIds?: number[];
    salesTeamIds?: number[];
    productTeamIds?: number[];
    permissionUserIds?: number[];
};

export type CommonLimitParams = {
    limit?: number;
    offset?: number;
};

export type CommonExportParams = {
    isForExport?: boolean;
};

export type CommonSortParams = {
    sorts?: {
        name: any;
        direction: EnumSortDirection;
    }[];
};

export type CommonCreateParams = {
    regionId?: number;
    createdUserId: number;
};

export type CommonUpdateParams = {
    id: number;
    updatedUserId: number;
};

export type CommonDeletedParams = {
    id: number;
    deletedUserId: number;
};

export type CommonPageInfo = {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
};

export type CommonFindAndCountResult<T> = {
    rows: T[];
    count: number;
};

export type CommonSearchResult<T> = {
    rows: T[];
    count: number;
    pageInfo: CommonPageInfo;
};

export type CommonSubscriptionFilter<T> = ResolverFilterData<
    T,
    ArgsDictionary,
    WsContext<any, WsContextExtra>
>;

export type CommonAPI<T> = {
    success: boolean;
    result: {
        data: T;
        total?: number;
        nextId?: number;
        hasNext?: boolean;
    };
};
