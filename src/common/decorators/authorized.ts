import { BaseError } from '@clinico/base-error';
import Container from 'typedi';
import { MetadataStorage } from '@/common/metadata/metadataStorage';

const metadataStorage = <MetadataStorage>Container.get('MetadataStorage');

export function Authorized(permissionCode: string, defaultValue: any) {
    return function (prototype, propertyKey) {
        if (typeof propertyKey === 'symbol') {
            throw new BaseError('Symbol keys are not supported yet!', 500);
        }

        metadataStorage.collectAuthorizedFieldMetadata({
            target: prototype.constructor.name,
            fieldName: propertyKey,
            permissionCode: permissionCode,
            defaultValue: defaultValue,
        });
    };
}
