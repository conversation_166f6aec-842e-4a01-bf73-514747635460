import { FileUpload } from '@clinico/graphql-upload';
import { GRPCFileUpload } from '../types/common.grpc.type';

export namespace FileHelper {
    export async function getFile(
        file: Promise<FileUpload> | GRPCFileUpload,
    ): Promise<{
        body: any;
        filename: string;
        mimetype: string;
        encoding: string;
    }> {
        let result: any = {};
        if ('content' in file) {
            // type of GRPCFileUpload
            result = {
                ...file,
                body: file.content,
            };
        } else {
            // type of FileUpload
            const _file = await file;
            result = {
                ..._file,
                body: _file.createReadStream(),
            };
        }
        return result;
    }
}
