import { FindOperator, Raw } from 'typeorm';

export namespace TypeOrmHelper {
    export const InOrNull = (ids: number[]): FindOperator<any> => {
        return Raw((columnAlias) => {
            if (!ids || ids.length === 0) {
                return `(${columnAlias} IS NULL)`;
            } else {
                return `(${columnAlias} IS NULL OR ${columnAlias} IN ( ${
                    ids?.join(',') || ''
                }))`;
            }
        });
    };
}
