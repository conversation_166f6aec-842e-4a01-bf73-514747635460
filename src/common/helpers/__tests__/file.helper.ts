import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import { ReadStream } from 'fs-capacitor';
import { FileHelper } from '../file.helper';

describe('FileHelper', () => {
    describe('getFile', () => {
        it('should return the correct file information for a FileUpload object', async () => {
            // Create a mock FileUpload object
            const file = {
                createReadStream: jest.fn(),
                filename: 'test.png',
                mimetype: 'image/png',
                encoding: 'utf-8',
            };

            // Mock the createReadStream method to return a ReadStream object
            const readStream = {} as ReadStream;
            file.createReadStream.mockReturnValue(readStream);

            // Call the getFile function with the mock object
            const result = await FileHelper.getFile(Promise.resolve(file));

            // Assert that the result matches the expected output
            expect(result.body).toEqual(readStream);
            expect(result.filename).toEqual('test.png');
            expect(result.mimetype).toEqual('image/png');
            expect(result.encoding).toEqual('utf-8');
        });

        it('should return the correct file information for a GRPCFileUpload object', async () => {
            // Create a mock GRPCFileUpload object
            const file: GRPCFileUpload = {
                content: Buffer.from('test content'),
                filename: 'test.txt',
                mimetype: 'text/plain',
                encoding: 'utf-8',
            };

            // Call the getFile function with the mock object
            const result = await FileHelper.getFile(file);

            // Assert that the result matches the expected output
            expect(result.body).toEqual(Buffer.from('test content'));
            expect(result.filename).toEqual('test.txt');
            expect(result.mimetype).toEqual('text/plain');
            expect(result.encoding).toEqual('utf-8');
        });
    });
});
