import { BigHelper } from '../big.helper';

describe('BigHelper', () => {
    describe('toPercentOrNull', () => {
        const { toPercentOrNull } = BigHelper;

        test('should return null if divisor is 0', () => {
            const result = toPercentOrNull({
                dividend: 5,
                divisor: 0,
            });
            expect(result).toBeNull();
        });

        test('should return null if either dividend or divisor is not a finite number', () => {
            const result1 = toPercentOrNull({
                dividend: 7,
                divisor: -Infinity,
            });
            expect(result1).toBeNull();

            const result2 = toPercentOrNull({
                dividend: NaN,
                divisor: 4,
            });
            expect(result2).toBeNull();

            const result3 = toPercentOrNull({
                dividend: 2,
                divisor: {},
            });
            expect(result3).toBeNull();
        });

        test('should return the correct percentage with default precision', () => {
            const result1 = toPercentOrNull({
                dividend: 3,
                divisor: 4,
            });
            expect(result1).toBe('0.7500');

            const result2 = toPercentOrNull({
                dividend: 7,
                divisor: 6,
            });
            expect(result2).toBe('1.1667');

            const result3 = toPercentOrNull({
                dividend: 90,
                divisor: 3,
            });
            expect(result3).toBe('30.0000');
        });

        test('should return the correct percentage with custom precision', () => {
            const result1 = toPercentOrNull({
                dividend: 3,
                divisor: 4,
                precision: 2,
            });
            expect(result1).toBe('0.75');

            const result2 = toPercentOrNull({
                dividend: 7,
                divisor: 10,
                precision: 3,
            });
            expect(result2).toBe('0.700');

            const result3 = toPercentOrNull({
                dividend: 90,
                divisor: 100,
                precision: 0,
            });
            expect(result3).toBe('1');
        });
    });
});
