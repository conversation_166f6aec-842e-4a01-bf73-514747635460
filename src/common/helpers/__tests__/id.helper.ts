import { Id<PERSON><PERSON><PERSON> } from '../id.helper';

describe('IdHelper', () => {
    describe('idsParse', () => {
        it('should parse number and string IDs to number array', () => {
            expect(IdHelper.idsParse([1, '2', 3])).toEqual([1, 2, 3]);
            expect(IdHelper.idsParse(['1', '2', '3'])).toEqual([1, 2, 3]);
        });
    });

    describe('uniqIds', () => {
        it('should return unique number IDs array', () => {
            expect(IdHelper.uniqIds([1, '2', 2, 3, 4])).toEqual([1, 2, 3, 4]);
            expect(IdHelper.uniqIds(['1', '2', '2', '3', '4'])).toEqual([
                1, 2, 3, 4,
            ]);
        });
    });

    describe('intersectionIds', () => {
        it('should return intersection of two number IDs arrays', () => {
            expect(IdHelper.intersectionIds([1, '2', 2, 3], [1, 3, 4])).toEqual(
                [1, 3],
            );
            expect(IdHelper.intersectionIds([1, '2', '3'], [4, 5, 6])).toEqual(
                [],
            );
            expect(IdHelper.intersectionIds(undefined, [1, 2, 3])).toEqual([
                1, 2, 3,
            ]);
        });
    });
});
