import { CommonSearchParams } from '@/common/types/common.type';
import { PageInfoHelper } from '../pageInfo.helper';

describe('PageInfoHelper', () => {
    describe('generate', () => {
        it('should return pageInfo with hasNextPage and hasPreviousPage correctly set', () => {
            // Arrange
            const totalCount = 100;
            const searchParams: CommonSearchParams = {
                limit: 20,
                offset: 40,
            };

            // Act
            const pageInfo = PageInfoHelper.generate({
                totalCount,
                searchParams,
            });

            // Assert
            expect(pageInfo).toEqual({
                hasNextPage: true,
                hasPreviousPage: true,
            });
        });
    });
});
