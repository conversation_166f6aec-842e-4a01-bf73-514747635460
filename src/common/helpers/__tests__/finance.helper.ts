import Big from 'big.js';
import { FinanceHelper } from '../finance.helper';

describe('FinanceHelper', () => {
    Big.DP = 2;

    test(`Should calculate untaxed amount`, () => {
        const mocks = [
            {
                amount: '100',
                taxRate: '0.05',
                tobe: '95.24',
            },
            {
                amount: '100.12',
                taxRate: '0.34',
                tobe: '74.72',
            },
        ];
        for (const mock of mocks) {
            expect(FinanceHelper.calcUntaxedAmount(mock)).toBe(mock.tobe);
        }
    });

    test(`Should calculate exchanged amount`, () => {
        const mocks = [
            {
                amount: '100',
                exchangeRate: '0.23',
                tobe: '23.00',
            },
            {
                amount: '100.12',
                exchangeRate: '0.34',
                tobe: '34.04',
            },
        ];
        for (const mock of mocks) {
            expect(FinanceHelper.calcExchangedAmount(mock)).toBe(mock.tobe);
        }
    });

    test(`Should calculate discount amount`, () => {
        const mocks = [
            {
                amount: '100',
                discountRate: '0.23',
                tobe: '77.00',
            },
            {
                amount: '100.12',
                discountRate: '1',
                tobe: '0.00',
            },
            {
                amount: '100.12',
                discountRate: '0',
                tobe: '100.12',
            },
        ];
        for (const mock of mocks) {
            expect(FinanceHelper.calcDiscountAmount(mock)).toBe(mock.tobe);
        }
    });

    test(`Should calculate discount rate`, () => {
        const mocks = [
            {
                amount: '100',
                discountAmount: '77',
                tobe: '0.23',
            },
            {
                amount: '100.12',
                discountAmount: '0',
                tobe: '1.00',
            },
            {
                amount: '100.12',
                discountAmount: '100.12',
                tobe: '0.00',
            },
        ];
        for (const mock of mocks) {
            expect(FinanceHelper.calcDiscountRate(mock)).toBe(mock.tobe);
        }
    });

    test(`Should calculate commission rate`, () => {
        const mocks = [
            {
                amount: '100',
                commissionAmount: '77',
                tobe: '0.77',
            },
            {
                amount: '100.12',
                commissionAmount: '0',
                tobe: '0.00',
            },
            {
                amount: '100.12',
                commissionAmount: '100.12',
                tobe: '1.00',
            },
        ];
        for (const mock of mocks) {
            expect(FinanceHelper.calcCommissionRate(mock)).toBe(mock.tobe);
        }
    });

    test(`Should return the correct gross profit margin`, () => {
        const mocks = [
            {
                revenueAmount: '100',
                costOfGoodsSoldAmount: '50',
                tobe: '0.50',
            },
            {
                revenueAmount: '200',
                costOfGoodsSoldAmount: '150',
                tobe: '0.25',
            },
            {
                revenueAmount: '500',
                costOfGoodsSoldAmount: '400',
                tobe: '0.20',
            },
        ];
        for (const mock of mocks) {
            expect(FinanceHelper.calcGrossProfitMargin(mock)).toBe(mock.tobe);
        }
    });
});
