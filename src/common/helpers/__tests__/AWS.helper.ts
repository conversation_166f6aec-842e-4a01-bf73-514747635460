import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../AWS.helper';

describe('AWSHelper', () => {
    describe('genS3Key', () => {
        it('should generate the correct S3 key for EyeFixedAssetDocument', () => {
            const params = {
                filename: 'test',
                ext: '.doc',
                paths: ['ES000000001'],
                s3KeyType: AWSHelper.S3KeyType.EyeFixedAssetDocument,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^eyeFixedAssets\/ES000000001\/documents\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.doc$/,
            );
        });

        it('should generate the correct S3 key for EyeFixedAssetImage', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['ES000000001'],
                s3KeyType: AWSHelper.S3KeyType.EyeFixedAssetImage,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^eyeFixedAssets\/ES000000001\/images\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.jpg$/,
            );
        });

        it('should generate the correct S3 key for EyeProductAttachment', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['ES000000001'],
                s3KeyType: AWSHelper.S3KeyType.EyeProductAttachment,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^eyeProducts\/ES000000001\/attachments\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.jpg$/,
            );
        });

        it('should generate the correct S3 key for EyeServiceOrderAttachment', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['ES000000001'],
                s3KeyType: AWSHelper.S3KeyType.EyeServiceOrderAttachment,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^eyeServiceOrders\/ES000000001\/attachments\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.jpg$/,
            );
        });

        it('should generate the correct S3 key for EyeServiceOrderWorkDiaryAttachment', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['ES000000001'],
                s3KeyType:
                    AWSHelper.S3KeyType.EyeServiceOrderWorkDiaryAttachment,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^eyeServiceOrderWorkDiaries\/ES000000001\/attachments\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.jpg$/,
            );
        });

        it('should generate the correct S3 key for CustomerAttachment', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['C0000001'],
                s3KeyType: AWSHelper.S3KeyType.CustomerAttachment,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^customers\/C0000001\/attachments\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.jpg$/,
            );
        });

        it('should generate the correct S3 key for CustomerCertificate', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['C0000001', 'certificate1'],
                s3KeyType: AWSHelper.S3KeyType.CustomerCertificate,
            };

            const s3Key = AWSHelper.genS3Key(params);

            expect(s3Key).toMatch(
                /^customers\/C0000001\/certificates\/certificate1\/test-[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\.jpg$/,
            );
        });

        it('should throw an error if an invalid S3KeyType is provided', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['ES000000001'],
                s3KeyType: <any>'invalid key type',
            };

            expect(() => {
                AWSHelper.genS3Key(params);
            }).toThrowError('生成S3 Key失敗, invalid key type 尚未實作');
        });

        it('should throw an error if the number of paths is incorrect', () => {
            const params = {
                filename: 'test',
                ext: '.jpg',
                paths: ['ES000000001', 'extra path'],
                s3KeyType: AWSHelper.S3KeyType.EyeServiceOrderAttachment,
            };

            expect(() => {
                AWSHelper.genS3Key(params);
            }).toThrowError('生成S3 Key失敗, 3 參數錯誤');
        });
    });
});
