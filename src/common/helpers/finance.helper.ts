import Big from 'big.js';

export namespace FinanceHelper {
    // 未稅金額計算
    export const calcUntaxedAmount = (params: {
        amount: string;
        taxRate: string;
    }): string => {
        const { amount, taxRate } = params;

        const rate = Big(1).add(taxRate);
        // 未稅金額 = 含稅金額 / 1.05
        return Big(amount).div(rate).toFixed(Big.DP);
    };

    // 匯率計算
    export const calcExchangedAmount = (params: {
        amount: string;
        exchangeRate: string;
    }): string => {
        const { amount, exchangeRate } = params;

        return Big(amount).times(exchangeRate).toFixed(Big.DP);
    };

    // 匯率計算(四捨五入)
    export const calcExchangedAmountRound = (params: {
        amount: string;
        exchangeRate: string;
    }): string => {
        const { amount, exchangeRate } = params;

        return Big(amount)
            .times(exchangeRate)
            .round(0, Big.roundHalfUp)
            .toFixed(Big.DP);
    };

    // 折扣金額計算
    export const calcDiscountAmount = (params: {
        amount: string;
        discountRate: string;
    }): string => {
        const { amount, discountRate } = params;

        const diff = Big(1).minus(discountRate);
        return Big(amount).times(diff).toFixed(Big.DP);
    };

    // 折扣率計算
    export const calcDiscountRate = (params: {
        amount: string;
        discountAmount: string;
    }): string => {
        const { amount, discountAmount } = params;

        if (Big(amount).eq(0)) {
            return '1';
        }
        const rate = Big(discountAmount).div(amount);
        return Big(1).minus(rate).toFixed(Big.DP);
    };

    //研究费比率计算
    export const calcCommissionRate = (params: {
        amount: string;
        commissionAmount: string;
    }): string => {
        const { amount, commissionAmount } = params;

        if (Big(amount).eq(0)) {
            return '0';
        }
        const rate = Big(commissionAmount).div(amount);
        return rate.toFixed(Big.DP);
    };

    // 毛利率計算
    export const calcGrossProfitMargin = (params: {
        revenueAmount: string; // 販售金額
        costOfGoodsSoldAmount: string; // 銷貨成本
    }): string => {
        const { revenueAmount, costOfGoodsSoldAmount } = params;

        if (Big(revenueAmount).eq(0)) {
            return '0';
        }
        const diff = Big(revenueAmount).minus(costOfGoodsSoldAmount);
        return diff.div(revenueAmount).toFixed(Big.DP);
    };

    // 研究費佔比
    export const calcCommissionAmountRate = (params: {
        amount: string;
        commissionAmount: string; // 研究費
    }): string => {
        const { amount, commissionAmount } = params;

        if (Big(amount).eq(0)) {
            return '1';
        }
        const rate = Big(commissionAmount).div(amount);
        return Big(1).minus(rate).toFixed(Big.DP);
    };
}
