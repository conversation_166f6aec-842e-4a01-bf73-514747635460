import _ from 'lodash';

export namespace ArrayHelper {
    /** 調整排名 */
    export function alignRanking<T>(
        rows: (T & { rank: number })[],
        field: keyof T,
    ): (T & { seq: number; rank: number })[] {
        let prev: { rank: number; value: any } = { rank: 0, value: null };
        const results = _.sortBy(rows, 'rank').map((row, idx) => {
            const seq = idx + 1;
            const curr: { rank: number; value: any } = {
                rank: seq,
                value: row[field],
            };

            if (field in row && curr.value === prev.value) {
                curr.rank = prev.rank;
            }

            prev = curr;
            return { ...row, seq, rank: curr.rank };
        });

        return results;
    }

    /** 賦予seq */
    export function assignSeq<T>(rows: T[]): (T & { seq: number })[] {
        let seq = 1;

        const results = rows.map((item) => {
            const result = { ...item, seq: seq++ };
            return result;
        });
        return results;
    }
}
