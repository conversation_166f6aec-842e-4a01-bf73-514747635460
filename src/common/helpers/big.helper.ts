import Big from 'big.js';

export namespace BigHelper {
    /** @description 計算百分比（無法計算時，回傳空值） */
    export function toPercentOrNull(params: {
        dividend: any;
        divisor: any;
        precision?: number;
    }): string | null {
        // Return null if dividend or divisor is not a number (maybe be NaN or Infinity)
        if (
            !Number.isFinite(params.dividend) ||
            !Number.isFinite(params.divisor)
        ) {
            return null;
        }

        const precision = params.precision ?? 4;
        Big.DP = precision;

        const numDividend = new Big(params.dividend);
        const numDivisor = new Big(params.divisor);

        if (numDivisor.eq(0)) {
            // 無法計算百分比的數值
            return null;
        }
        // return numDividend.dividedBy(numDivisor).toFixed(precision);
        return numDividend.div(numDivisor).toFixed(precision);
    }
}
