import _ from 'lodash';

export namespace IdHelper {
    export type ID = number | string;

    /**
     * 將id (number | string) => number
     */
    export const idParse = (id: ID): number => {
        return parseInt(id.toString());
    };

    /**
     * 將ids (number | string)[] => number[]
     */
    export const idsParse = (ids: ID[]): number[] => {
        return ids.map((el) => parseInt(el.toString()));
    };

    /**
     * 過濾重複Ids
     * e.g: [1, '2', 2, 3, 4] => [1,2,3,4]
     */
    export const uniqIds = (ids: ID[]): number[] => {
        return _.uniq(idsParse(ids));
    };

    /**
     * 找出兩組ids的交集
     * 如果原ids = undefined or null，回傳id2s
     * e.g:
     *  ids: [1, 1, 2, 3]
     *  id2s: [1, 3, 4]
     * return: [1, 3]
     */
    export const intersectionIds = (
        ids: ID[] | undefined,
        id2s: ID[],
    ): number[] => {
        return !_.isNil(ids)
            ? _.intersection(idsParse(ids), idsParse(id2s))
            : idsParse(id2s);
    };
}
