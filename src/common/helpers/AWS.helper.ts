import { Helpers } from '@clinico/clinico-node-framework';
import { v4 as uuidv4 } from 'uuid';
import configs from '@/configs';
import { BaseError } from '@clinico/base-error';

export namespace AWSHelper {
    export enum S3KeyType {
        EyeFixedAssetDocument,
        EyeFixedAssetImage,
        EyeProductAttachment,
        EyeServiceOrderAttachment,
        EyeServiceOrderAttachFile,
        EyeServiceOrderAcceptance,
        EyeServiceOrderWorkDiaryAttachment,
        CustomerAttachment,
        CustomerCertificate,
        MaterialImage,
        EyeQuotationOrderAttachFile,
        CustomerEquipment,
        ContactPerson,
        MarketActivity,
        BidAttachment,
        DistributorAuthorizationAttachment,
        WeeklyWorkReportAttachment,
        VisitAttachFile,
        EyeWarrantyContractAttachment,
        EyeWarrantyContractItemAttachment,
    }

    export function bucket(): string {
        if (Helpers.Env.isProduction()) {
            return configs.aws.s3.bucket.prod;
        } else {
            return configs.aws.s3.bucket.dev;
        }
    }

    /**
     * 用檔名生成S3 key
     * 結構: {BASE_PATH}/{PATHS}.../{FILE_NAME}-{UUID}{EXT}
     * e.g.  eyeOrderServiceAttachFiles/ES000000001/圖片A-352d6b84-a45c-4cc3-ab03-780b860f09ed.jpg
     * @param params.filename 檔案名稱
     * @param params.ext 副檔案 e.g. .jpg
     * @param params.paths 路徑節點 e.g. ['aaa', '123'] => {BASE_PATH}/aaa/123/...
     * @param params.s3KeyType
     * @returns s3 key
     */
    export function genS3Key(params: {
        filename?: string;
        ext: string;
        paths: (string | number)[];
        s3KeyType: S3KeyType;
    }): string {
        // 驗證參數數量
        const validate = (num: number): void => {
            if (params.paths.length != num) {
                throw new BaseError(
                    `生成S3 Key失敗, ${params.s3KeyType} 參數錯誤`,
                    500,
                );
            }
        };

        // 檔名與副檔名中間，夾一個 UUID
        const newFileName = params?.filename
            ? `${params.filename}-${uuidv4()}${params.ext}`
            : `${uuidv4()}${params.ext}`;

        switch (params.s3KeyType) {
            case S3KeyType.EyeFixedAssetDocument:
                validate(1);
                return `eyeFixedAssets/${params.paths[0]}/documents/${newFileName}`;
            case S3KeyType.EyeFixedAssetImage:
                validate(1);
                return `eyeFixedAssets/${params.paths[0]}/images/${newFileName}`;
            case S3KeyType.EyeProductAttachment:
                validate(1);
                return `eyeProducts/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.EyeServiceOrderAttachment:
                validate(1);
                return `eyeServiceOrders/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.EyeServiceOrderAttachFile:
                validate(1);
                return `eyeServiceOrders/${params.paths[0]}/attachFiles/${newFileName}`;
            case S3KeyType.EyeServiceOrderWorkDiaryAttachment:
                validate(1);
                return `eyeServiceOrderWorkDiaries/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.CustomerAttachment:
                validate(1);
                return `customers/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.CustomerCertificate:
                validate(2);
                return `customers/${params.paths[0]}/certificates/${params.paths[1]}/${newFileName}`;
            case S3KeyType.MaterialImage:
                validate(1);
                return `materials/${params.paths[0]}/images/${newFileName}`;
            case S3KeyType.EyeQuotationOrderAttachFile:
                validate(1);
                return `eyeQuotationOrders/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.CustomerEquipment:
                validate(2);
                return `customers/${params.paths[0]}/equipments/${params.paths[1]}/${newFileName}`;
            case S3KeyType.ContactPerson:
                validate(1);
                return `contactPeople/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.MarketActivity:
                validate(1);
                return `marketActivity/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.BidAttachment:
                validate(1);
                return `bid/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.DistributorAuthorizationAttachment:
                validate(1);
                return `distributorAuthorization/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.WeeklyWorkReportAttachment:
                validate(1);
                return `weeklyWorkReport/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.VisitAttachFile:
                validate(1);
                return `visit/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.EyeWarrantyContractAttachment:
                validate(1);
                return `eyeWarrantyContract/${params.paths[0]}/attachments/${newFileName}`;
            case S3KeyType.EyeWarrantyContractItemAttachment:
                validate(2);
                return `eyeWarrantyContract/${params.paths[0]}/items/${params.paths[1]}/attachments/${newFileName}`;
            default:
                throw new BaseError(
                    `生成S3 Key失敗, ${params.s3KeyType} 尚未實作`,
                    500,
                );
        }
    }
}
