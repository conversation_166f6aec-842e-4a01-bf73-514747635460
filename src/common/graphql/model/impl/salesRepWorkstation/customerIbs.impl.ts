import { Field, ObjectType } from 'type-graphql';
import { ICustomerIb } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerIb.model';

@ObjectType({ implements: ICustomerIb })
export class CustomerIb implements ICustomerIb {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    createdUserId: number;
    updatedUserId: number;
    code: string;
    navCustomerCode: string;
    customerId: number;
    materialCode: string;
    materialId: number;
    sn: string;
    installationDate: Date;
    salesDate: Date;
    lastServiceDate: Date;
}
