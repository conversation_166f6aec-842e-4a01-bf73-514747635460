import { Field, ObjectType } from 'type-graphql';
import { IBusinessLosingReason } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessLosingReason.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IBusinessLosingReason })
export class BusinessLosingReason implements IBusinessLosingReason {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
