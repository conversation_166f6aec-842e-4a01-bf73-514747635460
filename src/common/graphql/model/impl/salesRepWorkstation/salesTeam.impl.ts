import { Field, ObjectType } from 'type-graphql';
import { ISalesTeam } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/salesTeam.model';
import { SalesTeamsUser } from './salesTeamsUser.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ISalesTeam })
export class SalesTeam implements ISalesTeam {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    code: string;
    groupId: number;
    managerId: number;
    parentId: number;
    requiredForPrimaryCustomer: boolean;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    group: SalesTeamGroup;

    salesTeamsUsers: SalesTeamsUser[];
}
