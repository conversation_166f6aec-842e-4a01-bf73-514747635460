import { Field, ObjectType } from 'type-graphql';
import { IVisitAction } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitAction.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IVisitAction })
export class VisitAction implements IVisitAction {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
