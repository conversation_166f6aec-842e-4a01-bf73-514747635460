import { Field, ObjectType } from 'type-graphql';
import {
    EnumLastGmpBpmStatus,
    ICustomer,
    EnumCustomerClassification,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customer.model';
import { CustomersContactPerson } from './customersContactPerson.impl';
import { CustomerType } from './customerType.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { CustomersProperty } from './customersProperty.impl';
import { CustomersPrimaryUser } from './customersPrimaryUser.impl';
import { CustomersUser } from './customersUser.impl';

@ObjectType({ implements: ICustomer })
export class Customer implements ICustomer {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    groupId: number;
    typeId: number;
    areaId: number;
    businessCode: string;
    medicalCode: string;
    code: string;
    referenceCode: string;
    name: string;
    phone: string;
    mobile: string;
    email: string;
    fax: string;
    cityId: number;
    districtId: number;
    address: string;
    website: string;
    memo: string;
    createdUserId: number;
    updatedUserId: number;
    syncSourceId: number;
    syncCode: string;
    isGmp: boolean;
    parentId: number;
    hasEffectiveGmpCertificate: boolean;
    creditQuota: string;
    shippingAddress: string;
    contactPersonName: string;
    contactPersonPhone: string;
    bankAccountCode: string;
    bankAccountName: string;
    legalPersonName: string;
    billingUnitName: string;
    provinceId: number;
    lastGmpBpmInstanceId: string;
    lastGmpBpmStatus: EnumLastGmpBpmStatus;
    lastGmpBpmUserId: number;
    creditPeriodId: number;
    shortName: string;
    defaultPaymentMethodId: number;
    defaultShippingMethodId: number;
    yonyouCode: string;
    do1Code: string;
    navCode: string;
    isActive: boolean;
    categoryId: number;
    classification: EnumCustomerClassification;

    // relations
    @Field((type) => CustomerType, { nullable: true })
    type: CustomerType;

    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;

    customersContactPeople: CustomersContactPerson[];
    customersProperties: CustomersProperty[];
    customersUsers: CustomersUser[];
    customersPrimaryUsers: CustomersPrimaryUser[];
}
