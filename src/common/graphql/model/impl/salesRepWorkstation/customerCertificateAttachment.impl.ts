import { ObjectType } from 'type-graphql';
import { ICustomerCertificateAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerCertificateAttachment.model';

@ObjectType({ implements: ICustomerCertificateAttachment })
export class CustomerCertificateAttachment
    implements ICustomerCertificateAttachment
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    certificateId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
    createdUserId: number;
    updatedUserId: number;
}
