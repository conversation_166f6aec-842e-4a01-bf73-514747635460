import { ObjectType } from 'type-graphql';
import { IWeeklyWorkReportsToNextWeekVisit } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/weeklyWorkReportsToNextWeekVisit.model';

@ObjectType({ implements: IWeeklyWorkReportsToNextWeekVisit })
export class WeeklyWorkReportsToNextWeekVisit
    implements IWeeklyWorkReportsToNextWeekVisit
{
    weeklyWorkReportId: number;
    nextWeekVisitId: number;
}
