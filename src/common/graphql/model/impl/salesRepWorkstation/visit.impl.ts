import { Field, ObjectType } from 'type-graphql';
import {
    EnumVisitStatus,
    IVisit,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visit.model';
import { VisitsGoal } from '@clinico/typeorm-persistence/models/salesRepWorkstation/visitsGoal.model';
import { VisitMemo } from './visitMemo.impl';
import { VisitsProperty } from './visitsProperty.impl';
import { VisitsTimePeriod } from './visitsTimePeriod.impl';
import { VisitsToMentionProduct } from './visitsToMentionProduct.impl';
import { VisitsToRentProduct } from './visitsToRentProduct.impl';
import { VisitsPrimaryContactPerson } from './visitsPrimaryContactPerson.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IVisit })
export class Visit implements IVisit {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    title: string;
    content: string;
    visitAddress: string;
    visitDate: Date;
    priorityOrder: number;
    status: EnumVisitStatus;
    typeId: number;
    businessId: number;
    actionId: number;
    actionContent: string;
    customerId: number;
    salesTeamId: number;
    salesTeamUnitId: number;
    primaryUserId: number;
    hasAssistedVisitSupervisor: boolean;
    assistedVisitSupervisorId: number;
    createdUserId: number;
    updatedUserId: number;
    visitPlaceId: number;
    visitResult: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;

    visitMemos?: VisitMemo[];

    // Many to many
    visitsGoals: VisitsGoal[];
    visitsPrimaryContactPeople: VisitsPrimaryContactPerson[];
    visitsProperties: VisitsProperty[];
    visitsTimePeriods: VisitsTimePeriod[];
    visitsToMentionProducts: VisitsToMentionProduct[];
    visitsToRentProducts: VisitsToRentProduct[];
}
