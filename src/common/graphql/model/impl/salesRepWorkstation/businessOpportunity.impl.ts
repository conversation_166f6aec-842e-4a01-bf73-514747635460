import { Field, ObjectType } from 'type-graphql';
import { IBusinessOpportunity } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessOpportunity.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IBusinessOpportunity })
export class BusinessOpportunity implements IBusinessOpportunity {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: string;
    enabled: boolean;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
