import { Field, ObjectType } from 'type-graphql';
import {
    EnumCustomerTypeCode,
    ICustomerType,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerType.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerType })
export class CustomerType implements ICustomerType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: EnumCustomerTypeCode;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
