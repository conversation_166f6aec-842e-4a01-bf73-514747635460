import { Field, ObjectType } from 'type-graphql';
import { IVisitAttachFile } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitAttachFile.model';

@ObjectType({ implements: IVisitAttachFile })
export class VisitAttachFile implements IVisitAttachFile {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    visitId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
