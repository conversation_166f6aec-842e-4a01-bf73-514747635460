import { ObjectType } from 'type-graphql';
import { IWeeklyWorkReportsToThisWeekVisit } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/weeklyWorkReportsToThisWeekVisit.model';

@ObjectType({ implements: IWeeklyWorkReportsToThisWeekVisit })
export class WeeklyWorkReportsToThisWeekVisit
    implements IWeeklyWorkReportsToThisWeekVisit
{
    weeklyWorkReportId: number;
    thisWeekVisitId: number;
}
