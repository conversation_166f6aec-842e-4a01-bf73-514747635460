import { Field, ObjectType } from 'type-graphql';
import { IVisitType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitType.model';
import { Visit } from './visit.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { EnumVisitTypeCode } from '@clinico/typeorm-persistence/models/salesRepWorkstation/visitType.model';

@ObjectType({ implements: IVisitType })
export class VisitType implements IVisitType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: EnumVisitTypeCode;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;

    visitsByType: Visit[];
}
