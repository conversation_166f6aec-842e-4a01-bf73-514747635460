import { Field, ObjectType } from 'type-graphql';
import { ICustomerPropertyType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerPropertyType.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerPropertyType })
export class CustomerPropertyType implements ICustomerPropertyType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
