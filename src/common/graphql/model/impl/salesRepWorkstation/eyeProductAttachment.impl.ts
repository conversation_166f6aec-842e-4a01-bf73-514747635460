import { ObjectType } from 'type-graphql';
import { IEyeProductAttachment } from '@clinico/type-graphql-persistence/models/public/eyeProductAttachment.model';

@ObjectType({ implements: IEyeProductAttachment })
export class EyeProductAttachment implements IEyeProductAttachment {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
    eyeProductId: number;
    createdUserId: number;
    updatedUserId: number;
}
