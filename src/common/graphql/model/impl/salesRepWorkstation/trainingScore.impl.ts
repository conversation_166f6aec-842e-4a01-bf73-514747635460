import { ObjectType } from 'type-graphql';
import { ITrainingScore } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/trainingScore.model';

@ObjectType({ implements: ITrainingScore })
export class TrainingScore implements ITrainingScore {
    trainingInstitution: string;
    trainingType: string;
    courseCode: string;
    courseName: string;
    instructor: string;
    productCategory: string;
    productRegulationTraining: string;
    courseStartDate: string;
    courseEndDate: string;
    employeeScore: string;
    hasCertificate: string;
    certificateNumber: string;
    trainingHours: number;
    trainingCostNT: number;
    id: number;
    code: string;
    createdAt: string;
    updatedAt: string;
    startingDate: string;
    userCode: string;
    present: string;
    exam: string;
    examCnt: number;
    scoreMax: string;
    scoreAvg: string;
    datetimeFirst: string;
    datetimeHigh: string;
    datetimeLast: string;
    bestDays: number;
    bestMins: number;
    pass: string;
}
