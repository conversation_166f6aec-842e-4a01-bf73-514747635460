import { Field, ObjectType } from 'type-graphql';
import { ICustomerCertificateType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerCertificateType.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerCertificateType })
export class CustomerCertificateType implements ICustomerCertificateType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
