import { ObjectType } from 'type-graphql';
import { IDistributorAuthorizationAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/distributorAuthorizationAttachment.model';

@ObjectType({ implements: IDistributorAuthorizationAttachment })
export class DistributorAuthorizationAttachment
    implements IDistributorAuthorizationAttachment
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
    distributorAuthorizationId: number;
    createdUserId: number;
    updatedUserId: number;
}
