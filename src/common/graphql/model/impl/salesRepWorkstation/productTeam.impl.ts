import { ObjectType } from 'type-graphql';
import { IProductTeam } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/productTeam.model';
import { ProductTeamsUser } from './productTeamsUser.impl';
import { ProductTeamsBusinessProduct } from './productTeamsBusinessProduct.impl';

@ObjectType({ implements: IProductTeam })
export class ProductTeam implements IProductTeam {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    code: string;

    // relations
    productTeamsUsers: ProductTeamsUser[];
    productTeamsBusinessProducts: ProductTeamsBusinessProduct[];
}
