import { ICustomerEquipmentAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerEquipmentAttachment.model';
import { ObjectType } from 'type-graphql';

@ObjectType({ implements: ICustomerEquipmentAttachment })
export class CustomerEquipmentAttachment
    implements ICustomerEquipmentAttachment
{
    id: number;
    equipmentId: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    extension: string;
    s3Key: string;
    createdUserId: number;
    updatedUserId: number;
}
