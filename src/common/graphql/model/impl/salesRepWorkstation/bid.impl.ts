import { ObjectType, registerEnumType } from 'type-graphql';
import { DateResolver } from 'graphql-scalars';
import { BidsEquipment } from './bidsEquipment.impl';
import { IBid } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/bid.model';
import { BidAttachment } from './bidAttachment.impl';

export enum EnumBidResult {
    InProgress = 'InProgress', // 進行中
    Abandoned = 'Abandoned', // 棄標
    Pass = 'Pass', // 流標
    Acceptance = 'Acceptance', // 已得標
    Canceled = 'Canceled', // 已取消
}

registerEnumType(EnumBidResult, {
    name: 'EnumBidResult',
    valuesConfig: {
        InProgress: { description: '進行中' },
        Abandoned: { description: '棄標' },
        Pass: { description: '流標' },
        Acceptance: { description: '已得標' },
        Canceled: { description: '已取消' },
    },
});

@ObjectType({ implements: IBid })
export class Bid implements IBid {
    id: number;
    customerId: number;
    code: string;
    name: string;
    bidDate: Date;
    publishDate: Date;
    registrationDate: Date;
    publicLinkUrl: string;
    content: string;
    result: EnumBidResult;
    notes: string;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    // relations
    bidsEquipment: BidsEquipment[];
    bidAttachments: BidAttachment[];
}
