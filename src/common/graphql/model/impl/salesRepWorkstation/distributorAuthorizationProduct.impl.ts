import { ObjectType } from 'type-graphql';
import { IDistributorAuthorizationProduct } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/distributorAuthorizationProduct.model';

@ObjectType({ implements: IDistributorAuthorizationProduct })
export class DistributorAuthorizationProduct
    implements IDistributorAuthorizationProduct
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    distributorAuthorizationId: number;
    authorizationProductId: number;
}
