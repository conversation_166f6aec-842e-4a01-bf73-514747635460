import { Field, ObjectType } from 'type-graphql';
import {
    EnumContactPeopleGender,
    IContactPerson,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/contactPerson.model';
import { ContactPeopleUser } from './contactPeopleUser.impl';
import { ContactPeoplePrimaryUser } from './contactPeoplePrimaryUser.impl';
import { Customer } from './customer.impl';
import { CustomersContactPerson } from './customersContactPerson.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { ContactPeopleType } from './contactPeopleType.impl';
import { ContactPeopleAttachment } from './contactPeopleAttachment.impl';

@ObjectType({ implements: IContactPerson })
export class Contact<PERSON>erson implements IContactPerson {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    name: string;
    nickname: string;
    gender: EnumContactPeopleGender;
    jobTitle: string;
    doctorCode: string;
    dept: string;
    phone: string;
    mobile: string;
    email: string;
    fax: string;
    address: string;
    website: string;
    hobby: string;
    skill: string;
    memo: string;
    createdUserId: number;
    updatedUserId: number;
    syncSourceId: number;
    syncCode: string;
    referenceCode: string;
    code: string;
    citizenCode: string;
    departmentId: number;
    bankBranch: string;
    bankCardNumber: string;
    typeId: number;
    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;

    contactPeopleUsers: ContactPeopleUser[];
    contactPeoplePrimaryUsers: ContactPeoplePrimaryUser[];
    customersContactPeople: CustomersContactPerson[];

    type: ContactPeopleType;
    contactPeopleAttachments: ContactPeopleAttachment[];
}
