import { Field, ObjectType } from 'type-graphql';
import { IBusinessType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessType.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IBusinessType })
export class BusinessType implements IBusinessType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
