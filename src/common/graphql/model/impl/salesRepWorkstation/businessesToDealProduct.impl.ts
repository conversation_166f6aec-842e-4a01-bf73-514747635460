import { Field, ObjectType } from 'type-graphql';
import { IBusinessesToDealProduct } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessesToDealProduct.model';
import { BusinessProduct } from './businessProduct.impl';

@ObjectType({ implements: IBusinessesToDealProduct })
export class BusinessesToDealProduct implements IBusinessesToDealProduct {
    id: number;
    businessId: number;
    dealProductId: number;
    qty: number;

    @Field((type) => BusinessProduct, { nullable: true })
    dealProduct: BusinessProduct;
}
