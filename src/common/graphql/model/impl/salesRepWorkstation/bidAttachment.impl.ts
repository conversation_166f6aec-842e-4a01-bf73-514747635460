import { IBidAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/bidAttachment.model';
import { ObjectType } from 'type-graphql';

@ObjectType({ implements: IBidAttachment })
export class BidAttachment implements IBidAttachment {
    id: number;
    bidId: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    extension: string;
    s3Key: string;
    createdUserId: number;
    updatedUserId: number;
}
