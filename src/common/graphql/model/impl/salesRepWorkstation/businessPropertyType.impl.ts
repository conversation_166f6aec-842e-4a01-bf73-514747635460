import { Field, ObjectType } from 'type-graphql';
import { IBusinessPropertyType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessPropertyType.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { BusinessProperty } from './businessProperty.impl';

@ObjectType({ implements: IBusinessPropertyType })
export class BusinessPropertyType implements IBusinessPropertyType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;

    businessPropertiesByType: BusinessProperty[];
}
