import { Field, ObjectType } from 'type-graphql';
import { ITenderItem } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/tenderItem.model';

@ObjectType({ implements: ITenderItem })
export class TenderItem implements ITenderItem {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    tenderId: number;
    standardTextId: string;
    bidMatterName: string;
    bidMatterBrand: string;
    bidMatterModel: string;
    quantity: number;
    unitPrice: string;
    totalPrice: string;
}
