import { ObjectType } from 'type-graphql';
import { IVisitMemo } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitMemo.model';

@ObjectType({ implements: IVisitMemo })
export class VisitMemo implements IVisitMemo {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    content: string;
    visitId: number;
    createdUserId: number;
    updatedUserId: number;
}
