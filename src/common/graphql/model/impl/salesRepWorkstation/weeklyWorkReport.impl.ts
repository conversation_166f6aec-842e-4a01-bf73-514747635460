import { ObjectType, registerEnumType } from 'type-graphql';
import { IWeeklyWorkReport } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/weeklyWorkReport.model';
import { WeeklyWorkReportsToNextWeekVisit } from './weeklyWorkReportsToNextWeekVisit.impl';
import { WeeklyWorkReportsToThisWeekVisit } from './weeklyWorkReportsToThisWeekVisit.impl';
import { WeeklyWorkReportAttachment } from './weeklyWorkReportAttachment.impl';
export enum EnumState {
    WaitToRead = 'WaitToRead',
    AlreadyRead = 'AlreadyRead',
    Deleted = 'Deleted',
    Rejected = 'Rejected',
}

export enum EnumFillState {
    NotYet = 'NotYet',
    Already = 'Already',
}

registerEnumType(EnumState, {
    name: 'EnumState',
    valuesConfig: {
        WaitToRead: { description: 'WaitToRead' },
        AlreadyRead: { description: 'AlreadyRead' },
        Deleted: { description: 'Deleted' },
        Rejected: { description: 'Rejected' },
    },
});

registerEnumType(EnumFillState, {
    name: 'EnumFillState',
    valuesConfig: {
        NotYet: { description: 'NotYet' },
        Already: { description: 'Already' },
    },
});
@ObjectType({ implements: IWeeklyWorkReport })
export class WeeklyWorkReport implements IWeeklyWorkReport {
    salesUserId: number;
    id: number;
    salesTeamId: number;
    salesTeamUnitId: number;
    weeklyRecordsId: number;
    fillState: EnumFillState;
    state: EnumState;
    notes: string;
    rejectedReason: string;
    thisWeekWorkDescription: string;
    nextWeekWorkDescription: string;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    // relations
    weeklyWorkReportsToThisWeekVisits: WeeklyWorkReportsToThisWeekVisit[];
    weeklyWorkReportsToNextWeekVisits: WeeklyWorkReportsToNextWeekVisit[];
    weeklyWorkReportAttachments: WeeklyWorkReportAttachment[];
}
