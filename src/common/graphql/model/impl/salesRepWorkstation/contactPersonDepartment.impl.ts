import { Field, ObjectType } from 'type-graphql';
import { IContactPersonDepartment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/contactPersonDepartment.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IContactPersonDepartment })
export class ContactPersonDepartment implements IContactPersonDepartment {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
