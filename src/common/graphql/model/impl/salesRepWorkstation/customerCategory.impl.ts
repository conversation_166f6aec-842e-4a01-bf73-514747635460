import { Field, ObjectType } from 'type-graphql';
import { ICustomerCategory } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerCategory.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerCategory })
export class CustomerCategory implements ICustomerCategory {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
