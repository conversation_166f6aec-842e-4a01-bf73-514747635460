import { Field, ObjectType } from 'type-graphql';
import { ICustomerGroup } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerGroup.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerGroup })
export class CustomerGroup implements ICustomerGroup {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    name: string;
    createdUserId: number;
    updatedUserId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
