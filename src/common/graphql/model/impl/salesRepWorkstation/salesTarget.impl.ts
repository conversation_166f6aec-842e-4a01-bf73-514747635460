import { Field, ObjectType } from 'type-graphql';
import { ISalesTarget } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/salesTarget.model';

@ObjectType({ implements: ISalesTarget })
export class SalesTarget implements ISalesTarget {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    month: string;
    amount: string;
    qty: number;
    salesTeamUnitId: number;
    materialCode: string;
    key: string;
    eyeProductId: number;
    salesTargetYearId: number;
    eyeProductGroupId: number;
}
