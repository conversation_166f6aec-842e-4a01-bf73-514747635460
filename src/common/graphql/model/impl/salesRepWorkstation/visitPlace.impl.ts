import { ObjectType } from 'type-graphql';
import { IVisitPlace } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitPlace.model';

@ObjectType({ implements: IVisitPlace })
export class VisitPlace implements IVisitPlace {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    companyId: number;
    name: string;
    parentId: number;
    viewOrder: number;
}
