import { Field, ObjectType } from 'type-graphql';
import { ICompetitor } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/competitor.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { CompetitorsBusinessProduct } from './competitorsBusinessProduct.impl';

@ObjectType({ implements: ICompetitor })
export class Competitor implements ICompetitor {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    name: string;
    advantage: string;
    disadvantage: string;
    strategy: string;
    memo: string;
    createdUserId: number;
    updatedUserId: number;
    brand: string;
    model: string;
    agents: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
