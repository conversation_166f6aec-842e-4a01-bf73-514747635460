import { Field, ObjectType } from 'type-graphql';
import { IVisitGoal } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitGoal.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IVisitGoal })
export class VisitGoal implements IVisitGoal {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
