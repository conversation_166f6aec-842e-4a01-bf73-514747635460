import { Field, ObjectType } from 'type-graphql';
import { IBusinessProperty } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessProperty.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IBusinessProperty })
export class BusinessProperty implements IBusinessProperty {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    typeId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
