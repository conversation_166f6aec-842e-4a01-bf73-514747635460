import { ObjectType } from 'type-graphql';
import { ICustomerAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerAttachment.model';

@ObjectType({ implements: ICustomerAttachment })
export class CustomerAttachment implements ICustomerAttachment {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
    customerId: number;
    createdUserId: number;
    updatedUserId: number;
}
