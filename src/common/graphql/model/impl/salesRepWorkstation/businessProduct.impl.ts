import { Field, ObjectType } from 'type-graphql';
import { IBusinessProduct } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessProduct.model';
import { BusinessProductType } from './businessProductType.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { SalesTeamsBusinessProduct } from './salesTeamsBusinessProduct.impl';
import { BusinessProductsMaterial } from './businessProductsMaterial.impl';
import { ProductTeamsBusinessProduct } from './productTeamsBusinessProduct.impl';
import { BusinessProductsToProductLine } from './businessProductsToProductLine.impl';

@ObjectType({ implements: IBusinessProduct })
export class BusinessProduct implements IBusinessProduct {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    typeId: number;
    brand: string;
    isActive: boolean;
    productLineId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;

    @Field((type) => BusinessProductType, { nullable: true })
    type?: BusinessProductType;

    salesTeamsBusinessProducts: SalesTeamsBusinessProduct[];
    businessProductsMaterials: BusinessProductsMaterial[];
    productTeamsBusinessProducts: ProductTeamsBusinessProduct[];
    businessProductsToProductLines: BusinessProductsToProductLine[];
}
