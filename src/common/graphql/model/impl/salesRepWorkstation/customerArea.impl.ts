import { Field, ObjectType } from 'type-graphql';
import { ICustomerArea } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerArea.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerArea })
export class CustomerArea implements ICustomerArea {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
