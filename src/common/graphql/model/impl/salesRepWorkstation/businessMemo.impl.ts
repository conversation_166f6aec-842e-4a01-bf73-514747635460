import { Field, ObjectType } from 'type-graphql';
import { IBusinessMemo } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessMemo.model';
import { User } from '../public/user.impl';
import { BusinessesPrimaryContactPerson } from './businessesPrimaryContactPerson.impl';

@ObjectType({ implements: IBusinessMemo })
export class BusinessMemo implements IBusinessMemo {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    content: string;
    businessId: number;
    createdUserId: number;
    updatedUserId: number;
}
