import { ObjectType } from 'type-graphql';
import { ISalesTeamUnit } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/salesTeamUnit.model';

@ObjectType({ implements: ISalesTeamUnit })
export class SalesTeamUnit implements ISalesTeamUnit {
    id: number;
    salesTeamId: number;
    name: string;
    userId: number;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamUnitPostId: number;
    entryDate: Date;
    reportUserId: number;
    parentId: number;
    productLineId: number;
    unitRole: string;
    costCenterId: number;
    onDutyDate: Date;
}
