import { Field, ObjectType } from 'type-graphql';
import {
    EnumBusinessSalesMethodCode,
    IBusinessSalesMethod,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessSalesMethod.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IBusinessSalesMethod })
export class BusinessSalesMethod implements IBusinessSalesMethod {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: EnumBusinessSalesMethodCode;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
