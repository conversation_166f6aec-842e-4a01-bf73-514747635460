import { ObjectType } from 'type-graphql';
import {
    EnumDistributorAuthorizationApprovalStatus,
    IDistributorAuthorization,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/distributorAuthorization.model';

@ObjectType({ implements: IDistributorAuthorization })
export class DistributorAuthorization implements IDistributorAuthorization {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    companyId: number;
    customerId: number;
    projectName: string;
    projectCode: string;
    planCode: string;
    memo: string;
    startDate: Date;
    endDate: Date;
    bpmInstanceId: string;
    approvalStatus: EnumDistributorAuthorizationApprovalStatus;
    dealerId: number;
}
