import { Field, ObjectType } from 'type-graphql';
import { IVisitProperty } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitProperty.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IVisitProperty })
export class VisitProperty implements IVisitProperty {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    typeId: number;
    parentId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
