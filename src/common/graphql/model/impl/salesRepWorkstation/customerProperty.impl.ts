import { Field, ObjectType } from 'type-graphql';
import { ICustomerProperty } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerProperty.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: ICustomerProperty })
export class CustomerProperty implements ICustomerProperty {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    typeId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
