import { Field, ObjectType } from 'type-graphql';
import {
    EnumBusinessStatusType,
    IBusinessStatus,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessStatus.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IBusinessStatus })
export class BusinessStatus implements IBusinessStatus {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    type: EnumBusinessStatusType;
    buyingOpportunity: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
