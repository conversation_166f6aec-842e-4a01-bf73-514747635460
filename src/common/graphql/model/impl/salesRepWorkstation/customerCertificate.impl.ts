import { ObjectType } from 'type-graphql';
import { ICustomerCertificate } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerCertificate.model';
import { CustomerCertificateAttachment } from './customerCertificateAttachment.impl';

@ObjectType({ implements: ICustomerCertificate })
export class CustomerCertificate implements ICustomerCertificate {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    customerId: number;
    typeId: number;
    effectiveDate: Date;
    expiryDate: Date;
    disabled: boolean;
    createdUserId: number;
    updatedUserId: number;
    code: string;
    scope: string;

    // relations
    customerCertificateAttachmentsByCertificate: CustomerCertificateAttachment[];
}
