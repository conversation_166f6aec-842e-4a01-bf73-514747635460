import { ObjectType } from 'type-graphql';
import { IContactPeopleAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/contactPeopleAttachment.model';
import { ContactPerson } from './contactPerson.impl';

@ObjectType({ implements: IContactPeopleAttachment })
export class ContactPeopleAttachment {
    id: number;
    contactPeopleId: number;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
    name: string;
    s3Key: string;
    createdUserId: number;
    updatedUserId: number;
    extension: string;
    contactPerson: ContactPerson;
}
