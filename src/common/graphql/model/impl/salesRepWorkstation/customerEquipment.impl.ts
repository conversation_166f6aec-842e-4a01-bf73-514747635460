import { ObjectType } from 'type-graphql';
import { ICustomerEquipment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customerEquipment.model';
import { CustomerEquipmentAttachment } from './customerEquipmentAttachment.impl';
import { Competitor } from './competitor.impl';

@ObjectType({ implements: ICustomerEquipment })
export class CustomerEquipment implements ICustomerEquipment {
    id: number;
    customerId: number;
    competitorId: number;
    manufactureYear: number;
    competitorPrice: number;
    notes: string;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;

    // relations
    customerEquipmentAttachmentsByEquipment: CustomerEquipmentAttachment[];
    competitor: Competitor;
}
