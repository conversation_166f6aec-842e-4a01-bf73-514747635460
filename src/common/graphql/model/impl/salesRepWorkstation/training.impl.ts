import { ObjectType } from 'type-graphql';
import { ITraining } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/training.model';

@ObjectType({ implements: ITraining })
export class Training implements ITraining {
    id: number;
    createdAt: string;
    updatedAt: string;
    empCode: string;
    empName: string;
    trainingOrg: string;
    course: string;
    startDate: Date;
    endDate: Date;
    score: string;
    trainingType: string;
    teacherName: string;
    productType: string;
    isProductTraining: string;
    isCertified: boolean;
    certificationNo: string;
    trainingHours: string;
    trainingFee: string;
}
