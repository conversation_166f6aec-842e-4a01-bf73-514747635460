import { ObjectType } from 'type-graphql';
import { ICustomersConsumablePrice } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customersConsumablePrice.model';

@ObjectType({ implements: ICustomersConsumablePrice })
export class CustomersConsumablePrice implements ICustomersConsumablePrice {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    customerId: number;
    invoicingCustomerId: number;
    materialId: number;
    cleanPrice: string;
    consumablePriceTypeId: number;
    discountPolicyId: number;
    remark1: string;
    remark2: string;
    remark3: string;
    effectiveStartDate: Date;
    effectiveEndDate: Date;
    deleted: boolean;
}
