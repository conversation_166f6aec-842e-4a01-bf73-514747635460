import { Field, ObjectType } from 'type-graphql';
import { ITender } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/tender.model';

@ObjectType({ implements: ITender })
export class Tender implements ITender {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    standardTextId: string;
    keyword: string;
    provinceId: number;
    cityId: number;
    districtId: number;
    releaseTime: string;
    projectCode: string;
    bidCustomerId: number;
    bidBudget: string;
    bidContactPerson: string;
    bidContactPhone: string;
    winBidCustomer: string;
    winBidBudget: string;
    winBidContactPerson: string;
    winBidContactPhone: string;
    agency: string;
    proxyContactPerson: string;
    proxyContactPhone: string;
    informationType: string;
    bidMethod: string;
    bidAcquisitionTime: string;
    bidDeadline: string;
    bidStartTime: string;
    bidEndTime: string;
    bidOpeningTime: string;
    bidEvaluationExpert: string;
    fundSource: string;
    isElectronicBid: boolean;
    secondaryInformationType: string;
    bidCustomerName: string;
    messageHeader: string;
}
