import { ObjectType } from 'type-graphql';
import {
    EnumSalesTeamGroupCode,
    ISalesTeamGroup,
} from '@clinico/type-graphql-persistence/models/salesRepWorkstation/salesTeamGroup.model';

@ObjectType({ implements: ISalesTeamGroup })
export class SalesTeamGroup implements ISalesTeamGroup {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    code: EnumSalesTeamGroupCode;
    directorId: number;
    regionId: number;
}
