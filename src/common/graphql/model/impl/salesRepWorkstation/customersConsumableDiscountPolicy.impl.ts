import { ObjectType } from 'type-graphql';
import { ICustomersConsumableDiscountPolicy } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/customersConsumableDiscountPolicy.model';

@ObjectType({ implements: ICustomersConsumableDiscountPolicy })
export class CustomersConsumableDiscountPolicy
    implements ICustomersConsumableDiscountPolicy
{
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    code: string;
}
