import { Field, ObjectType } from 'type-graphql';
import { IVisitPropertyType } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitPropertyType.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { VisitProperty } from './visitProperty.impl';

@ObjectType({ implements: IVisitPropertyType })
export class VisitPropertyType implements IVisitPropertyType {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    code: string;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
