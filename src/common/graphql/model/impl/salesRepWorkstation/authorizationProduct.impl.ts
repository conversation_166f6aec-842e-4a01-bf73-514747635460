import { ObjectType } from 'type-graphql';
import { IAuthorizationProduct } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/authorizationProduct.model';

@ObjectType({ implements: IAuthorizationProduct })
export class AuthorizationProduct implements IAuthorizationProduct {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    code: string;
    registrant: string;
    agent: string;
    model: string;
}
