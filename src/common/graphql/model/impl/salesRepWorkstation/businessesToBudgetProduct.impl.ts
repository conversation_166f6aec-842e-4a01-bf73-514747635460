import { Field, ObjectType } from 'type-graphql';
import { IBusinessesToBudgetProduct } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/businessesToBudgetProduct.model';
import { BusinessProduct } from './businessProduct.impl';

@ObjectType({ implements: IBusinessesToBudgetProduct })
export class BusinessesToBudgetProduct implements IBusinessesToBudgetProduct {
    id: number;
    businessId: number;
    budgetProductId: number;
    qty: number;

    @Field((type) => BusinessProduct, { nullable: true })
    budgetProduct: BusinessProduct;
}
