import { Field, ObjectType } from 'type-graphql';
import { IBusiness } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/business.model';
import { BusinessesToDealProduct } from './businessesToDealProduct.impl';
import { BusinessesToBudgetProduct } from './businessesToBudgetProduct.impl';
import { BusinessesPrimaryContactPerson } from './businessesPrimaryContactPerson.impl';
import { BusinessesProperty } from './businessesProperty.impl';
import { BusinessesCompetitor } from './businessesCompetitor.impl';
import { SalesTeamGroup } from './salesTeamGroup.impl';
import { BusinessesUser } from './businessesUser.impl';
import { BusinessesLosingReason } from './businessesLosingReason.impl';

@ObjectType({ implements: IBusiness })
export class Business implements IBusiness {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;
    typeId: number;
    title: string;
    content: string;
    orderCode: string;
    eyeQuotationOrderCode: string;
    budgetAmount: string;
    dealAmount: string;
    expectedClosedDate: Date;
    closedDate: Date;
    winningOpportunityId: number;
    buyingOpportunityId: number;
    losingReason: string;
    losingImprovementPlan: string;
    statusId: number;
    customerId: number;
    salesTeamId: number;
    salesTeamUnitId: number;
    primaryUserId: number;
    createdUserId: number;
    updatedUserId: number;
    saleAmount: string;
    code: string;
    salesMethodId: number;
    dealerId: number;
    customerMemo: string;

    // relations
    @Field((type) => SalesTeamGroup, {
        nullable: true,
        description: '業務團隊組織',
    })
    salesTeamGroup: SalesTeamGroup;

    @Field((type) => [BusinessesToDealProduct], {
        description: '商機與成交商品關聯',
    })
    businessesToDealProducts: BusinessesToDealProduct[];

    @Field((type) => [BusinessesToBudgetProduct], {
        description: '商機與預算商品關聯',
    })
    businessesToBudgetProducts: BusinessesToBudgetProduct[];

    @Field((type) => [BusinessesUser], {
        description: '商機與負責（協助）業務關聯',
    })
    businessesUsers: BusinessesUser[];

    // 主要聯絡人
    businessesPrimaryContactPeople: BusinessesPrimaryContactPerson[];

    // 商機屬性
    businessesProperties: BusinessesProperty[];

    // 競爭對手
    businessesCompetitors: BusinessesCompetitor[];

    // 丟單原因
    businessesLosingReasons: BusinessesLosingReason[];
}
