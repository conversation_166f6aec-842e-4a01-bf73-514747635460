import { ObjectType } from 'type-graphql';
import { IVisitCheckIn } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitCheckIn.model';

@ObjectType({ implements: IVisitCheckIn })
export class VisitCheckIn implements IVisitCheckIn {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    visitId: number;
    lat: number;
    lng: number;
    content: string;
}
