import { ObjectType } from 'type-graphql';
import { IPerformanceForecast } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/performanceForecast.model';

@ObjectType({ implements: IPerformanceForecast })
export class PerformanceForecast implements IPerformanceForecast {
    id: number;
    updatedAt: Date;
    updatedUserId: number;
    year: number;
    month: number;
    amountEarlyMonth: string;
    amountMidMonth: string;
    amountLateMonth: string;
    salesTeamUnitId: number;
    performanceForecastYearId: number;
}
