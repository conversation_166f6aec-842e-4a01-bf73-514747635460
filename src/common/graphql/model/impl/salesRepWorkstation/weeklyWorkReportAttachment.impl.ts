import { IWeeklyWorkReportAttachment } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/weeklyWorkReportAttachment.model';
import { ObjectType } from 'type-graphql';

@ObjectType({ implements: IWeeklyWorkReportAttachment })
export class WeeklyWorkReportAttachment implements IWeeklyWorkReportAttachment {
    id: number;
    weeklyWorkReportId: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    extension: string;
    s3Key: string;
    createdUserId: number;
    updatedUserId: number;
}
