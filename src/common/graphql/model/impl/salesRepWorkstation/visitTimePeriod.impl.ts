import { Field, ObjectType } from 'type-graphql';
import { IVisitTimePeriod } from '@clinico/type-graphql-persistence/models/salesRepWorkstation/visitTimePeriod.model';
import { SalesTeamGroup } from './salesTeamGroup.impl';

@ObjectType({ implements: IVisitTimePeriod })
export class VisitTimePeriod implements IVisitTimePeriod {
    id: number;
    name: string;
    viewOrder: number;
    createdAt: Date;
    updatedAt: Date;
    salesTeamGroupId: number;

    // relations
    @Field((type) => SalesTeamGroup, { nullable: true })
    salesTeamGroup: SalesTeamGroup;
}
