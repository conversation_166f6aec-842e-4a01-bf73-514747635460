import { ObjectType } from 'type-graphql';
import { IRole } from '@clinico/type-graphql-persistence/models/permission/role.model';
import { RolesPermission } from './rolesPermission.impl';

@ObjectType({ implements: IRole })
export class Role implements IRole {
    id: number;
    name: string;
    code: string;
    isSystemAdmin: boolean;
    isAllowAllRegions: boolean;
    allowRegionIds: number[];
    isAllowAllTwCompanies: boolean;
    isAllowAllCnCompanies: boolean;
    allowCompanyIds: number[];
    isAllowAllSalesTeamGroups: boolean;
    allowSalesTeamGroupIds: number[];
    isAllowAllTwnEcAndMrSalesTeam: boolean;
    isAllowAllTwnClSalesTeam: boolean;
    isAllowAllTwnEyeSalesTeam: boolean;
    isAllowAllChnEyeSalesTeam: boolean;
    allowSalesTeamIds: number[];
    isOnlyOwnUser: boolean;
    allowUserIds: number[];
    allowProductTeamIds: number[];
    allowEyeFixedAssetsServiceProvidersIds: number[];

    // relations
    rolesPermissions: RolesPermission[];
}
