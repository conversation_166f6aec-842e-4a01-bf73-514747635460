import { ObjectType } from 'type-graphql';
import { IRepairDocument } from '@clinico/type-graphql-persistence/models/public/repairDocument.model';

@ObjectType({ implements: IRepairDocument })
export class RepairDocument implements IRepairDocument {
    id: number;
    repairOrderCode: string;
    s3Key: string;
    name: string;
    description: string;
    extension: string;
    extendTable: string;
    extendId: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
