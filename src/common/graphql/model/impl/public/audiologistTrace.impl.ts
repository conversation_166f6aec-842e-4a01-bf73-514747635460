import { ObjectType } from 'type-graphql';
import { IAudiologistTrace } from '@clinico/type-graphql-persistence/models/public/audiologistTrace.model';

@ObjectType({ implements: IAudiologistTrace })
export class AudiologistTrace implements IAudiologistTrace {
    id: number;
    departmentId: number;
    audiologistId: number;
    memberId: number;
    memberCode: string;
    seq: number;
    qty: number;
    isClosed: string;
    type: string;
    contactRecord: string;
    closedRecord: string;
    traceResultId: number;
    delayReasonId: number;
    contactPeopleId: number;
    time1: string;
    time2: string;
    traceDate: Date;
    shouldTraceDate: Date;
    delayTraceDate: Date;
    closedDate: string;
    validCode: string;
    sn: string;
    createdUserId: number;
    updatedUserId: number;
    closedUserId: number;
    orderId: string;
    orderSeq: number;
    shipId: string;
    shipSeq: number;
    itemId: string;
    guaranteeStartDate: Date;
    guaranteeEndDate: Date;
    createdAt: Date;
    updatedAt: Date;
    contactRemark: string;
    isLoseContact: boolean;
    isActive: boolean;
    traceableDate1: Date;
    traceableDate2: Date;
    closedDatetime: Date;
    dailyMeanHours: number;
    regionId: number;
    materialId: number;
}
