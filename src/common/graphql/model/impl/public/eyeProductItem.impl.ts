import { ObjectType } from 'type-graphql';
import { IEyeProductItem } from '@clinico/type-graphql-persistence/models/public/eyeProductItem.model';

@ObjectType({ implements: IEyeProductItem })
export class EyeProductItem implements IEyeProductItem {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeProductId: number;
    eyeProductItemTypeId: number;
    materialId: number;
    materialName: string;
    qty: number;
    materialSellingUnit: string;
    isOptional: boolean;
    description: string;
    viewOrder: number;
}
