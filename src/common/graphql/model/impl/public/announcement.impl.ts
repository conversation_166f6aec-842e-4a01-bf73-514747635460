import { ObjectType } from 'type-graphql';
import { IAnnouncement } from '@clinico/type-graphql-persistence/models/public/announcement.model';

@ObjectType({ implements: IAnnouncement })
export class Announcement implements IAnnouncement {
    id: number;
    announcementTypeId: number;
    title: string;
    content: string;
    startDate: Date;
    endDate: Date;
    status: number;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
