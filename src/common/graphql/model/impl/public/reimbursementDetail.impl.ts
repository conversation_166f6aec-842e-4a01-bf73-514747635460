import { ObjectType } from 'type-graphql';
import { IReimbursementDetail } from '@clinico/type-graphql-persistence/models/public/reimbursementDetail.model';

@ObjectType({ implements: IReimbursementDetail })
export class ReimbursementDetail implements IReimbursementDetail {
    id: number;
    reimbursementId: number;
    accountItemId: number;
    taxTypeId: number;
    taxItemId: number;
    price: number;
    invoiceId: string;
    invoiceDate: Date;
    companyTaxId: string;
    summary: string;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
