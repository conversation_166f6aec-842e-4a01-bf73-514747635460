import { ObjectType } from 'type-graphql';
import { IAppointmentItem } from '@clinico/type-graphql-persistence/models/public/appointmentItem.model';

@ObjectType({ implements: IAppointmentItem })
export class AppointmentItem implements IAppointmentItem {
    id: number;
    code: string;
    name: string;
    serviceHours: number;
    isShowInOfficial: boolean;
    viewOrder: number;
    regionId: number;
}
