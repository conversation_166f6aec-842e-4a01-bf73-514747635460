import { ObjectType } from 'type-graphql';
import { IShippingMethod } from '@clinico/type-graphql-persistence/models/public/shippingMethod.model';

@ObjectType({ implements: IShippingMethod })
export class ShippingMethod implements IShippingMethod {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    regionId: number;
    companyId: number;
    code: string;
    name: string;
    viewOrder: number;
}
