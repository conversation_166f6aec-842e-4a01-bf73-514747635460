import { ObjectType } from 'type-graphql';
import { IModifyLog } from '@clinico/type-graphql-persistence/models/public/modifyLog.model';

@ObjectType({ implements: IModifyLog })
export class ModifyLog implements IModifyLog {
    id: number;
    model: string;
    modelName: string;
    dataKey: number;
    referenceModel: string;
    referenceModelName: string;
    referenceDataKey: number;
    field: string;
    fieldName: string;
    beforeValue: string;
    afterValue: string;
    userId: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
}
