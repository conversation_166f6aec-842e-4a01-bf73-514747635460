import { ObjectType } from 'type-graphql';
import { IReimbursement } from '@clinico/type-graphql-persistence/models/public/reimbursement.model';

@ObjectType({ implements: IReimbursement })
export class Reimbursement implements IReimbursement {
    id: number;
    currencyId: number;
    receiptTypeId: number;
    createdUserId: number;
    code: string;
    date: Date;
    status: number;
    currencyRate: string;
    taxId: string;
    paymentType: number;
    paymentDate: Date;
    createdAt: Date;
    updatedAt: Date;
}
