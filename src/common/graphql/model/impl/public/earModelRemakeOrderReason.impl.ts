import { ObjectType } from 'type-graphql';
import { IEarModelRemakeOrderReason } from '@clinico/type-graphql-persistence/models/public/earModelRemakeOrderReason.model';

@ObjectType({ implements: IEarModelRemakeOrderReason })
export class EarModelRemakeOrderReason implements IEarModelRemakeOrderReason {
    id: number;
    earModelOrderId: number;
    ear: string;
    remakeReason: number;
    painfulArea: number;
    leakType: number;
    createdAt: Date;
    updatedAt: Date;
    remakeEarModelOrderId: number;
}
