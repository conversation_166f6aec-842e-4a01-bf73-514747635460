import { ObjectType } from 'type-graphql';
import { IMemberSn } from '@clinico/type-graphql-persistence/models/public/memberSn.model';

@ObjectType({ implements: IMemberSn })
export class MemberSn implements IMemberSn {
    id: number;
    materialCode: string;
    SN: string;
    memberId: number;
    memberCode: string;
    isOverSea: boolean;
    isOtherChannel: boolean;
    ear: string;
    clinicoSN: string;
    firewareVersion: string;
    qty: number;
    status: number;
    warrantyStartDate: Date;
    warrantyEndDate: Date;
    createdAt: Date;
    updatedAt: Date;
    batteryType: string;
    lotNo: string;
}
