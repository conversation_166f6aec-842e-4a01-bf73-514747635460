import { ObjectType } from 'type-graphql';
import { IMarketActivityInternalAttendance } from '@clinico/type-graphql-persistence/models/public/marketActivityInternalAttendance.model';

@ObjectType({ implements: IMarketActivityInternalAttendance })
export class MarketActivityInternalAttendance
    implements IMarketActivityInternalAttendance
{
    id: number;
    marketActivityId: number;
    departmentId: number;
    costCenterId: number;
    internalCount: number;
    estimatedCost: string;
    createdAt: string;
    updatedAt: string;
}
