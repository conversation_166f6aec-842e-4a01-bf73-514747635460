import { ObjectType } from 'type-graphql';
import { ICustomClosingTime } from '@clinico/type-graphql-persistence/models/public/customClosingTime.model';

@ObjectType({ implements: ICustomClosingTime })
export class CustomClosingTime implements ICustomClosingTime {
    id: number;
    isAllDay: boolean;
    startClosingTime: string;
    endClosingTime: string;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    startDate: Date;
    endDate: Date;
}
