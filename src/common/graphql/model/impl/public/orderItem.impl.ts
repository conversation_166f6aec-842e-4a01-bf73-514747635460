import { ObjectType } from 'type-graphql';
import { IOrderItem } from '@clinico/type-graphql-persistence/models/public/orderItem.model';

@ObjectType({ implements: IOrderItem })
export class OrderItem implements IOrderItem {
    id: number;
    orderId: number;
    seq: number;
    materialCode: string;
    materialId: number;
    unit: string;
    bundleId: number;
    qty: number;
    price: string;
    amount: string;
    createdAt: Date;
    updatedAt: Date;
}
