import { ObjectType } from 'type-graphql';
import { IRetailCategory } from '@clinico/type-graphql-persistence/models/public/retailCategory.model';

@ObjectType({ implements: IRetailCategory })
export class RetailCategory implements IRetailCategory {
    id: number;
    companyId: number;
    code: string;
    name: string;
    viewOrder: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
