import { ObjectType } from 'type-graphql';
import { IBooking } from '@clinico/type-graphql-persistence/models/public/booking.model';

@ObjectType({ implements: IBooking })
export class Booking implements IBooking {
    id: number;
    storeId: number;
    rentAppointmentId: number;
    returnAppointmentId: number;
    memberId: number;
    startDate: Date;
    endDate: Date;
    returnDate: Date;
    status: number;
    monitorNoComplete: boolean;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
