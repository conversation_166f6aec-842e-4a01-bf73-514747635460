import { ObjectType } from 'type-graphql';
import { IRentDeviceTransfer } from '@clinico/type-graphql-persistence/models/public/rentDeviceTransfer.model';

@ObjectType({ implements: IRentDeviceTransfer })
export class RentDeviceTransfer implements IRentDeviceTransfer {
    id: number;
    rentDeviceId: number;
    shippedStoreId: number;
    receivedStoreId: number;
    shippedDate: Date;
    receivedDate: Date;
    remark: string;
    status: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
