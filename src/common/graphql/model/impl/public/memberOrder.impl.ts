import { ObjectType } from 'type-graphql';
import { IMemberOrder } from '@clinico/type-graphql-persistence/models/public/memberOrder.model';

@ObjectType({ implements: IMemberOrder })
export class MemberOrder implements IMemberOrder {
    id: number;
    orderCode: string;
    orderSeq: number;
    memberId: number;
    memberCode: string;
    memberName: string;
    storeId: number;
    itemCode: string;
    itemDesc: string;
    orderQty: number;
    orderShipQty: number;
    orderUnit: string;
    orderAmount: number;
    prCode: string;
    prSeq: number;
    prQty: number;
    prUnit: string;
    poCode: string;
    poSeq: number;
    poQty: number;
    poUnit: string;
    clSoCode: string;
    clSoSeq: number;
    clPrCode: string;
    clPoCode: string;
    clReceiveCode: string;
    clShipCode: string;
    clShipQty: number;
    clShipDate: Date;
    clShipIspost: string;
    receiveCode: string;
    receiveStatus: string;
    receiveQty: number;
    receiveUnit: string;
    sn: string;
}
