import { ObjectType } from 'type-graphql';
import { IMemberPap } from '@clinico/type-graphql-persistence/models/public/memberPap.model';

@ObjectType({ implements: IMemberPap })
export class MemberPap implements IMemberPap {
    id: number;
    memberId: number;
    memberCode: string;
    seq: number;
    date: Date;
    storeId: number;
    createdUserId: number;
    height: number;
    weight: number;
    bmiValue: number;
    ahiValue: number;
    type: string;
    result: string;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    isSeekSleepMedicalAdvice: boolean;
    isSeekSleepMedicalAdviceRemark: string;
    neckCircumference: number;
}
