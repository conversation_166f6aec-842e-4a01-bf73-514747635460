import { ObjectType } from 'type-graphql';
import { IMarketActivityFeeItem } from '@clinico/type-graphql-persistence/models/public/marketActivityFeeItem.model';

@ObjectType({ implements: IMarketActivityFeeItem })
export class MarketActivityFeeItem implements IMarketActivityFeeItem {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    marketActivityId: number;
    feeTypeId: number;
    expense: string;
    memo: string;
}
