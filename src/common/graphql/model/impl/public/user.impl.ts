import { ObjectType } from 'type-graphql';
import { IUser } from '@clinico/type-graphql-persistence/models/public/user.model';

@ObjectType({ implements: IUser })
export class User implements IUser {
    id: number;
    companyId: number;
    departmentId: number;
    name: string;
    email: string;
    code: string;
    isLocked: boolean;
    lastSelectStoreId: number;
    active: string;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    isAudiologist: boolean;
    isHearingAidEngineer: boolean;
    storeId: number;
    regionId: number;
    isOutsourcing: boolean;
    authType: number;
}
