import { ObjectType } from 'type-graphql';
import { IMemberConsultationAnswer } from '@clinico/type-graphql-persistence/models/public/memberConsultationAnswer.model';

@ObjectType({ implements: IMemberConsultationAnswer })
export class MemberConsultationAnswer implements IMemberConsultationAnswer {
    id: number;
    memberId: number;
    version: number;
    answers: JSON;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
