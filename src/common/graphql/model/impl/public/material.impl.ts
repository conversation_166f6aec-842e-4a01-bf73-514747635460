import { ObjectType } from 'type-graphql';
import { IMaterial } from '@clinico/type-graphql-persistence/models/public/material.model';

@ObjectType({ implements: IMaterial })
export class Material implements IMaterial {
    id: number;
    code: string;
    name: string;
    category: string;
    ear: string;
    scope: string;
    brand: string;
    model: string;
    style: string;
    class: string;
    spec: string;
    unit: string;
    saleUnit: string;
    materialTypeCode: string;
    s3Key: string;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    isActive: boolean;
    repairCategory: string;
    salePrice: string;
    currencyId: number;
}
