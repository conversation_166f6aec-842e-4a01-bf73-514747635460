import { ObjectType } from 'type-graphql';
import {
    EnumMarketActivityApprovalStatus,
    IMarketActivityApprovalMapping,
} from '@clinico/type-graphql-persistence/models/public/marketActivityApprovalMapping.model';

@ObjectType({ implements: IMarketActivityApprovalMapping })
export class MarketActivityApprovalMapping
    implements IMarketActivityApprovalMapping
{
    id: number;
    marketActivityId: number;
    level: number;
    approvalUserId: number;
    approvalStatus: EnumMarketActivityApprovalStatus;
    approvalAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
