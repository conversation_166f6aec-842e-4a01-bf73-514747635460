import { ObjectType } from 'type-graphql';
import { IMemberAccount } from '@clinico/type-graphql-persistence/models/public/memberAccount.model';

@ObjectType({ implements: IMemberAccount })
export class MemberAccount implements IMemberAccount {
    id: number;
    memberCode: string;
    companyCode: string;
    email: string;
    password: string;
    locked: boolean;
    createdAt: Date;
    updatedAt: Date;
}
