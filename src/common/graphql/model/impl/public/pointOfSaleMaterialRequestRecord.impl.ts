import { ObjectType } from 'type-graphql';
import { IPointOfSaleMaterialRequestRecord } from '@clinico/type-graphql-persistence/models/public/pointOfSaleMaterialRequestRecord.model';

@ObjectType({ implements: IPointOfSaleMaterialRequestRecord })
export class PointOfSaleMaterialRequestRecord
    implements IPointOfSaleMaterialRequestRecord
{
    id: number;
    specificationId: number;
    managerId: number;
    quantity: number;
    departmentId: number;
    otherLocation: string;
    usingDate: Date;
    closingDate: Date;
    status: number;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
