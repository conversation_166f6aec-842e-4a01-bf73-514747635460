import { ObjectType } from 'type-graphql';
import { IRepairQuotationItem } from '@clinico/type-graphql-persistence/models/public/repairQuotationItem.model';

@ObjectType({ implements: IRepairQuotationItem })
export class RepairQuotationItem implements IRepairQuotationItem {
    id: number;
    repairQuotationHistoryId: number;
    materialCode: string;
    amount: number;
    confirmed: boolean;
    unrepairReason: string;
    unrepairRemark: string;
    createdAt: Date;
    updatedAt: Date;
    materialId: number;
}
