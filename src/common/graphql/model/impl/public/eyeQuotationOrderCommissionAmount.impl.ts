import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderCommissionAmount } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderCommissionAmount.model';

@ObjectType({ implements: IEyeQuotationOrderCommissionAmount })
export class EyeQuotationOrderCommissionAmount
    implements IEyeQuotationOrderCommissionAmount
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    commissionAmount: string;
    eyeQuotationOrderCommissionTypeId: number;
    eyeQuotationOrderId: number;
}
