import { ObjectType } from 'type-graphql';
import { IEyePromotionAddonProductItem } from '@clinico/type-graphql-persistence/models/public/eyePromotionAddonProductItem.model';

@ObjectType({ implements: IEyePromotionAddonProductItem })
export class EyePromotionAddonProductItem
    implements IEyePromotionAddonProductItem
{
    id: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    eyePromotionId: number;
    eyeProductItemTypeId: number;
    materialId: number;
    materialUnit: string;
    qty: number;
    isOptional: boolean;
    description: string;
}
