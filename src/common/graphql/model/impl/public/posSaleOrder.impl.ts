import { ObjectType } from 'type-graphql';
import { IPosSaleOrder } from '@clinico/type-graphql-persistence/models/public/posSaleOrder.model';

@ObjectType({ implements: IPosSaleOrder })
export class PosSaleOrder implements IPosSaleOrder {
    id: number;
    tiptopOrderCode: string;
    orderType: string;
    orderStauts: number;
    isApproved: boolean;
    payStauts: number;
    memberId: number;
    companyId: number;
    storeId: number;
    saleDate: Date;
    totalQty: number;
    totalDiscount: number;
    extraDiscount: number;
    orderDiscount: number;
    discountCode: string;
    originalPrice: number;
    paidAmount: number;
    depositAmount: number;
    totalAmount: number;
    totalUntaxedAmount: number;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
