import { ObjectType } from 'type-graphql';
import { IPosCheckoutOrderItem } from '@clinico/type-graphql-persistence/models/public/posCheckoutOrderItem.model';

@ObjectType({ implements: IPosCheckoutOrderItem })
export class PosCheckoutOrderItem implements IPosCheckoutOrderItem {
    id: number;
    checkoutOrderId: number;
    saleOrderDetailId: number;
    qty: number;
    createdAt: Date;
    updatedAt: Date;
}
