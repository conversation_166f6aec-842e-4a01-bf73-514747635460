import { ObjectType } from 'type-graphql';
import { IMember } from '@clinico/type-graphql-persistence/models/public/member.model';

@ObjectType({ implements: IMember })
export class Member implements IMember {
    id: number;
    cityId: number;
    districtId: number;
    storeId: number;
    audiologistId: number;
    name: string;
    enName: string;
    avatar: string;
    birthday: Date;
    email: string;
    education: string;
    gender: string;
    isMarried: string;
    isDead: string;
    lEarStatus: string;
    rEarStatus: string;
    ahiValue: number;
    phone: string;
    cellPhone: string;
    companyPhone: string;
    mailCode: string;
    residentMailCode: string;
    address: string;
    residentAddress: string;
    jobType: string;
    incomeRank: string;
    code: string;
    type: string;
    rank: string;
    clientCode: string;
    clientType: string;
    invoiceInformType: string;
    isQuietRankUp: string;
    isQuietEmail: string;
    isBlockEmail: string;
    isSendPos: string;
    isApplyBonus: number;
    isAgreePersonalInfoDocs: string;
    cardType: string;
    cardCode: string;
    lineUserId: string;
    lineOwner: string;
    lineElseOwner: string;
    dataSource: string;
    updatedDataSource: string;
    dataOwnerGroupId: number;
    dataOwnerUserId: number;
    createdUserId: number;
    createdDepartmentId: number;
    updatedUserId: number;
    recommendUserId: number;
    remark: string;
    remarkUser: string;
    checksumCode: string;
    departmentId: number;
    createdAt: Date;
    updatedAt: Date;
    companyId: number;
    contactPhone: string;
    isLoseContact: boolean;
    untraceable: string;
    psgAhiValue: number;
    regionId: number;
    wechatUserId: string;
    shoplineUserId: string;
    identityNumber: string;
}
