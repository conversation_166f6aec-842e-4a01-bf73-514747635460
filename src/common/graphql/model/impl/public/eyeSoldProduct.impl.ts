import { ObjectType } from 'type-graphql';
import { IEyeSoldProduct } from '@clinico/type-graphql-persistence/models/public/eyeSoldProduct.model';

@ObjectType({ implements: IEyeSoldProduct })
export class EyeSoldProduct implements IEyeSoldProduct {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    companyId: number;
    sn: string;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    customerId: number;
    customerCode: string;
    orderCode: string;
    shippingOrderCode: string;
    shippingDate: Date;
    udi: string;
    deptId: number;
    deptCode: string;
    userId: number;
    userCode: string;
    description: string;
    memo: string;
    status: number;
}
