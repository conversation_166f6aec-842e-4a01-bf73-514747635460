import { ObjectType } from 'type-graphql';
import {
    EnumMarketActivityStatus,
    IMarketActivity,
} from '@clinico/type-graphql-persistence/models/public/marketActivity.model';
import { MarketActivityExpertFeeItem } from './marketActivityExpertFeeItem.impl';
import { MarketActivityFeeItem } from './marketActivityFeeItem.impl';
import { MarketActivityBusinessProduct } from './marketActivityBusinessProduct.impl';
import { MarketActivityInternalAttendance } from './marketActivityInternalAttendance.impl';
import { MarketActivityInternalRegistration } from './marketActivityInternalRegistration.impl';
import { MarketActivityExternalAttendance } from './marketActivityExternalAttendance.impl';
import { MarketActivityExternalRegistration } from './marketActivityExternalRegistration.impl';
import { MarketActivityActualExpertFeeItem } from './marketActivityActualExpertFeeItem.impl';
import { MarketActivityActualFeeItem } from './marketActivityActualFeeItem.impl';

@ObjectType({ implements: IMarketActivity })
export class MarketActivity implements IMarketActivity {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    name: string;
    code: string;
    link: string;
    date1: Date;
    date2: Date;
    typeId: number;
    participationTypeId: number;
    natureId: number;
    memo: string;
    regionId: number;
    provinceId: number;
    cityId: number;
    districtId: number;
    address: string;
    departmentId: number;
    primaryUserId: number;
    expenseBudget: string;
    budgetCode: string;
    status: EnumMarketActivityStatus;
    bpmInstanceId: string;
    fundingSourceId: number;
    executionCode: string;
    closureCode: string;
    confirmExpense: string;
    costCenterId: number;
    // relations
    marketActivityExpertFeeItems: MarketActivityExpertFeeItem[];
    marketActivityFeeItems: MarketActivityFeeItem[];
    marketActivityBusinessProducts: MarketActivityBusinessProduct[];
    marketActivityInternalAttendances: MarketActivityInternalAttendance[];
    marketActivityInternalRegistrations: MarketActivityInternalRegistration[];
    marketActivityExternalAttendances: MarketActivityExternalAttendance[];
    marketActivityExternalRegistrations: MarketActivityExternalRegistration[];
    marketActivityActualExpertFeeItems: MarketActivityActualExpertFeeItem[];
    marketActivityActualFeeItems: MarketActivityActualFeeItem[];
}
