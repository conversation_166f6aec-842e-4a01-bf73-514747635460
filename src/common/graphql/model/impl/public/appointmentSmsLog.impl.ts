import { ObjectType } from 'type-graphql';
import { IAppointmentSmsLog } from '@clinico/type-graphql-persistence/models/public/appointmentSmsLog.model';

@ObjectType({ implements: IAppointmentSmsLog })
export class AppointmentSmsLog implements IAppointmentSmsLog {
    id: number;
    appointmentId: number;
    appointmentDate: Date;
    createdAt: Date;
    updatedAt: Date;
    content: string;
    isSend: boolean;
}
