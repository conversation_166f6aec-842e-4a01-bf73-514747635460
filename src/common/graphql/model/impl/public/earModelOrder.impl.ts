import { ObjectType } from 'type-graphql';
import { IEarModelOrder } from '@clinico/type-graphql-persistence/models/public/earModelOrder.model';

@ObjectType({ implements: IEarModelOrder })
export class EarModelOrder implements IEarModelOrder {
    id: number;
    orderId: string;
    memberId: number;
    auditionId: number;
    createdStoreId: number;
    createdDate: Date;
    expectedCompletedDate: Date;
    completedDate: Date;
    orderType: number;
    status: number;
    ears: string;
    createdUserId: number;
    updatedUserId: number;
    receiveType: number;
    recipientStoreId: number;
    recipient: string;
    address: string;
    isRemake: boolean;
    isRemakeAble: boolean;
    remakeOrderId: number;
    remakeEars: number;
    leftRemakeRemark: string;
    rightRemakeRemark: string;
    leftRemakeMethod: string;
    rightRemakeMethod: string;
    rejectReason: number;
    createdAt: Date;
    updatedAt: Date;
    isSystemCanceled: boolean;
    phone: string;
    isUrgent: boolean;
    regionId: number;
}
