import { ObjectType } from 'type-graphql';
import { IAnnouncementReadRule } from '@clinico/type-graphql-persistence/models/public/announcementReadRule.model';

@ObjectType({ implements: IAnnouncementReadRule })
export class AnnouncementReadRule implements IAnnouncementReadRule {
    id: number;
    announcementId: number;
    allowAllToView: boolean;
    companyId: number;
    zoneId: number;
    zone2Id: number;
    storeId: number;
    regionId: number;
}
