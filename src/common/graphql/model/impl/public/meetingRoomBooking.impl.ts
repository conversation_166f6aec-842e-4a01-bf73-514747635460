import { ObjectType } from 'type-graphql';
import { IMeetingRoomBooking } from '@clinico/type-graphql-persistence/models/public/meetingRoomBooking.model';

@ObjectType({ implements: IMeetingRoomBooking })
export class MeetingRoomBooking implements IMeetingRoomBooking {
    id: number;
    meetingRoomId: number;
    bookingUserId: number;
    bookingStart: Date;
    bookingEnd: Date;
    description: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
