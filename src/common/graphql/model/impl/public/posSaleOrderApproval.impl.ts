import { ObjectType } from 'type-graphql';
import { IPosSaleOrderApproval } from '@clinico/type-graphql-persistence/models/public/posSaleOrderApproval.model';

@ObjectType({ implements: IPosSaleOrderApproval })
export class PosSaleOrderApproval implements IPosSaleOrderApproval {
    id: number;
    saleOrderId: number;
    status: string;
    workFlowUrl: string;
    createdAt: Date;
    updatedAt: Date;
}
