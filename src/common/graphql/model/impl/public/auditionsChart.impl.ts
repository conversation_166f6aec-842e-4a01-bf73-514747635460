import { ObjectType } from 'type-graphql';
import { IAuditionsChart } from '@clinico/type-graphql-persistence/models/public/auditionsChart.model';

@ObjectType({ implements: IAuditionsChart })
export class AuditionsChart implements IAuditionsChart {
    id: number;
    type: string;
    ear: string;
    frequency: number;
    decibel: number;
    noResponse: boolean;
    auditionId: number;
    createdAt: Date;
    updatedAt: Date;
}
