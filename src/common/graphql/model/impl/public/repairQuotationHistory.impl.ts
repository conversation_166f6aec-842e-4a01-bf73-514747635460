import { ObjectType } from 'type-graphql';
import { IRepairQuotationHistory } from '@clinico/type-graphql-persistence/models/public/repairQuotationHistory.model';

@ObjectType({ implements: IRepairQuotationHistory })
export class RepairQuotationHistory implements IRepairQuotationHistory {
    id: number;
    repairId: number;
    repairItemId: number;
    repairWorkOrderCode: string;
    repairContent: string;
    repairRemark: string;
    status: string;
    disabled: boolean;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
