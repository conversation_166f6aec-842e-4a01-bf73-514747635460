import { ObjectType } from 'type-graphql';
import { IOrderAuditResult } from '@clinico/type-graphql-persistence/models/public/orderAuditResult.model';

@ObjectType({ implements: IOrderAuditResult })
export class OrderAuditResult implements IOrderAuditResult {
    id: number;
    orderAuditId: number;
    seq: number;
    otoscopePass: string;
    tympanogramPass: string;
    audiogramPass: string;
    rearPass: string;
    reurPass: string;
    otoscopeNotPassReasons: JSON;
    otoscopeNotPassOtherReasonRemark: string;
    tympanogramNotPassReasons: JSON;
    tympanogramNotPassOtherReasonRemark: string;
    audiogramNotPassReasons: JSON;
    audiogramNotPassReasonImage: string;
    audiogramNotPassOtherReasonRemark: string;
    rearNotPassReasons: JSON;
    rearNotPassOtherReasonRemark: string;
    reurNotPassReasons: JSON;
    reurNotPassOtherReasonRemark: string;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
