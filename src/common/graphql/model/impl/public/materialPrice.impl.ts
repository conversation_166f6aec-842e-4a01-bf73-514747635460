import { ObjectType } from 'type-graphql';
import { IMaterialPrice } from '@clinico/type-graphql-persistence/models/public/materialPrice.model';

@ObjectType({ implements: IMaterialPrice })
export class MaterialPrice implements IMaterialPrice {
    id: number;
    materialCode: string;
    companyId: number;
    price: number;
    unit: string;
    createdAt: Date;
    updatedAt: Date;
    materialId: number;
}
