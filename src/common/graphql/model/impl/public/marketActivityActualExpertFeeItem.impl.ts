import { ObjectType } from 'type-graphql';
import { IMarketActivityActualExpertFeeItem } from '@clinico/type-graphql-persistence/models/public/marketActivityActualExpertFeeItem.model';

@ObjectType({ implements: IMarketActivityActualExpertFeeItem })
export class MarketActivityActualExpertFeeItem
    implements IMarketActivityActualExpertFeeItem
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    marketActivityId: number;
    expertTypeId: number;
    name: string;
    expense: string;
    memo: string;
    expertLevelId: number;
    expertServiceTimeId: number;
}
