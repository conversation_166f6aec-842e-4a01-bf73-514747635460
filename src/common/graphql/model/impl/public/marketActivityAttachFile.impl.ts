import { ObjectType } from 'type-graphql';
import { IMarketActivityAttachFile } from '@clinico/type-graphql-persistence/models/public/marketActivityAttachFile.model';

@ObjectType({ implements: IMarketActivityAttachFile })
export class MarketActivityAttachFile implements IMarketActivityAttachFile {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    marketActivityId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
