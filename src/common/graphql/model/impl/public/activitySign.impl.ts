import { ObjectType } from 'type-graphql';
import { IActivitySign } from '@clinico/type-graphql-persistence/models/public/activitySign.model';

@ObjectType({ implements: IActivitySign })
export class ActivitySign implements IActivitySign {
    id: number;
    activityId: number;
    date: Date;
    startTime: string;
    askToSignIn: boolean;
    allowSignInTime: string;
    endTime: string;
    askToSignOut: boolean;
    allowSignOutTime: string;
}
