import { ObjectType } from 'type-graphql';
import { IPointOfSaleMaterial } from '@clinico/type-graphql-persistence/models/public/pointOfSaleMaterial.model';

@ObjectType({ implements: IPointOfSaleMaterial })
export class PointOfSaleMaterial implements IPointOfSaleMaterial {
    id: number;
    name: string;
    typeId: number;
    brandId: number;
    supplierName: string;
    picture: string;
    date1: Date;
    date2: Date;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
