import { ObjectType } from 'type-graphql';
import { IOrderAudit } from '@clinico/type-graphql-persistence/models/public/orderAudit.model';

@ObjectType({ implements: IOrderAudit })
export class OrderAudit implements IOrderAudit {
    id: number;
    companyId: number;
    storeId: number;
    orderCode: string;
    memberId: number;
    status: string;
    optionalEar: string;
    optionalFormula: number;
    oneEarReasons: JSON;
    audiogramImage: string;
    otoscopeLeftImage: string;
    otoscopeRightImage: string;
    otoscopeNotExecuteReasons: JSON;
    otoscopeNotExecuteOtherReason: string;
    tympanogramLeftImage: string;
    tympanogramRightImage: string;
    tympanogramNotExecuteReasons: JSON;
    tympanogramNotExecuteOtherReason: string;
    reurImage: string;
    reurNotExecuteReasons: JSON;
    reurNotExecuteOtherReason: string;
    rearImage: string;
    rearNotExecuteReasons: JSON;
    rearNotExecuteOtherReason: string;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    firstAuditDate: Date;
    closedDate: Date;
    orderUserId: number;
    closedUserId: number;
    oneEarOtherReason: string;
    otoscopeImage: string;
    tympanogramImage: string;
}
