import { ObjectType } from 'type-graphql';
import {
    EnumEyeProductStatus,
    IEyeProduct,
} from '@clinico/type-graphql-persistence/models/public/eyeProduct.model';
import { EyePromotionProductMapping } from './eyePromotionProductMapping.impl';
import { EyeProductsProductUser } from './eyeProductsProductUser.impl';
import { EyeProductsToProductLine } from '../salesRepWorkstation/eyeProductsToProductLine.impl';

@ObjectType({ implements: IEyeProduct })
export class EyeProduct implements IEyeProduct {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeProductTypeId: number;
    regionId: number;
    companyId: number;
    date1: Date;
    date2: Date;
    name: string;
    description: string;
    brand: string;
    model: string;
    currencyId: number;
    salePrice: string;
    status: EnumEyeProductStatus;
    dealerPrice: string;
    standardDeliveryDays: number;
    dsmPrice: string;
    regionalManagerPrice: string;
    businessManagerPrice: string;
    generalManagerPrice: string;
    bonusCoefficient: string;
    eyeProductGroupId: number;
    displayId: number;
    productLineId: number;
    singleCurrencyId: number;
    shippingGuidancePriceBonus: string;
    dsmPriceBonus: string;
    rsmPriceBonus: string;
    nsmPriceBonus: string;
    generalManagerPriceBonus: string;
    groupBonus: string;
    hospitalSuggestSalePrice: string;
    unitBonus: string;

    // relations
    eyePromotionProductMappings: EyePromotionProductMapping[];
    eyeProductsProductUsers: EyeProductsProductUser[];
    eyeProductsToProductLines: EyeProductsToProductLine[];
}
