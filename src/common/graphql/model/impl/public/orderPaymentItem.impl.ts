import { ObjectType } from 'type-graphql';
import { IOrderPaymentItem } from '@clinico/type-graphql-persistence/models/public/orderPaymentItem.model';

@ObjectType({ implements: IOrderPaymentItem })
export class OrderPaymentItem implements IOrderPaymentItem {
    id: number;
    orderPaymentId: number;
    orderId: number;
    receivedAmount: string;
    paymentMethod: string;
    paymentPayload: JSON;
    createdAt: Date;
    updatedAt: Date;
}
