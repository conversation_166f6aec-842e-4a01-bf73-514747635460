import { ObjectType } from 'type-graphql';
import { IEyeFixedAssetRentalObject } from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetRentalObject.model';
import { EnumEyeFixedAssetRentalObjectCode } from '@clinico/typeorm-persistence/models/public/eyeFixedAssetRentalObject.model';

@ObjectType({ implements: IEyeFixedAssetRentalObject })
export class EyeFixedAssetRentalObject implements IEyeFixedAssetRentalObject {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    name: string;
    code: EnumEyeFixedAssetRentalObjectCode;
    regionId: number;
}
