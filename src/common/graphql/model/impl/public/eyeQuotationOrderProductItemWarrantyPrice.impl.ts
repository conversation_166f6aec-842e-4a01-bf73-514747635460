import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderProductItemWarrantyPrice } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderProductItemWarrantyPrice.model';

@ObjectType({ implements: IEyeQuotationOrderProductItemWarrantyPrice })
export class EyeQuotationOrderProductItemWarrantyPrice
    implements IEyeQuotationOrderProductItemWarrantyPrice
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeQuotationOrderProductItemId: number;
    internalWarrantyPrice: string;
    internalWarrantyCurrencyId: number;
}
