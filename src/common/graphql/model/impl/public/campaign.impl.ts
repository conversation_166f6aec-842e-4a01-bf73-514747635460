import { ObjectType } from 'type-graphql';
import { ICampaign } from '@clinico/type-graphql-persistence/models/public/campaign.model';

@ObjectType({ implements: ICampaign })
export class Campaign implements ICampaign {
    id: number;
    campaignSourceId: number;
    companyId: number;
    name: string;
    link: string;
    satrtDate: Date;
    endDate: Date;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
