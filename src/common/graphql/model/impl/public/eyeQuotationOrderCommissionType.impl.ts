import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderCommissionType } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderCommissionType.model';

@ObjectType({ implements: IEyeQuotationOrderCommissionType })
export class EyeQuotationOrderCommissionType
    implements IEyeQuotationOrderCommissionType
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    regionId: number;
    companyId: number;
}
