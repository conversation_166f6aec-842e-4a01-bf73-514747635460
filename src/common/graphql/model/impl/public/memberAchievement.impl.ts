import { ObjectType } from 'type-graphql';
import { IMemberAchievement } from '@clinico/type-graphql-persistence/models/public/memberAchievement.model';

@ObjectType({ implements: IMemberAchievement })
export class MemberAchievement implements IMemberAchievement {
    id: number;
    memberCode: string;
    score: number;
    buyoutOrSubscribe: number;
    recommendHst: number;
    recommendBuyoutOrSubscribe: number;
    shareGraphic: number;
    shareVideo: number;
    conference: number;
    consecutiveLogin: number;
    continuousUsing: number;
    createdAt: Date;
    updatedAt: Date;
}
