import { ObjectType } from 'type-graphql';
import { IMemberTriedHistory } from '@clinico/type-graphql-persistence/models/public/memberTriedHistory.model';

@ObjectType({ implements: IMemberTriedHistory })
export class MemberTriedHistory implements IMemberTriedHistory {
    id: number;
    memberId: number;
    date: Date;
    isAccompanied: boolean;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
