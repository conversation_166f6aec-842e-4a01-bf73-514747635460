import { ObjectType } from 'type-graphql';
import { IResmedDevice } from '@clinico/type-graphql-persistence/models/public/resmedDevice.model';

@ObjectType({ implements: IResmedDevice })
export class ResmedDevice implements IResmedDevice {
    id: number;
    storeId: number;
    memberId: number;
    memberCode: string;
    materialCode: string;
    SN: string;
    shipDate: Date;
    type: string;
    subscribeStatus: string;
    subscribeStartDate: Date;
    subscribeEndDate: Date;
    createdAt: Date;
    updatedAt: Date;
    materialId: number;
}
