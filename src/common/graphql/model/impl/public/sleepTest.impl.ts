import { ObjectType } from 'type-graphql';
import { ISleepTest } from '@clinico/type-graphql-persistence/models/public/sleepTest.model';

@ObjectType({ implements: ISleepTest })
export class SleepTest implements ISleepTest {
    id: number;
    memberId: number;
    storeId: number;
    defaultSleepTechnologistUserId: number;
    sleepTechnologistUserId: number;
    type: string;
    sleepTestReferralId: number;
    otherReferral: string;
    completeContent: string;
    neckCircumference: number;
    bmi: number;
    ahi: number;
    lowestSpo2: number;
    specialBreath: string;
    snoresPerNight: number;
    remark: string;
    status: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    remarkByStore: string;
    isTestFailed: boolean;
    airviewUrl: string;
    resmedBookingId: number;
    apneaIndex: number;
    uai: number;
    oai: number;
    cai: number;
    mai: number;
    hypopneaIndex: number;
    odi: number;
    averageSaturation: number;
    baselineSaturation: number;
    minPulse: number;
    maxPulse: number;
    averagePulse: number;
}
