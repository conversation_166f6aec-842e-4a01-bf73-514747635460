import { ObjectType } from 'type-graphql';
import { IMemberDocument } from '@clinico/type-graphql-persistence/models/public/memberDocument.model';

@ObjectType({ implements: IMemberDocument })
export class MemberDocument implements IMemberDocument {
    id: number;
    memberId: number;
    name: string;
    description: string;
    extension: string;
    s3Key: string;
    s3Url: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
