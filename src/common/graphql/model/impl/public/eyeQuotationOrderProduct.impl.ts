import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderProduct } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderProduct.model';

@ObjectType({ implements: IEyeQuotationOrderProduct })
export class EyeQuotationOrderProduct implements IEyeQuotationOrderProduct {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeQuotationOrderId: number;
    eyeProductId: number;
    qty: number;
    name: string;
    description: string;
    brand: string;
    model: string;
    currencyId: number;
    exchangeRate: string;
    salePrice: string;
    unitPrice: string;
    taxRate: string;
    unitPriceVat: string;
    customQuotationPrice: string;
    unitBonus: string;
}
