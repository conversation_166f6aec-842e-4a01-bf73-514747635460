import { ObjectType } from 'type-graphql';
import { IPointOfSaleMaterialInventoryRecord } from '@clinico/type-graphql-persistence/models/public/pointOfSaleMaterialInventoryRecord.model';

@ObjectType({ implements: IPointOfSaleMaterialInventoryRecord })
export class PointOfSaleMaterialInventoryRecord
    implements IPointOfSaleMaterialInventoryRecord
{
    id: number;
    specificationId: number;
    specificationRequestId: number;
    billingDate: Date;
    quantity: number;
    unitPriceWithTax: string;
    referenceUnitPrice: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
