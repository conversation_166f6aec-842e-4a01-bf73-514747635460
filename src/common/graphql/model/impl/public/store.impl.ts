import { ObjectType } from 'type-graphql';
import { IStore } from '@clinico/type-graphql-persistence/models/public/store.model';

@ObjectType({ implements: IStore })
export class Store implements IStore {
    id: number;
    companyId: number;
    areaId: number;
    cityId: number;
    districtId: number;
    zoneId: number;
    zone2Id: number;
    name: string;
    code: string;
    email: string;
    phone: string;
    address: string;
    createdAt: Date;
    updatedAt: Date;
    departmentId: number;
    isMaintenanceCenter: boolean;
    picture: string;
    createdUserId: number;
    updatedUserId: number;
    isShowInResmedBooking: boolean;
    guiNumber: string;
    openingDate: Date;
    closingDate: Date;
    isPosAvailable: boolean;
    sleepTechnologistUserId: number;
    regionId: number;
    isShowInMorear: boolean;
    lat: number;
    lng: number;
}
