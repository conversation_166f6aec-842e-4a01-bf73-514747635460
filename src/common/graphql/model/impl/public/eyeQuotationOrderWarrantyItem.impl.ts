import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderWarrantyItem } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderWarrantyItem.model';

@ObjectType({ implements: IEyeQuotationOrderWarrantyItem })
export class EyeQuotationOrderWarrantyItem
    implements IEyeQuotationOrderWarrantyItem
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeQuotationOrderId: number;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    sn: string;
    udi: string;
    qty: number;
    memo: string;
}
