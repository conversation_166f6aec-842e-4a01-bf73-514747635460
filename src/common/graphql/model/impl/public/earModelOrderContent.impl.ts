import { ObjectType } from 'type-graphql';
import { IEarModelOrderContent } from '@clinico/type-graphql-persistence/models/public/earModelOrderContent.model';

@ObjectType({ implements: IEarModelOrderContent })
export class EarModelOrderContent implements IEarModelOrderContent {
    id: number;
    ear: string;
    earModelOrderId: number;
    productionSn: string;
    material: number;
    otherMaterial: string;
    earCanalLength: number;
    size: number;
    type: number;
    isSkeleton: boolean;
    ditchType: number;
    venting: string;
    draining: string;
    removal: string;
    ciType: string;
    microType: number;
    miniType: number;
    miniTubeType: number;
    soundBore: number;
    soundBoreType: number;
    color: number;
    remark: string;
    createdAt: Date;
    updatedAt: Date;
    earDrumColor: string;
    earDrumAccessories: string;
    buttonSwitch: boolean;
    volumeSwitch: boolean;
    powerType: number;
    inEarSn: string;
    inEarMaterialCode: string;
    inEarCode: string;
    inEarName: string;
    inEarKIT: string;
}
