import { ObjectType } from 'type-graphql';
import { IMemberAchievementSeasonBackup } from '@clinico/type-graphql-persistence/models/public/memberAchievementSeasonBackup.model';

@ObjectType({ implements: IMemberAchievementSeasonBackup })
export class MemberAchievementSeasonBackup
    implements IMemberAchievementSeasonBackup
{
    id: number;
    memberCode: string;
    year: string;
    season: string;
    score: number;
    buyoutOrSubscribe: number;
    recommendHst: number;
    recommendBuyoutOrSubscribe: number;
    shareGraphic: number;
    shareVideo: number;
    conference: number;
    consecutiveLogin: number;
    continuousUsing: number;
}
