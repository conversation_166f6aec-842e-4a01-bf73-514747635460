import { ObjectType } from 'type-graphql';
import { IRole } from '@clinico/type-graphql-persistence/models/public/role.model';

@ObjectType({ implements: IRole })
export class Role implements IRole {
    id: number;
    name: string;
    allowAllClinicoStores: boolean;
    allowAllSkdStores: boolean;
    code: string;
    allowAllIbeliveStores: boolean;
    filterZones: string;
    filterSubZones: string;
    filterStores: string;
    allowAllCnStores: boolean;
    regionId: number;
    allowAllHearingCenters: boolean;
}
