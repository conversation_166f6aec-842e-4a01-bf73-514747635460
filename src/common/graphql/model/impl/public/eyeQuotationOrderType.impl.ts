import { ObjectType } from 'type-graphql';
import {
    EnumEyeQuotationOrderType,
    IEyeQuotationOrderType,
} from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderType.model';

@ObjectType({ implements: IEyeQuotationOrderType })
export class EyeQuotationOrderType implements IEyeQuotationOrderType {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    companyId: number;
    name: string;
    code: EnumEyeQuotationOrderType;
    viewOrder: number;
}
