import { ObjectType } from 'type-graphql';
import { IEyeFixedAssetDocument } from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetDocument.model';

@ObjectType({ implements: IEyeFixedAssetDocument })
export class EyeFixedAssetDocument implements IEyeFixedAssetDocument {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeFixedAssetId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
