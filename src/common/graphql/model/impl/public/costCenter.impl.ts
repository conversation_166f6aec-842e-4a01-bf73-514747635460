import { ObjectType } from 'type-graphql';
import { ICostCenter } from '@clinico/type-graphql-persistence/models/public/costCenter.model';

@ObjectType({ implements: ICostCenter })
export class CostCenter implements ICostCenter {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    name: string;
    code: string;
    regionId: number;
    companyId: number;
    departmentId: number;
}
