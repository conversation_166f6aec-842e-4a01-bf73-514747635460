import { ObjectType } from 'type-graphql';
import { IEyeFixedAssetImage } from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetImage.model';

@ObjectType({ implements: IEyeFixedAssetImage })
export class EyeFixedAssetImage implements IEyeFixedAssetImage {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeFixedAssetId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
