import { ObjectType } from 'type-graphql';
import {
    EnumEyeFixedAssetTypeCode,
    IEyeFixedAssetType,
} from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetType.model';

@ObjectType({ implements: IEyeFixedAssetType })
export class EyeFixedAssetType implements IEyeFixedAssetType {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    name: string;
    code: EnumEyeFixedAssetTypeCode;
    regionId: number;
}
