import { ObjectType } from 'type-graphql';
import { IBundleDetail } from '@clinico/type-graphql-persistence/models/public/bundleDetail.model';

@ObjectType({ implements: IBundleDetail })
export class BundleDetail implements IBundleDetail {
    id: number;
    bundleId: number;
    seq: number;
    name: string;
    defaultMaterialPriceId: number;
    price: number;
    maxQty: number;
    minQty: number;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
