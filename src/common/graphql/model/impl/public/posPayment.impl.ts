import { ObjectType } from 'type-graphql';
import { IPosPayment } from '@clinico/type-graphql-persistence/models/public/posPayment.model';

@ObjectType({ implements: IPosPayment })
export class PosPayment implements IPosPayment {
    id: number;
    companyId: number;
    code: string;
    type: string;
    name: string;
    isChange: boolean;
    maxChange: number;
    canReturn: boolean;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
