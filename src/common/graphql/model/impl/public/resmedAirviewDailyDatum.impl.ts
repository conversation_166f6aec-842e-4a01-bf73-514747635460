import { ObjectType } from 'type-graphql';
import { IResmedAirviewDailyDatum } from '@clinico/type-graphql-persistence/models/public/resmedAirviewDailyDatum.model';

@ObjectType({ implements: IResmedAirviewDailyDatum })
export class ResmedAirviewDailyDatum implements IResmedAirviewDailyDatum {
    id: number;
    airviewId: number;
    date: Date;
    setupDateDays: number;
    usage: string;
    usageBelow: boolean;
    ahi: string;
    ahiAbove: boolean;
    leakVent: string;
    leakVentAbove: boolean;
    leakValve: string;
    leakValveAbove: boolean;
    createdAt: Date;
    updatedAt: Date;
}
