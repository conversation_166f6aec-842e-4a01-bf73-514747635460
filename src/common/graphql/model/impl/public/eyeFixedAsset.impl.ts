import { ObjectType } from 'type-graphql';
import {
    EnumEyeFixedAssetRentalStatus,
    EnumEyeFixedAssetsStatus,
    IEyeFixedAsset,
} from '@clinico/type-graphql-persistence/models/public/eyeFixedAsset.model';
import { EyeFixedAssetRentalRecordItem } from './eyeFixedAssetRentalRecordItem.impl';
import { EyeFixedAssetItem } from './eyeFixedAssetItem.impl';
import { EyeFixedAssetsType } from './eyeFixedAssetsType.impl';
import { EyeFixedAssetsServiceProvider } from '@clinico/typeorm-persistence/models/public/eyeFixedAssetsServiceProvider.model';
import { EyeFixedAssetDepartment } from './eyeFixedAssetDepartment.impl';

@ObjectType({ implements: IEyeFixedAsset })
export class EyeFixedAsset implements IEyeFixedAsset {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    companyId: number;
    name: string;
    code: string;
    status: EnumEyeFixedAssetsStatus;
    deptId: number;
    userId: number;
    pmUserId: number;
    brand: string;
    memo: string;
    currentLocation: string;
    warehouseId: number;
    categoryId: number;
    borrowingDays: number;
    maxExtendedDays: number;
    maxExtendedTimes: number;
    deliveryDays: number;
    rentalStatus: EnumEyeFixedAssetRentalStatus;
    techManagerUserId: number;
    imei: string;

    // relations
    eyeFixedAssetItems: EyeFixedAssetItem[];
    eyeFixedAssetRentalRecordItems: EyeFixedAssetRentalRecordItem[];
    eyeFixedAssetsTypes: EyeFixedAssetsType[];
    eyeFixedAssetsServiceProviders: EyeFixedAssetsServiceProvider[];
    eyeFixedAssetDepartments: EyeFixedAssetDepartment[];
}
