import { ObjectType } from 'type-graphql';
import {
    IEyeFixedAssetRentalRecord,
    EnumEyeFixedAssetsRentalStatus,
} from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetRentalRecord.model';
import { EyeFixedAssetRentalRecordItem } from './eyeFixedAssetRentalRecordItem.impl';

@ObjectType({ implements: IEyeFixedAssetRentalRecord })
export class EyeFixedAssetRentalRecord implements IEyeFixedAssetRentalRecord {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    regionId: number;
    companyId: number;
    installDate: Date;
    date1: Date;
    date2: Date;
    dismantleDate: Date;
    status: EnumEyeFixedAssetsRentalStatus;
    remark: string;
    courierNumber: string;

    // relations
    eyeFixedAssetRentalRecordItems: EyeFixedAssetRentalRecordItem[];
}
