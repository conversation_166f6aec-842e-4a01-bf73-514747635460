import { ObjectType } from 'type-graphql';
import { IWorkDiary } from '@clinico/type-graphql-persistence/models/public/workDiary.model';

@ObjectType({ implements: IWorkDiary })
export class WorkDiary implements IWorkDiary {
    id: number;
    companyId: number;
    memberId: number;
    memberCode: string;
    departmentId: number;
    userId: number;
    workServiceItemId: number;
    seq: number;
    date: Date;
    shiftType: string;
    chShiftType: string;
    recommendUser: string;
    dataSource: string;
    type: string;
    supplyCode: string;
    qty: number;
    price: number;
    bonus: number;
    isNewCustomer: string;
    isSelectPair: string;
    workTriedItem: string;
    isTry: string;
    isPrint: string;
    remark: string;
    serviceDescription: string;
    time1: string;
    time2: string;
    createdUserId: number;
    updatedUserId: number;
    memberType: number;
    createdAt: Date;
    updatedAt: Date;
    createdDate: Date;
    updatedDate: Date;
    recommendDepartment: string;
    recommendCode: string;
    appointmentId: number;
    regionId: number;
    dataSourceId: number;
    isAccompanied: boolean;
    triedHistoryId: number;
    hearingAidOpportunityRate: number;
    salesOpportunityRate: number;
}
