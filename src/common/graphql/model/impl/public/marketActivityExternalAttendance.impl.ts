import { ObjectType } from 'type-graphql';
import { IMarketActivityExternalAttendance } from '@clinico/type-graphql-persistence/models/public/marketActivityExternalAttendance.model';
@ObjectType({ implements: IMarketActivityExternalAttendance })
export class MarketActivityExternalAttendance
    implements IMarketActivityExternalAttendance
{
    id: number;
    marketActivityId: number;
    customerId: number;
    customerCount: number;
    createdAt: string;
    updatedAt: string;
}
