import { ObjectType } from 'type-graphql';
import { IEyeFixedAssetRentalGoal } from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetRentalGoal.model';

@ObjectType({ implements: IEyeFixedAssetRentalGoal })
export class EyeFixedAssetRentalGoal implements IEyeFixedAssetRentalGoal {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    name: string;
    code: string;
    regionId: number;
}
