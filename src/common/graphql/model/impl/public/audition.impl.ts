import { ObjectType } from 'type-graphql';
import { IAudition } from '@clinico/type-graphql-persistence/models/public/audition.model';

@ObjectType({ implements: IAudition })
export class Audition implements IAudition {
    id: number;
    memberId: number;
    memberCode: string;
    departmentId: number;
    userId: number;
    seq: number;
    testDate: Date;
    shiftType: string;
    isSelectPair: string;
    isInviteUseNewest: string;
    isSatisfied: string;
    isNeedNew: string;
    isNeedCheck: string;
    isPurchased: string;
    isConscious: string;
    isTellGiftVoucher: string;
    noNeedReason: string;
    dissatisfiedReason: string;
    opinionAboutAudition: string;
    lEarRecognition: string;
    rEarRecognition: string;
    lEarLossLevel: string;
    rEarLossLevel: string;
    hearingLossType: string;
    hearingLossEar: string;
    complication: string;
    purchasePower: string;
    lastPurchaseYear: string;
    useSituation: string;
    buyBrand: string;
    lastDoctor: string;
    relatedPerson: string;
    result: string;
    rank: string;
    rEarAC250: string;
    rEarAC500: string;
    rEarAC1K: string;
    rEarAC2K: string;
    rEarAC4K: string;
    rEarAC8K: string;
    lEarAC250: string;
    lEarAC500: string;
    lEarAC1K: string;
    lEarAC2K: string;
    lEarAC4K: string;
    lEarAC8K: string;
    rEarBC250: string;
    rEarBC500: string;
    rEarBC1K: string;
    rEarBC2K: string;
    rEarBC4K: string;
    rEarBC8K: string;
    lEarBC250: string;
    lEarBC500: string;
    lEarBC1K: string;
    lEarBC2K: string;
    lEarBC4K: string;
    lEarBC8K: string;
    lEarSRT: string;
    rEarSRT: string;
    lEarSDT: string;
    rEarSDT: string;
    lEarSDS: string;
    rEarSDS: string;
    lEarSDSDB: string;
    rEarSDSDB: string;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    deletedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    rEarTympanogram: string;
    lEarTympanogram: string;
    lHearingLossType: string;
    rHearingLossType: string;
    bothBareEarSds: string;
    bothWearEarSds: string;
    bothBareEarSdsDb: string;
    bothWearEarSdsDb: string;
}
