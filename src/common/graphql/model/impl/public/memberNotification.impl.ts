import { ObjectType } from 'type-graphql';
import { IMemberNotification } from '@clinico/type-graphql-persistence/models/public/memberNotification.model';

@ObjectType({ implements: IMemberNotification })
export class MemberNotification implements IMemberNotification {
    id: number;
    memberId: number;
    message: string;
    type: string;
    values: JSON;
    isRead: boolean;
    createdAt: Date;
    updatedAt: Date;
}
