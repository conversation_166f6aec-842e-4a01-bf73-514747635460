import { ObjectType } from 'type-graphql';
import { IMarketActivityInternalAttendance } from '@clinico/type-graphql-persistence/models/public/marketActivityInternalAttendance.model';
import { IMarketActivityExternalRegistration } from '@clinico/type-graphql-persistence/models/public/marketActivityExternalRegistration.model';
@ObjectType({ implements: IMarketActivityExternalRegistration })
export class MarketActivityExternalRegistration
    implements IMarketActivityExternalRegistration
{
    id: number;
    marketActivityId: number;
    customerId: number;
    customerCount: number;
    createdAt: string;
    updatedAt: string;
}
