import { ObjectType } from 'type-graphql';
import { IHoliday } from '@clinico/type-graphql-persistence/models/public/holiday.model';

@ObjectType({ implements: IHoliday })
export class Holiday implements IHoliday {
    id: number;
    date: Date;
    name: string;
    description: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    isStoreIgnore: boolean;
    regionId: number;
}
