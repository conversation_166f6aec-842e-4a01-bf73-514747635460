import { ObjectType } from 'type-graphql';
import { IEyePromotion } from '@clinico/type-graphql-persistence/models/public/eyePromotion.model';
import { EyePromotionProductMapping } from './eyePromotionProductMapping.impl';

@ObjectType({ implements: IEyePromotion })
export class EyePromotion implements IEyePromotion {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    companyId: number;
    name: string;
    date1: Date;
    date2: Date;
    isAddon: boolean;
    discountRate: string;
    discountAmount: string;
    currencyId: number;
    description: string;

    // relations
    eyePromotionProductMappings: EyePromotionProductMapping[];
}
