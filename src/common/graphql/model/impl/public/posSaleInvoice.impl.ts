import { ObjectType } from 'type-graphql';
import { IPosSaleInvoice } from '@clinico/type-graphql-persistence/models/public/posSaleInvoice.model';

@ObjectType({ implements: IPosSaleInvoice })
export class PosSaleInvoice implements IPosSaleInvoice {
    id: number;
    checkoutOrderId: number;
    invoiceCode: string;
    sellerGuiNumber: string;
    buyerGuiNumber: string;
    taxableAmount: number;
    dutyFreeAmount: number;
    zeroTax: number;
    untaxedAmount: number;
    taxAmount: number;
    amount: number;
    saleTime: string;
    carrierType: string;
    carrierCode: string;
    carrierHiddenCode: string;
    loveCode: string;
    rebateCode: string;
    createdAt: Date;
    updatedAt: Date;
}
