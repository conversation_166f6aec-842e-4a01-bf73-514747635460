import { ObjectType } from 'type-graphql';
import { IEyeFixedAssetItem } from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetItem.model';

@ObjectType({ implements: IEyeFixedAssetItem })
export class EyeFixedAssetItem implements IEyeFixedAssetItem {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeFixedAssetId: number;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    sn: string;
    udi: string;
    memo: string;
}
