import { ObjectType } from 'type-graphql';
import { IRepairStatusHistory } from '@clinico/type-graphql-persistence/models/public/repairStatusHistory.model';

@ObjectType({ implements: IRepairStatusHistory })
export class RepairStatusHistory implements IRepairStatusHistory {
    id: number;
    repairId: number;
    status: string;
    remark: string;
    createdUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
