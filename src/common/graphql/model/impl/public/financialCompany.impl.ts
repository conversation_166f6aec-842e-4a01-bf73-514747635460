import { ObjectType } from 'type-graphql';
import { IFinancialCompany } from '@clinico/type-graphql-persistence/models/public/financialCompany.model';

@ObjectType({ implements: IFinancialCompany })
export class FinancialCompany implements IFinancialCompany {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    name: string;
    code: string;
    regionId: number;
    companyId: number;
}
