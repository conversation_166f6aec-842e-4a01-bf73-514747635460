import { ObjectType } from 'type-graphql';
import { ICorporation } from '@clinico/type-graphql-persistence/models/public/corporation.model';
import { CorporationsDept } from './corporationsDept.impl';

@ObjectType({ implements: ICorporation })
export class Corporation implements ICorporation {
    id: number;
    regionId: number;
    disabled: boolean;
    createdAt: Date;
    updatedAt: Date;
    code: string;
    name: string;
    guiNumber: string;
    yonyouSetOfBook: string;
    phone: string;
    fax: string;
    email: string;
    address: string;
    website: string;
    facebook: string;
    instagram: string;
    twitter: string;
    weibo: string;
    tiktok: string;

    // relations
    corporationsDepts: CorporationsDept[];
}
