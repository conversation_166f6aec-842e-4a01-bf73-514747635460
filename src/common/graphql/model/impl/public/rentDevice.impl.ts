import { ObjectType } from 'type-graphql';
import { IRentDevice } from '@clinico/type-graphql-persistence/models/public/rentDevice.model';

@ObjectType({ implements: IRentDevice })
export class RentDevice implements IRentDevice {
    id: number;
    storeId: number;
    primaryStoreId: number;
    rentDeviceType: number;
    rentDeviceBookingId: number;
    rentDeviceTransferId: number;
    SN: string;
    status: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    materialCode: string;
    materialPrice: number;
    materialName: string;
}
