import { ObjectType } from 'type-graphql';
import { IShiftScheduler } from '@clinico/type-graphql-persistence/models/public/shiftScheduler.model';

@ObjectType({ implements: IShiftScheduler })
export class ShiftScheduler implements IShiftScheduler {
    id: number;
    userId: number;
    userCode: string;
    shiftDate: Date;
    shiftCode: string;
    storeId: number;
    workStartTime: string;
    workEndTime: string;
    batchId: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
