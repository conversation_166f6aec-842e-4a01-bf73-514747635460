import { ObjectType } from 'type-graphql';
import { IMemberTriedItem } from '@clinico/type-graphql-persistence/models/public/memberTriedItem.model';

@ObjectType({ implements: IMemberTriedItem })
export class MemberTriedItem implements IMemberTriedItem {
    id: number;
    triedHistoryId: number;
    triedItem: string;
    feedback: JSON;
    triedMotivation: string;
    price: number;
    promotion: string;
    createdUserId: number;
    createdAt: Date;
    updatedAt: Date;
    triedEar: string;
}
