import { ObjectType } from 'type-graphql';
import { IMarketActivityInternalRegistration } from '@clinico/type-graphql-persistence/models/public/marketActivityInternalRegistration.model';
@ObjectType({ implements: IMarketActivityInternalRegistration })
export class MarketActivityInternalRegistration
    implements IMarketActivityInternalRegistration
{
    id: number;
    marketActivityId: number;
    departmentId: number;
    costCenterId: number;
    internalCount: number;
    estimatedCost: string;
    createdAt: string;
    updatedAt: string;
}
