import { ObjectType } from 'type-graphql';
import { IOrderPayment } from '@clinico/type-graphql-persistence/models/public/orderPayment.model';

@ObjectType({ implements: IOrderPayment })
export class OrderPayment implements IOrderPayment {
    id: number;
    orderId: number;
    date: Date;
    invoiceNo: string;
    receivedAmount: string;
    currencyId: number;
    remark: string;
    createdUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
