import { ObjectType } from 'type-graphql';
import { IRetail } from '@clinico/type-graphql-persistence/models/public/retail.model';

@ObjectType({ implements: IRetail })
export class Retail implements IRetail {
    id: number;
    materialPriceId: number;
    retailCategoryId: number;
    viewOrder: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
