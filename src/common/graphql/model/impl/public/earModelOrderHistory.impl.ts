import { ObjectType } from 'type-graphql';
import { IEarModelOrderHistory } from '@clinico/type-graphql-persistence/models/public/earModelOrderHistory.model';

@ObjectType({ implements: IEarModelOrderHistory })
export class EarModelOrderHistory implements IEarModelOrderHistory {
    id: number;
    earModelOrderId: number;
    status: number;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
