import { ObjectType } from 'type-graphql';
import { IRepairItem } from '@clinico/type-graphql-persistence/models/public/repairItem.model';

@ObjectType({ implements: IRepairItem })
export class RepairItem implements IRepairItem {
    id: number;
    repairId: number;
    repairWorkOrderCode: string;
    SN: string;
    materialCode: string;
    createdAt: Date;
    updatedAt: Date;
    repairReasonIds: JSON;
    repairOtherReason: string;
    materialId: number;
}
