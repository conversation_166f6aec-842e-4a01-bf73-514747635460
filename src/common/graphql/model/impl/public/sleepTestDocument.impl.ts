import { ObjectType } from 'type-graphql';
import { ISleepTestDocument } from '@clinico/type-graphql-persistence/models/public/sleepTestDocument.model';

@ObjectType({ implements: ISleepTestDocument })
export class SleepTestDocument implements ISleepTestDocument {
    id: number;
    sleepTestId: number;
    type: string;
    s3Key: string;
    name: string;
    description: string;
    extension: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
