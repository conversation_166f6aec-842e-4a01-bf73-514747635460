import { ObjectType } from 'type-graphql';
import { IMemberBonus } from '@clinico/type-graphql-persistence/models/public/memberBonus.model';

@ObjectType({ implements: IMemberBonus })
export class MemberBonus implements IMemberBonus {
    id: number;
    memberId: number;
    memberCode: string;
    type: string;
    orderId: string;
    bonus: number;
    updatedDate: Date;
    updatedTime: string;
    createdUserId: number;
    amount: number;
    storeId: number;
    departmentId: number;
    reason: string;
    remark: string;
    dataSource: string;
}
