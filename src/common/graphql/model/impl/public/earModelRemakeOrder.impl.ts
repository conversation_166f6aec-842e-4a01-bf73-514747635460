import { ObjectType } from 'type-graphql';
import { IEarModelRemakeOrder } from '@clinico/type-graphql-persistence/models/public/earModelRemakeOrder.model';

@ObjectType({ implements: IEarModelRemakeOrder })
export class EarModelRemakeOrder implements IEarModelRemakeOrder {
    id: number;
    sourceModelOrderId: number;
    remakeModelOrderId: number;
    ear: string;
    leftRemark: string;
    leftMethod: string;
    rightRemark: string;
    rightMethod: string;
    createdAt: Date;
    updatedAt: Date;
}
