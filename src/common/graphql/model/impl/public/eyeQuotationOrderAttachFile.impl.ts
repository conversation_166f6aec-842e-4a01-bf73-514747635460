import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderAttachFile } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderAttachFile.model';

@ObjectType({ implements: IEyeQuotationOrderAttachFile })
export class EyeQuotationOrderAttachFile
    implements IEyeQuotationOrderAttachFile
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeQuotationOrderId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
