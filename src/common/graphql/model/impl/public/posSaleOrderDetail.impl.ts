import { ObjectType } from 'type-graphql';
import { IPosSaleOrderDetail } from '@clinico/type-graphql-persistence/models/public/posSaleOrderDetail.model';

@ObjectType({ implements: IPosSaleOrderDetail })
export class PosSaleOrderDetail implements IPosSaleOrderDetail {
    id: number;
    saleOrderId: number;
    seq: number;
    assemblageCode: string;
    assemblageSeq: number;
    itemGroup: number;
    materialCode: string;
    qty: number;
    originalPrice: number;
    unit: string;
    amount: number;
    totalAmount: number;
    shippedQty: number;
    createdAt: Date;
    updatedAt: Date;
}
