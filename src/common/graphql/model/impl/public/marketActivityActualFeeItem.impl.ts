import { ObjectType } from 'type-graphql';
import { IMarketActivityActualFeeItem } from '@clinico/type-graphql-persistence/models/public/marketActivityActualFeeItem.model';

@ObjectType({ implements: IMarketActivityActualFeeItem })
export class MarketActivityActualFeeItem
    implements IMarketActivityActualFeeItem
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    marketActivityId: number;
    feeTypeId: number;
    expense: string;
    memo: string;
}
