import { ObjectType } from 'type-graphql';
import { IAppointmentContactHistory } from '@clinico/type-graphql-persistence/models/public/appointmentContactHistory.model';

@ObjectType({ implements: IAppointmentContactHistory })
export class AppointmentContactHistory implements IAppointmentContactHistory {
    id: number;
    appointmentId: number;
    date: Date;
    createdUserId: number;
    updatedUserId: number;
    remark: string;
    createdAt: Date;
    updatedAt: Date;
}
