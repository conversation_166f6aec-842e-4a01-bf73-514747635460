import { ObjectType } from 'type-graphql';
import { IStoreBusinessHoursPreset } from '@clinico/type-graphql-persistence/models/public/storeBusinessHoursPreset.model';

@ObjectType({ implements: IStoreBusinessHoursPreset })
export class StoreBusinessHoursPreset implements IStoreBusinessHoursPreset {
    id: number;
    storeId: number;
    storeBusinessHours: JSON;
    setDate: Date;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    isImport: boolean;
}
