import { ObjectType } from 'type-graphql';
import { IPointOfSaleMaterialSpecification } from '@clinico/type-graphql-persistence/models/public/pointOfSaleMaterialSpecification.model';

@ObjectType({ implements: IPointOfSaleMaterialSpecification })
export class PointOfSaleMaterialSpecification
    implements IPointOfSaleMaterialSpecification
{
    id: number;
    pointOfSaleMaterialId: number;
    name: string;
    isBusinessIdentification: boolean;
    multiple: number;
    multipleUnit: string;
    color: string;
    weight: string;
    weightUnit: number;
    length: string;
    width: string;
    height: string;
    lengthUnit: number;
    description: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
