import { ObjectType } from 'type-graphql';
import { IInvoice } from '@clinico/type-graphql-persistence/models/public/invoice.model';

@ObjectType({ implements: IInvoice })
export class Invoice implements IInvoice {
    id: number;
    code: string;
    storeId: number;
    storeInvoicePoolId: number;
    year: string;
    monthType: number;
    randomCode: string;
    isOpen: boolean;
    isInvalid: boolean;
    invalidReasonCode: string;
    invalidUserId: number;
    invalidAt: Date;
    isCancel: boolean;
    cancelReasonCode: string;
    cancelUserId: number;
    cancelAt: Date;
    isPrint: boolean;
    rePrintUserId: number;
    rePrintAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
