import { ObjectType } from 'type-graphql';
import { IOrderShippingItem } from '@clinico/type-graphql-persistence/models/public/orderShippingItem.model';

@ObjectType({ implements: IOrderShippingItem })
export class OrderShippingItem implements IOrderShippingItem {
    id: number;
    orderShippingId: number;
    orderId: number;
    materialId: number;
    materialCode: string;
    unit: string;
    qty: number;
    createdAt: Date;
    updatedAt: Date;
}
