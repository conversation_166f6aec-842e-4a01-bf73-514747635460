import { ObjectType } from 'type-graphql';
import { ICustomClosingTimeAllowRule } from '@clinico/type-graphql-persistence/models/public/customClosingTimeAllowRule.model';

@ObjectType({ implements: ICustomClosingTimeAllowRule })
export class CustomClosingTimeAllowRule implements ICustomClosingTimeAllowRule {
    id: number;
    customClosingTimeId: number;
    allowAllStores: boolean;
    companyId: number;
    zoneId: number;
    zone2Id: number;
    storeId: number;
    regionId: number;
}
