import { ObjectType } from 'type-graphql';
import { IStoreStaffMapping } from '@clinico/type-graphql-persistence/models/public/storeStaffMapping.model';

@ObjectType({ implements: IStoreStaffMapping })
export class StoreStaffMapping implements IStoreStaffMapping {
    id: number;
    userId: number;
    storeId: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
