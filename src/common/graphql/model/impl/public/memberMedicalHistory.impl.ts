import { ObjectType } from 'type-graphql';
import { IMemberMedicalHistory } from '@clinico/type-graphql-persistence/models/public/memberMedicalHistory.model';

@ObjectType({ implements: IMemberMedicalHistory })
export class MemberMedicalHistory implements IMemberMedicalHistory {
    id: number;
    memberId: number;
    hearingStatus: number;
    earPus: number[];
    earPain: number[];
    tinnitus: number[];
    dizzy: number[];
    hearNotClearly: number[];
    symptomRemark: string;
    earMedicalHistories: number[];
    earMedicalHistoryOthers: string;
    medicalHistories: number[];
    medicalHistoryOthers: string;
    isHearingAssistiveExperience: boolean;
    CILeft: boolean;
    CILeftBrand: string;
    CILeftModel: string;
    CIRight: boolean;
    CIRightBrand: string;
    CIRightModel: string;
    HALeft: boolean;
    HALeftBrand: string;
    HALeftModel: string;
    HALeftType: string;
    HARight: boolean;
    HARightBrand: string;
    HARightModel: string;
    HARightType: string;
    considerReasons: number[];
    considerReasonsOthers: string;
    createdAt: Date;
    updatedAt: Date;
    hearNotClearlyRemark: string;
    dizzyRemark: string;
    tinnitusRemark: string;
    earPainRemark: string;
    earPusRemark: string;
    earMedicalHistoriesNestedOptions: JSON;
    medicalHistoriesNestedOptions: JSON;
    earNotComfortable: number;
}
