import { ObjectType } from 'type-graphql';
import { IShiftSchedulerLeaf } from '@clinico/type-graphql-persistence/models/public/shiftSchedulerLeaf.model';

@ObjectType({ implements: IShiftSchedulerLeaf })
export class ShiftSchedulerLeaf implements IShiftSchedulerLeaf {
    id: number;
    leaveStartTime: string;
    leaveEndTime: string;
    remark: string;
    shiftSchedulerId: number;
    createdAt: Date;
    updatedAt: Date;
}
