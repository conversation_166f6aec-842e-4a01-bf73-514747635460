import { ObjectType } from 'type-graphql';
import { IAppointment } from '@clinico/type-graphql-persistence/models/public/appointment.model';

@ObjectType({ implements: IAppointment })
export class Appointment implements IAppointment {
    id: number;
    storeId: number;
    audiologistId: number;
    memberType: number;
    identity: number;
    date: Date;
    timeType: number;
    time: string;
    name: string;
    email: string;
    gender: string;
    mobile: string;
    memberId: number;
    isReminded: boolean;
    eventCode: string;
    status: number;
    createdUserId: number;
    updatedUserId: number;
    remark: string;
    dataSource: number;
    createdAt: Date;
    updatedAt: Date;
    endTime: string;
    createdDate: Date;
    updatedDate: Date;
    isSendSms: boolean;
    birthday: Date;
    cancelReasonType: number;
    isConfirmed: boolean;
    googleAdsName: string;
    regionId: number;
}
