import { ObjectType } from 'type-graphql';
import { IPosShipHistoryDetail } from '@clinico/type-graphql-persistence/models/public/posShipHistoryDetail.model';

@ObjectType({ implements: IPosShipHistoryDetail })
export class PosShipHistoryDetail implements IPosShipHistoryDetail {
    id: number;
    shipHistoryId: number;
    checkoutOrderItemId: number;
    shipQty: number;
    remainedQty: number;
    createdAt: Date;
    updatedAt: Date;
    saleOrderDetailId: number;
}
