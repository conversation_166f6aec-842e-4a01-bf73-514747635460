import { ObjectType } from 'type-graphql';
import { IPotentialCustomer } from '@clinico/type-graphql-persistence/models/public/potentialCustomer.model';

@ObjectType({ implements: IPotentialCustomer })
export class PotentialCustomer implements IPotentialCustomer {
    id: number;
    appointmentId: number;
    campaignId: number;
    name: string;
    gender: string;
    age: number;
    email: string;
    telephone: string;
    mobile: string;
    line: string;
    address: string;
    values: JSON;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    campaignOther: string;
    leftEarHearingDb: number;
    rightEarHearingDb: number;
    regionId: number;
}
