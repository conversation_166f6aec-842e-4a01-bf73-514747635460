import { ObjectType } from 'type-graphql';
import { IDepartment } from '@clinico/type-graphql-persistence/models/public/department.model';
import { CostCenter } from './costCenter.impl';

@ObjectType({ implements: IDepartment })
export class Department implements IDepartment {
    id: number;
    companyId: number;
    name: string;
    code: string;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    setOfBook: string;
    isActive: boolean;
    managerId: number;
    parentId: number;
    costCenterId: number;
}
