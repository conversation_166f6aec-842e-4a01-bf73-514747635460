import {
    createUnionType,
    Field,
    ID,
    ObjectType,
    registerEnumType,
} from 'type-graphql';
import { EnumNotificationType } from '@clinico/type-graphql-persistence/models/public/notification.model';
import { GraphQLJSON } from 'graphql-scalars';

/**
 * 訊息行為
 * NotificationActionComponent (觸發內部component)
 * NotificationActionLink （外部連結網址）
 * NotificationActionInnerPath （內部連結 不包含hostname）
 */
export enum EnumNotificationActionComponent {
    MeetingRoom = 'MeetingRoom',
    Appointment = 'Appointment',
    EarModel = 'EarModel',
    RepairOrder = 'RepairOrder',
}

registerEnumType(EnumNotificationActionComponent, {
    name: 'EnumNotificationActionComponent',
    description: '訊息 - 內部Component類型',
    valuesConfig: {
        MeetingRoom: { description: '會議室預約' },
        Appointment: { description: '耳科預約' },
        EarModel: { description: '耳模訂製' },
        RepairOrder: { description: '耳科維修' },
    },
});
@ObjectType({ description: '訊息行為 - 內部Component' })
export class NotificationActionComponent {
    @Field((type) => EnumNotificationActionComponent, { nullable: true })
    key?: EnumNotificationActionComponent;

    @Field((type) => GraphQLJSON, { nullable: true })
    payload?: JSON;
}

@ObjectType({ description: '訊息行為 - 連外網站' })
export class NotificationActionLink {
    @Field({ nullable: true })
    url?: string;
}

@ObjectType({ description: '訊息行為 - 內部連結(不包含HostNmae)' })
export class NotificationActionInnerPath {
    @Field({ nullable: true })
    path?: string;
}

export const SearchResultUnion = createUnionType({
    name: 'NotificationAction',
    types: () => [
        NotificationActionLink,
        NotificationActionInnerPath,
        NotificationActionComponent,
    ],
    resolveType: (values) => {
        if ('url' in values) {
            return NotificationActionLink;
        }
        if ('path' in values) {
            return NotificationActionInnerPath;
        }
        if ('key' in values) {
            return NotificationActionComponent;
        }
        return undefined;
    },
});

@ObjectType()
export class Notification {
    @Field((type) => ID)
    id: number;

    @Field((type) => ID, { nullable: true })
    userId: number;

    @Field({ nullable: true })
    message: string;

    @Field((type) => EnumNotificationType, { nullable: true })
    type: EnumNotificationType;

    @Field((type) => SearchResultUnion, { nullable: true })
    values: JSON;

    @Field()
    isRead: boolean;

    @Field()
    createdAt: Date;

    @Field()
    updatedAt: Date;
}
