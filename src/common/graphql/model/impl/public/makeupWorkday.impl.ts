import { ObjectType } from 'type-graphql';
import { IMakeupWorkday } from '@clinico/type-graphql-persistence/models/public/makeupWorkday.model';

@ObjectType({ implements: IMakeupWorkday })
export class MakeupWorkday implements IMakeupWorkday {
    id: number;
    date: Date;
    name: string;
    description: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    isStoreIgnore: boolean;
    regionId: number;
}
