import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderProductItem } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderProductItem.model';
import { Authorized } from '@/common/decorators/authorized';

@ObjectType({ implements: IEyeQuotationOrderProductItem })
export class EyeQuotationOrderProductItem
    implements IEyeQuotationOrderProductItem
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeQuotationOrderId: number;
    eyeQuotationOrderProductId: number;
    eyeProductItemId: number;
    eyeQuotationOrderPromotionId: number;
    eyePromotionAddonProductItemId: number;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    materialSellingPrice: string;
    materialSellingCurrencyId: number;
    unitQty: number;
    qty: number;
    exchangeRate: string;
    unitPrice: string;
    taxRate: string;
    unitPriceVat: string;
    description: string;
    discountedSellingPrice: string;
    discountPrice: string;
    warrantyMonths: number;
    warrantyPeriodTypeId: number;
    customQuotationPrice: string;
    @Authorized('material.cost_price', 0)
    materialCostPrice: string;
    materialCostCurrencyId: number;
    materialInventoryPrice: string;
    materialInventoryCurrencyId: number;
    materialOptionSalePrice: string;
    materialOptionSaleCurrencyId: number;
    materialCleanPrice: string;
    materialCleanCurrencyId: number;
    materialBundleSalePrice: string;
    materialBundleSaleCurrencyId: number;
}
