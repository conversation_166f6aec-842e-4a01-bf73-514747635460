import { ObjectType } from 'type-graphql';
import { IServiceItem } from '@clinico/type-graphql-persistence/models/public/serviceItem.model';

@ObjectType({ implements: IServiceItem })
export class ServiceItem implements IServiceItem {
    id: number;
    type: number;
    code: string;
    name: string;
    appointmentOnly: boolean;
    disabled: boolean;
    isSystem: boolean;
    hasRecommend: boolean;
    regionId: number;
}
