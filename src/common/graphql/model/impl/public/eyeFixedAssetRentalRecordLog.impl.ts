import { ObjectType } from 'type-graphql';
import {
    EnumEyeFixedAssetRentalRecordLogApprovalStatus,
    IEyeFixedAssetRentalRecordLog,
} from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetRentalRecordLog.model';

@ObjectType({ implements: IEyeFixedAssetRentalRecordLog })
export class EyeFixedAssetRentalRecordLog
    implements IEyeFixedAssetRentalRecordLog
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    eyeServiceOrderId: number;
    sn: string;
    newSn: string;
    date1: Date;
    date2: Date;
    newDate1: Date;
    newDate2: Date;
    approvalStatus: EnumEyeFixedAssetRentalRecordLogApprovalStatus;
    bpmInstanceId: string;
    memo: string;
}
