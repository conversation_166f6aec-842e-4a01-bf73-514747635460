import { ObjectType } from 'type-graphql';
import { IMemberContact } from '@clinico/type-graphql-persistence/models/public/memberContact.model';

@ObjectType({ implements: IMemberContact })
export class MemberContact implements IMemberContact {
    id: number;
    memberId: number;
    memberCode: string;
    seq: number;
    name: string;
    relationship: string;
    cellPhone: string;
    contactPhone: string;
    phone: string;
    email: string;
    isQuietEmail: string;
    birthday: Date;
    remark: string;
    societyCode: string;
    storeId: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
