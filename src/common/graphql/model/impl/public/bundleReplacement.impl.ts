import { ObjectType } from 'type-graphql';
import { IBundleReplacement } from '@clinico/type-graphql-persistence/models/public/bundleReplacement.model';

@ObjectType({ implements: IBundleReplacement })
export class BundleReplacement implements IBundleReplacement {
    id: number;
    bundleDetailId: number;
    materialPriceId: number;
    premiumPrice: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    qty: number;
}
