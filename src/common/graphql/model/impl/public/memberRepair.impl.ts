import { ObjectType } from 'type-graphql';
import { IMemberRepair } from '@clinico/type-graphql-persistence/models/public/memberRepair.model';

@ObjectType({ implements: IMemberRepair })
export class MemberRepair implements IMemberRepair {
    id: number;
    memberId: number;
    memberCode: string;
    orderType: string;
    orderId: string;
    orderDate: Date;
    checkedDate: Date;
    wpcOrderId: string;
    wpcOrderSeq: number;
    sn: string;
    itemName: string;
    itemCode: string;
    createUserId: number;
    createDepartmentId: number;
    expectedDate: Date;
    completedDate: Date;
    repairUserId: number;
    repairDepartmentId: number;
    repairRemark: string;
}
