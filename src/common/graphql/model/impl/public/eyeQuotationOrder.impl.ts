import { ObjectType } from 'type-graphql';
import {
    EnumEyeQuotationOrderOfficialSealStatus,
    EnumEyeQuotationOrderStatus,
    EnumWarrantyBuyType,
    IEyeQuotationOrder,
} from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrder.model';
import { Authorized } from '@/common/decorators/authorized';

@ObjectType({ implements: IEyeQuotationOrder })
export class EyeQuotationOrder implements IEyeQuotationOrder {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    companyId: number;
    eyeServiceOrderId: number;
    /** @deprecated 「商機」改用「多對一關聯」 */
    businessId: number;
    bpmInstanceId: string;
    code: string;
    orderCode: string;
    invoicingCustomerId: number;
    customerId: number;
    contactPerson: string;
    contactPhone: string;
    provinceId: number;
    cityId: number;
    districtId: number;
    address: string;
    deptId: number;
    userId: number;
    currencyId: number;
    standardAmount: string;
    localCurrencyId: number;
    localStandardAmount: string;
    discountRate: string;
    discountAmount: string;
    extraDiscountAmount: string;
    realAmount: string;
    realDiscountAmount: string;
    realDiscountRate: string;
    taxRate: string;
    exchangeRate: string;
    untaxedAmount: string;
    @Authorized('quotation.commission_amount', 0)
    commissionAmount: string;
    discountRateWithoutCommission: string;
    warrantyBuyType: EnumWarrantyBuyType;
    warrantyMonths: number;
    warrantyPeriodTypeId: number;
    creditPeriodId: number;
    expectDeliveryDate: Date;
    expectPaymentDate: Date;
    status: EnumEyeQuotationOrderStatus;
    description: string;
    biddingPrice: string;
    eyeQuotationOrderTypeId: number;
    onlyMaterials: boolean;
    bpmOfficialSealInstanceId: string;
    officialSealStatus: EnumEyeQuotationOrderOfficialSealStatus;
    costCenterId: number;
    @Authorized('quotation.commission_rate', 0)
    commissionRate: string;
    financialCompanyId: number;
    eyeQuotationOrderCommissionTypeId: number;
    recommendedAmount: string;
    warehouseId: number;
    salesTeamUnitId: number;
    paymentInfo: string;
    grossMargin: string;
    inventoryPrice: string;
}
