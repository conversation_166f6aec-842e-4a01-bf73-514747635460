import { ObjectType } from 'type-graphql';
import { IOrder } from '@clinico/type-graphql-persistence/models/public/order.model';

@ObjectType({ implements: IOrder })
export class Order implements IOrder {
    id: number;
    code: string;
    companyId: number;
    storeId: number;
    memberId: number;
    managerId: number;
    status: string;
    canceledReason: string;
    totalAmount: string;
    discountAmount: string;
    amount: string;
    currencyId: number;
    remark: string;
    isAuditable: boolean;
    auditableStartDate: Date;
    isAuditSubmitable: boolean;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    createdAt: Date;
    updatedAt: Date;
}
