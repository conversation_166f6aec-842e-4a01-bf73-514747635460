import { ObjectType } from 'type-graphql';
import { IBundle } from '@clinico/type-graphql-persistence/models/public/bundle.model';

@ObjectType({ implements: IBundle })
export class Bundle implements IBundle {
    id: number;
    companyId: number;
    code: string;
    name: string;
    startDate: Date;
    endDate: Date;
    remark: string;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    createdAt: Date;
    updatedAt: Date;
    totalPrice: number;
}
