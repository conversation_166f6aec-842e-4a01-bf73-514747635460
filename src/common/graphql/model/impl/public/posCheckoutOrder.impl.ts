import { ObjectType } from 'type-graphql';
import { IPosCheckoutOrder } from '@clinico/type-graphql-persistence/models/public/posCheckoutOrder.model';

@ObjectType({ implements: IPosCheckoutOrder })
export class PosCheckoutOrder implements IPosCheckoutOrder {
    id: number;
    companyId: number;
    storeId: number;
    saleOrderId: number;
    orderCode: string;
    saleDate: Date;
    payStauts: number;
    depositAmount: number;
    paidAmount: number;
    totalAmount: number;
    isPrepay: boolean;
    closedDate: Date;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    checkoutOrderId: number;
}
