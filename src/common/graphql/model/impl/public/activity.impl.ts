import { ObjectType } from 'type-graphql';
import { IActivity } from '@clinico/type-graphql-persistence/models/public/activity.model';

@ObjectType({ implements: IActivity })
export class Activity implements IActivity {
    id: number;
    activityTypeId: number;
    title: string;
    content: string;
    startDate: Date;
    endDate: Date;
    meetingStoreId: number;
    meetingOtherLocation: string;
    status: number;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
