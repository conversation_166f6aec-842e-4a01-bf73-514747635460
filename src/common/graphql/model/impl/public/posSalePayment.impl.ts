import { ObjectType } from 'type-graphql';
import { IPosSalePayment } from '@clinico/type-graphql-persistence/models/public/posSalePayment.model';

@ObjectType({ implements: IPosSalePayment })
export class PosSalePayment implements IPosSalePayment {
    id: number;
    checkoutOrderId: number;
    paymentId: number;
    seq: number;
    amount: number;
    changeAmount: number;
    creditCardNo: string;
    createdAt: Date;
    updatedAt: Date;
}
