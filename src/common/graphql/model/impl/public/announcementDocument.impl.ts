import { ObjectType } from 'type-graphql';
import { IAnnouncementDocument } from '@clinico/type-graphql-persistence/models/public/announcementDocument.model';

@ObjectType({ implements: IAnnouncementDocument })
export class AnnouncementDocument implements IAnnouncementDocument {
    id: number;
    announcementId: number;
    s3Key: string;
    name: string;
    description: string;
    extension: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
}
