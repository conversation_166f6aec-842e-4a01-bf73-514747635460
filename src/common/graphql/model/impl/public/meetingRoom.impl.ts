import { ObjectType } from 'type-graphql';
import { IMeetingRoom } from '@clinico/type-graphql-persistence/models/public/meetingRoom.model';

@ObjectType({ implements: IMeetingRoom })
export class MeetingRoom implements IMeetingRoom {
    id: number;
    type: string;
    name: string;
    cityId: number;
    address: string;
    capacity: number;
    isProjector: boolean;
    createdAt: Date;
    updatedAt: Date;
}
