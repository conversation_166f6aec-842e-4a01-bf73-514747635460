import { ObjectType } from 'type-graphql';
import { IEyeQuotationOrderPromotion } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderPromotion.model';

@ObjectType({ implements: IEyeQuotationOrderPromotion })
export class EyeQuotationOrderPromotion implements IEyeQuotationOrderPromotion {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeQuotationOrderId: number;
    eyePromotionId: number;
    name: string;
    currencyId: number;
    discountRate: string;
    discountAmount: string;
    description: string;
}
