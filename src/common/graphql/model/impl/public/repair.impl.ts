import { ObjectType } from 'type-graphql';
import { IRepair } from '@clinico/type-graphql-persistence/models/public/repair.model';

@ObjectType({ implements: IRepair })
export class Repair implements IRepair {
    id: number;
    companyId: number;
    memberId: number;
    memberCode: string;
    userId: number;
    repairUserId: number;
    repairAreaCode: string;
    repairOrderCode: string;
    receivedDate: Date;
    receivedStoreId: number;
    pickupStoreId: number;
    status: string;
    createdUserId: number;
    updatedUserId: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
}
