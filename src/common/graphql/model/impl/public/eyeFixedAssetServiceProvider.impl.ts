import { ObjectType } from 'type-graphql';
import { IEyeFixedAssetServiceProvider } from '@clinico/type-graphql-persistence/models/public/eyeFixedAssetServiceProvider.model';
import { EnumEyeFixedAssetServiceProviderCode } from '@clinico/typeorm-persistence/models/public/eyeFixedAssetServiceProvider.model';

@ObjectType({ implements: IEyeFixedAssetServiceProvider })
export class EyeFixedAssetServiceProvider
    implements IEyeFixedAssetServiceProvider
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    name: string;
    code: EnumEyeFixedAssetServiceProviderCode;
    regionId: number;
}
