import { ObjectType } from 'type-graphql';
import { IMaterialExtraProperty } from '@clinico/type-graphql-persistence/models/inventory/materialExtraProperty.model';

@ObjectType({ implements: IMaterialExtraProperty })
export class MaterialExtraProperty implements IMaterialExtraProperty {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    materialId: number;
    warrantyMonths: number;
    earType: number;
    isWarranty: boolean;
    isExtendedWarranty: boolean;
    isSubscriptionMainProduct: boolean;
}
