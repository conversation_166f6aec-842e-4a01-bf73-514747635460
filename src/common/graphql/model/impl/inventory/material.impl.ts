import { Field, ObjectType } from 'type-graphql';
import {
    EnumMaterialStatus,
    IMaterial,
} from '@clinico/type-graphql-persistence/models/inventory/material.model';
import { MaterialsMedicalDeviceClassification } from './materialsMedicalDeviceClassification.impl';
import { SyncSource } from './syncSource.impl';
import { Authorized } from '@/common/decorators/authorized';
import { MaterialsToProductLine } from '@clinico/typeorm-persistence/models/inventory/materialsToProductLine.model';
import { CustomersConsumablePrice } from '../salesRepWorkstation/customersConsumablePrice.impl';

@ObjectType({ implements: IMaterial })
export class Material implements IMaterial {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    regionId: number;
    companyId: number;
    supplierId: number;
    materialTypeId: number;
    materialRepairTypeId: number;
    code: string;
    name: string;
    name2: string;
    brand: string;
    model: string;
    spec: string;
    color: string;
    unit: string;
    countingUnitId: number;
    taxRate: string;
    salePrice: string;
    saleCurrencyId: number;
    @Authorized('material.cost_price', 0)
    costPrice: string;
    costCurrencyId: number;
    internalWarrantyPrice: string;
    internalWarrantyCurrencyId: number;
    externalWarrantyPrice: string;
    externalWarrantyCurrencyId: number;
    warrantyPeriodTypeId: number;
    status: EnumMaterialStatus;
    syncSourceId: number;
    syncCode: string;
    createdUserId: number;
    updatedUserId: number;
    registerName: string;
    registerExpirationDate: Date;
    optionSalePrice: string;
    optionSaleCurrencyId: number;
    // 最小採買數量
    minimumOrderQuantity: number;
    registerCode: string;
    registerModel: string;
    inventoryPrice: string;
    bundleSalePrice: string;
    bundleSaleCurrencyId: number;
    inventoryUntaxedPrice: string;

    // relations
    materialsMedicalDeviceClassifications: MaterialsMedicalDeviceClassification[];

    materialsToProductLines: MaterialsToProductLine[];

    customersConsumablePrices: CustomersConsumablePrice[];

    @Field((returns) => SyncSource, {
        nullable: true,
        description: '資料來源',
    })
    syncSource: SyncSource | null;
}
