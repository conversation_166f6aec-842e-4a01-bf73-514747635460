import { ObjectType } from 'type-graphql';
import { IMaterialProperty } from '@clinico/type-graphql-persistence/models/inventory/materialProperty.model';
import { MaterialsProperty } from './materialsProperty.impl';

@ObjectType({ implements: IMaterialProperty })
export class MaterialProperty implements IMaterialProperty {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    companyId: number;
    code: string;
    name: string;

    // relations
    materialsProperties: MaterialsProperty[];
}
