import { ObjectType } from 'type-graphql';
import { ISupplier } from '@clinico/type-graphql-persistence/models/inventory/supplier.model';

@ObjectType({ implements: ISupplier })
export class Supplier implements ISupplier {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    companyId: number;
    code: string;
    name: string;
    name2: string;
}
