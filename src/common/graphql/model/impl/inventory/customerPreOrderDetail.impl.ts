import { ObjectType } from 'type-graphql';
import { ICustomerPreOrderDetail } from '@clinico/type-graphql-persistence/models/inventory/customerPreOrderDetail.model';

@ObjectType({ implements: ICustomerPreOrderDetail })
export class CustomerPreOrderDetail implements ICustomerPreOrderDetail {
    id: number;
    customerPreOrderId: number;
    materialId: number;
    currencyId: number;
    price: string;
    qty: number;
    total: string;
}
