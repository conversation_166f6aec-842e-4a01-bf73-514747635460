import { ObjectType } from 'type-graphql';
import { IMaterialImage } from '@clinico/type-graphql-persistence/models/inventory/materialImage.model';

@ObjectType({ implements: IMaterialImage })
export class MaterialImage implements IMaterialImage {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    materialId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
