import { ObjectType } from 'type-graphql';
import { ICustomerPreOrder } from '@clinico/type-graphql-persistence/models/inventory/customerPreOrder.model';

@ObjectType({ implements: ICustomerPreOrder })
export class CustomerPreOrder implements ICustomerPreOrder {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    code: string;
    status: number;
    customerId: number;
    name: string;
    provinceId: number;
    cityId: number;
    districtId: number;
    shippingAddress: string;
    phone: string;
    mobile: string;
    shippingMethodId: number;
    shippingDate: Date;
    estimatedArrivalDate: Date;
    arrivalDate: Date;
    trackingCode: string;
    currencyId: number;
    subtotal: string;
    taxRate: string;
    discount: string;
    total: string;
    memo: string;
}
