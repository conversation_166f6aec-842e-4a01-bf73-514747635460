import { ObjectType } from 'type-graphql';
import { ISupplierProperty } from '@clinico/type-graphql-persistence/models/inventory/supplierProperty.model';
import { SuppliersProperty } from './suppliersProperty.impl';

@ObjectType({ implements: ISupplierProperty })
export class SupplierProperty implements ISupplierProperty {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    regionId: number;
    companyId: number;
    code: string;
    name: string;

    // relations
    suppliersProperties?: SuppliersProperty[];
}
