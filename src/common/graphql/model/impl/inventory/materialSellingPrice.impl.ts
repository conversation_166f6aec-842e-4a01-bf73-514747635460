import { ObjectType } from 'type-graphql';
import { IMaterialSellingPrice } from '@clinico/type-graphql-persistence/models/inventory/materialSellingPrice.model';

@ObjectType({ implements: IMaterialSellingPrice })
export class MaterialSellingPrice implements IMaterialSellingPrice {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    isActive: boolean;
    materialId: number;
    currencyId: number;
    price: string;
    date1: Date;
    date2: Date;
    priority: number;
}
