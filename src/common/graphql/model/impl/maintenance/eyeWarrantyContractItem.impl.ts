import { ObjectType } from 'type-graphql';
import { IEyeWarrantyContractItem } from '@clinico/type-graphql-persistence/models/maintenance/eyeWarrantyContractItem.model';

@ObjectType({ implements: IEyeWarrantyContractItem })
export class EyeWarrantyContractItem implements IEyeWarrantyContractItem {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeWarrantyContractId: number;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    sn: string;
    udi: string;
    qty: number;
    memo: string;
}
