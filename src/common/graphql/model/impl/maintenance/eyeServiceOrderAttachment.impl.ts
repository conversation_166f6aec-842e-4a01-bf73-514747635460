import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderAttachment } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderAttachment.model';

@ObjectType({ implements: IEyeServiceOrderAttachment })
export class EyeServiceOrderAttachment implements IEyeServiceOrderAttachment {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
