import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderWorkDiaryCheckIn } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderWorkDiaryCheckIn.model';

@ObjectType({ implements: IEyeServiceOrderWorkDiaryCheckIn })
export class EyeServiceOrderWorkDiaryCheckIn
    implements IEyeServiceOrderWorkDiaryCheckIn
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderWorkDiaryId: number;
    lat: number;
    lng: number;
    content: string;
}
