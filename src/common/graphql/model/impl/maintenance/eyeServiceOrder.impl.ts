import { ObjectType } from 'type-graphql';
import {
    EnumEyeServiceOrderPriority,
    EnumEyeServiceOrderStatus,
    IEyeServiceOrder,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrder.model';
import { EnumEyeServiceOrderApprovalStatus } from '@clinico/typeorm-persistence/models/maintenance/eyeServiceOrder.model';

@ObjectType({ implements: IEyeServiceOrder })
export class EyeServiceOrder implements IEyeServiceOrder {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    code: string;
    eyeWarrantyContractId: number;
    eyeServiceOrderTypeId: number;
    regionId: number;
    companyId: number;
    userId: number;
    customerId: number;
    contactPerson: string;
    contactPhone: string;
    priority: EnumEyeServiceOrderPriority;
    estimatedDate: Date;
    description: string;
    memo: string;
    status: EnumEyeServiceOrderStatus;
    provinceId: number;
    cityId: number;
    districtId: number;
    address: string;
    assigneeDeptId: number;
    assigneeUserId: number;
    approverUserId: number;
    businessId: number;
    eyeFixedAssetRentalObjectId: number;
    dispatcherUserId: number;
    costCenterId: number;
    approvalStatus: EnumEyeServiceOrderApprovalStatus;
    eyeFixedAssetRentalGoalId: number;
    bpmInstanceId: string;
    installationorderCode: string;
    installationCustomerId: number;
    installationCustomerEyeWarrantyContractId: number;
    installationCustomerContactPerson: string;
    installationCustomerContactPhone: string;
    installationCustomerAddress: string;
    satisfactionContactPerson: string;
    satisfactionContactPhone: string;
    satisfactionLevel: number;
    satisfactionFeedback: string;
    ratingServiceEngineer: number;
    equipmentRating: number;
    servicePeriod: number;
    isLastService: boolean;
    completionUpdateDate: Date;
}
