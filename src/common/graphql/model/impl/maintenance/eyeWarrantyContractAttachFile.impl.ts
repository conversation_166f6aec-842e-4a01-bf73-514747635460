import { ObjectType } from 'type-graphql';
import { IEyeWarrantyContractAttachFile } from '@clinico/type-graphql-persistence/models/maintenance/eyeWarrantyContractAttachFile.model';

@ObjectType({ implements: IEyeWarrantyContractAttachFile })
export class EyeWarrantyContractAttachFile
    implements IEyeWarrantyContractAttachFile
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeWarrantyContractId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
