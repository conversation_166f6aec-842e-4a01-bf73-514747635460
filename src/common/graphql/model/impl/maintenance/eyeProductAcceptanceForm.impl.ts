import { ObjectType } from 'type-graphql';
import {
    IEyeProductAcceptanceForm,
    EnumProductAcceptanceFormStatus,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeProductAcceptanceForm.model';

@ObjectType({ implements: IEyeProductAcceptanceForm })
export class EyeProductAcceptanceForm implements IEyeProductAcceptanceForm {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeProductId: number;
    version: string;
    fields: JSON;
    date1: Date;
    date2: Date;
    status: EnumProductAcceptanceFormStatus;
}
