import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderItem } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderItem.model';

@ObjectType({ implements: IEyeServiceOrderItem })
export class EyeServiceOrderItem implements IEyeServiceOrderItem {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    sn: string;
    udi: string;
    qty: number;
    memo: string;
}
