import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderType } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderType.model';
import { EnumEyeServiceOrderTypeCode } from '@clinico/typeorm-persistence/models/maintenance/eyeServiceOrderType.model';

@ObjectType({ implements: IEyeServiceOrderType })
export class EyeServiceOrderType implements IEyeServiceOrderType {
    id: number;
    name: string;
    viewOrder: number;
    code: EnumEyeServiceOrderTypeCode;
}
