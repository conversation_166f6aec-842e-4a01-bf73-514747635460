import { ObjectType } from 'type-graphql';
import {
    EnumEyeServiceOrderWorkDiaryMachineFlowCode,
    IEyeServiceOrderWorkDiaryMachineFlow,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderWorkDiaryMachineFlow.model';

@ObjectType({ implements: IEyeServiceOrderWorkDiaryMachineFlow })
export class EyeServiceOrderWorkDiaryMachineFlow
    implements IEyeServiceOrderWorkDiaryMachineFlow
{
    id: number;
    name: string;
    regionId: number;
    code: EnumEyeServiceOrderWorkDiaryMachineFlowCode;
}
