import { Field, ObjectType } from 'type-graphql';
import {
    EnumEyeServiceOrderExtensionApprovalStatus,
    IEyeServiceOrderExtension,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderExtension.model';

@ObjectType({ implements: IEyeServiceOrderExtension })
export class EyeServiceOrderExtension implements IEyeServiceOrderExtension {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeServiceOrderId: number;
    extendDate1: Date;
    extendDate2: Date;
    memo: string;
    approvalStatus: EnumEyeServiceOrderExtensionApprovalStatus;
    bpmInstanceId: string;
}
