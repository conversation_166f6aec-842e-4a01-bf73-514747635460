import { ObjectType } from 'type-graphql';
import {
    EnumEyeServiceOrderWorkDiaryTypeCode,
    IEyeServiceOrderWorkDiaryType,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderWorkDiaryType.model';

@ObjectType({ implements: IEyeServiceOrderWorkDiaryType })
export class EyeServiceOrderWorkDiaryType
    implements IEyeServiceOrderWorkDiaryType
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    code: EnumEyeServiceOrderWorkDiaryTypeCode;
}
