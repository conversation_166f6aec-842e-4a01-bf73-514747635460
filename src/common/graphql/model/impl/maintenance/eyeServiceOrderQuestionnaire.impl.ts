import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderQuestionnaire } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderQuestionnaire.model';

@ObjectType({ implements: IEyeServiceOrderQuestionnaire })
export class EyeServiceOrderQuestionnaire
    implements IEyeServiceOrderQuestionnaire
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    questionnaireId: number;
    answers: JSON;
    score: number;
    status: number;
}
