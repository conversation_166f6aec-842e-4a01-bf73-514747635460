import { ObjectType } from 'type-graphql';
import {
    IQuestionnaire,
    EnumQuestionnaireStatus,
} from '@clinico/type-graphql-persistence/models/maintenance/questionnaire.model';

@ObjectType({ implements: IQuestionnaire })
export class Questionnaire implements IQuestionnaire {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    typeId: number;
    name: string;
    fields: JSON;
    date1: Date;
    date2: Date;
    status: EnumQuestionnaireStatus;
}
