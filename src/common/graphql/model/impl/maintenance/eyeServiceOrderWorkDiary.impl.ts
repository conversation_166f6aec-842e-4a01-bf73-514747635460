import { ObjectType } from 'type-graphql';
import {
    EnumEyeServiceOrderWorkDiaryStatus,
    IEyeServiceOrderWorkDiary,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderWorkDiary.model';
import { EyeServiceOrderWorkDiaryUser } from './eyeServiceOrderWorkDiaryUser.impl';
import { EyeServiceOrderWorkDiaryItem } from './eyeServiceOrderWorkDiaryItem.impl';
import { EyeServiceOrderWorkDiaryType } from './eyeServiceOrderWorkDiaryType.impl';

@ObjectType({ implements: IEyeServiceOrderWorkDiary })
export class EyeServiceOrderWorkDiary implements IEyeServiceOrderWorkDiary {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    eyeFixedAssetRentalRecordId: number;
    eyeServiceOrderWorkDiaryTypeId: number;
    regionId: number;
    companyId: number;
    status: EnumEyeServiceOrderWorkDiaryStatus;
    date: Date;
    hours: number;
    remark: string;
    estimatedDate: Date;
    estimatedHours: number;
    completionDate: Date;
    eyeServiceOrderWorkDiaryMachineFlowId: number;
    logisticsSupplierId: number;
    logisticsNumber: string;
    satisfactionContactPeopleId: number;
    contactPeopleId: number;

    // relations
    eyeServiceOrderWorkDiaryUsers: EyeServiceOrderWorkDiaryUser[];
    eyeServiceOrderWorkDiaryItems: EyeServiceOrderWorkDiaryItem[];
    eyeServiceOrderWorkDiaryType: EyeServiceOrderWorkDiaryType;
}
