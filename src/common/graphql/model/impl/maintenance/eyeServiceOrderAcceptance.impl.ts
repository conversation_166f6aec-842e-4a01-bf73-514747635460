import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderAcceptance } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderAcceptance.model';

@ObjectType({ implements: IEyeServiceOrderAcceptance })
export class EyeServiceOrderAcceptance implements IEyeServiceOrderAcceptance {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    eyeServiceOrderItemId: number;
    eyeProductAcceptanceFormId: number;
    name: string;
    answers: JSON;
    extension: string;
    s3Key: string;
    memo: string;
}
