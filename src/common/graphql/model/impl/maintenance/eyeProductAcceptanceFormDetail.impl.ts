import { ObjectType } from 'type-graphql';
import { IEyeProductAcceptanceFormDetail } from '@clinico/type-graphql-persistence/models/maintenance/eyeProductAcceptanceFormDetail.model';

@ObjectType({ implements: IEyeProductAcceptanceFormDetail })
export class EyeProductAcceptanceFormDetail
    implements IEyeProductAcceptanceFormDetail
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    typeId: number;
    name: string;
    options: JSON;
    required: boolean;
    description: string;
}
