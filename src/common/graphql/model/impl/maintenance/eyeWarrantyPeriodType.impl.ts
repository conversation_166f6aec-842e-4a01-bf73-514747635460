import { ObjectType } from 'type-graphql';
import {
    EnumEyeWarrantyPeriodTypeUnit,
    IEyeWarrantyPeriodType,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeWarrantyPeriodType.model';

@ObjectType({ implements: IEyeWarrantyPeriodType })
export class EyeWarrantyPeriodType implements IEyeWarrantyPeriodType {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    code: string;
    name: string;
    value: number;
    unit: EnumEyeWarrantyPeriodTypeUnit;
}
