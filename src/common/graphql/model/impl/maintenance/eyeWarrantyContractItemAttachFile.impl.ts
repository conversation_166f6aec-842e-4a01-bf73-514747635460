import { ObjectType } from 'type-graphql';
import { IEyeWarrantyContractItemAttachFile } from '@clinico/type-graphql-persistence/models/maintenance/eyeWarrantyContractItemAttachFile.model';

@ObjectType({ implements: IEyeWarrantyContractItemAttachFile })
export class EyeWarrantyContractItemAttachFile
    implements IEyeWarrantyContractItemAttachFile
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeWarrantyContractItemId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
