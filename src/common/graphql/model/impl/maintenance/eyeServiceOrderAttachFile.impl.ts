import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderAttachFile } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderAttachFile.model';

@ObjectType({ implements: IEyeServiceOrderAttachFile })
export class EyeServiceOrderAttachFile implements IEyeServiceOrderAttachFile {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
