import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderWorkDiaryItem } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderWorkDiaryItem.model';

@ObjectType({ implements: IEyeServiceOrderWorkDiaryItem })
export class EyeServiceOrderWorkDiaryItem
    implements IEyeServiceOrderWorkDiaryItem
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    eyeServiceOrderWorkDiaryId: number;
    materialId: number;
    materialCode: string;
    materialModel: string;
    materialSpec: string;
    materialUnit: string;
    materialName: string;
    sn: string;
}
