import { ObjectType } from 'type-graphql';
import {
    EnumEyeWarrantyContractStatus,
    IEyeWarrantyContract,
    EnumEyeWarrantyContractWarrantyBuyType,
} from '@clinico/type-graphql-persistence/models/maintenance/eyeWarrantyContract.model';
import { EyeWarrantyContractItem } from './eyeWarrantyContractItem.impl';

@ObjectType({ implements: IEyeWarrantyContract })
export class EyeWarrantyContract implements IEyeWarrantyContract {
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    regionId: number;
    companyId: number;
    code: string;
    approvalCode: string;
    orderCode: string;
    customerId: number;
    contactPerson: string;
    contactPhone: string;
    provinceId: number;
    cityId: number;
    districtId: number;
    address: string;
    warrantyMonths: number;
    warrantyPeriodTypeId: number;
    warrantyDate1: Date;
    warrantyDate2: Date;
    deptId: number;
    userId: number;
    description: string;
    memo: string;
    status: EnumEyeWarrantyContractStatus;
    warrantyBuyType: EnumEyeWarrantyContractWarrantyBuyType;

    // relations
    eyeWarrantyContractItems?: EyeWarrantyContractItem[];
}
