import { ObjectType } from 'type-graphql';
import { IEyeServiceOrderWorkDiaryAttachFile } from '@clinico/type-graphql-persistence/models/maintenance/eyeServiceOrderWorkDiaryAttachFile.model';

@ObjectType({ implements: IEyeServiceOrderWorkDiaryAttachFile })
export class EyeServiceOrderWorkDiaryAttachFile
    implements IEyeServiceOrderWorkDiaryAttachFile
{
    id: number;
    createdAt: Date;
    updatedAt: Date;
    createdUserId: number;
    updatedUserId: number;
    eyeServiceOrderWorkDiaryId: number;
    name: string;
    extension: string;
    s3Key: string;
    memo: string;
}
