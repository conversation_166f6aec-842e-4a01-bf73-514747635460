import { Helpers } from '@clinico/clinico-node-framework';
import * as Sentry from '@sentry/node';
import { MiddlewareFn } from 'type-graphql';
import Koa from 'koa';
import { Middleware, KoaMiddlewareInterface } from 'routing-controllers';
import {
    BaseGraphQLError,
    BaseGRPCError,
    BaseHttpError,
} from '@clinico/base-error';
import { Context as MaliCtx } from 'mali';
const { version } = require('../../../package.json');

// Graphql
export const GraphQLErrorInterceptor: MiddlewareFn<Koa.Context> = async (
    { context, info },
    next,
) => {
    try {
        return await next();
    } catch (err) {
        const graphqlError = new BaseGraphQLError(err);
        Sentry.withScope((scope) => {
            scope.addEventProcessor((event) => {
                return Sentry.addRequestDataToEvent(event, context.request);
            });
            scope.setUser({
                ...context.req['user'],
            });
            scope.setExtras({
                GraphQLError: graphqlError,
                'api version': version,
                'web version': context.req?.headers['web-version'],
                path: info.path?.key,
                query: context.request?.body,
                variables: JSON.stringify(info.variableValues),
            });
            Sentry.captureException(err);
        });
        console.error(graphqlError);
        throw graphqlError;
    }
};

// gRPC
export async function gRPCErrorInterceptor(ctx: MaliCtx<any>, next: any) {
    try {
        await next();
    } catch (err) {
        const gRPCError = new BaseGRPCError(err);
        Sentry.withScope((scope) => {
            scope.addEventProcessor((event) => {
                return Sentry.addRequestDataToEvent(event, ctx.req);
            });
            scope.setExtras({
                gRPCError: gRPCError,
                metadata: ctx.metadata,
            });
            Sentry.captureException(err);
        });
        console.error(gRPCError);
        throw gRPCError;
    }
}

// Koa
@Middleware({ type: 'before' })
export class HttpErrorInterceptor implements KoaMiddlewareInterface {
    async use(
        context: Koa.Context,
        next: (err?: any) => Promise<any>,
    ): Promise<any> {
        try {
            await next();
            if ((context.status || 404) == 404) {
                context.throw(404, 'Resource not found');
            }
        } catch (err: any) {
            const httpError = new BaseHttpError(err);
            Sentry.withScope((scope) => {
                scope.addEventProcessor((event) => {
                    return Sentry.addRequestDataToEvent(event, context.request);
                });
                scope.setUser({
                    ...context.req['user'],
                });
                scope.setExtras({
                    httpError: httpError,
                });
                Sentry.captureException(err);
            });
            console.error(httpError);

            // 將 httpError.constraints 中的細項errors攤平在 message中
            let message = httpError.message;
            message += httpError.constraints.length > 0 ? '\r\n ' : '';
            message += httpError.constraints.join('\r\n ');

            context.status = httpError.status;
            context.body = Helpers.Json.error({
                status: httpError.status,
                message: message,
            });
        }
    }
}
