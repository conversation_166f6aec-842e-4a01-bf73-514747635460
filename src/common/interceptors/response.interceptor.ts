import Koa from 'koa';
import { Context as WsContext } from 'graphql-ws/lib/server';
import { Interceptor, InterceptorInterface, Action } from 'routing-controllers';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import { UserPayload } from '@/modules/auth/types/auth.type';
import { Inject, Service } from 'typedi';
import { AuthService } from '@/modules/auth/providers/auth.service';
import { MiddlewareInterface, NextFn, ResolverData } from 'type-graphql';
import { object } from 'joi';
import { WsContextExtra } from '../types/websocket.type';

/**
 * 針對 routing-controllers 的結果添加格式
 */
@Interceptor()
export class ResponseInterceptor implements InterceptorInterface {
    intercept(action: Action, content: any) {
        // 判斷是否為匯出或webhook，如果不是匯出就套上 Helpers.Json.success 的格式
        if (
            action.response.body ||
            action.request.originalUrl.includes('/webhook')
        ) {
            return content;
        }
        return Helpers.Json.success(content);
    }
}

/**
 * 針對所有 Graphql Response 的每個欄位做權限過濾
 * 目前支援 Query、Mutation、FieldResolver 內的所有Fields
 */
@Service()
export class AuthorizedFieldsInterceptor
    implements MiddlewareInterface<Koa.Context>
{
    @Inject('AuthService')
    private authService: AuthService;

    async use(
        { context }: ResolverData<Koa.Context | WsContext<any, WsContextExtra>>,
        next: NextFn,
    ) {
        // TODO: Websocket尚未實作驗證欄位
        if (!('req' in context)) return await next();

        let user: UserPayload = context.req['user'];
        const token = context.req.headers['authorization'] || '';
        if (!user && token) {
            user = await Utils.JWT.verify(token);
        }

        const responese = await next();
        if (user) {
            const userPermission =
                await this.authService.findUserPermissionFromRedis(user.id);
            await this.authService.authorizedFields(userPermission, responese);
        }
        return responese;
    }
}
