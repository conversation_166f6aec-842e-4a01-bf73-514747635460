import Koa from 'koa';
import { Utils } from '@clinico/clinico-node-framework';
import { Context as WsContext } from 'graphql-ws/lib/server';
import { UseMiddleware } from 'type-graphql';
import { BaseError } from '@clinico/base-error';
import { WsContextExtra } from '../types/websocket.type';
import { KoaMiddlewareInterface } from 'routing-controllers';
import Container from 'typedi';
import { AuthService } from '@/modules/auth/providers/auth.service';
import _ from 'lodash';

const authService = Container.get<AuthService>('AuthService');

// Graphql
export const UserAuthInterceptor = (permissions?: string | string[]) => {
    return UseMiddleware(async ({ context, args, info }, next) => {
        const token = context.req.headers['authorization'] || '';
        const user = await Utils.JWT.verify(token);
        if (!user) {
            throw new BaseError('Relogin required', 401);
        }
        context.req['user'] = user;

        // 取得使用者權限
        const userPermission = await authService.findUserPermissionFromRedis(
            user.id,
        );
        context.req['permission'] = userPermission;

        // 權限判斷 EndPoint
        if (permissions) {
            await authService.userPermissionCheck(
                userPermission,
                Array.isArray(permissions) ? permissions : [permissions],
            );
        }

        // 權限判斷 查詢Args
        if (
            !_.isNil(args) &&
            info.path.typename === 'Query' &&
            'filters' in args
        ) {
            await authService.authorizedArgs(userPermission, args.filters);
        }

        return await next();
    });
};

// Koa Controller
export class UserAuthKoaInterceptor implements KoaMiddlewareInterface {
    async use(
        context: Koa.Context,
        next: (err?: any) => Promise<any>,
    ): Promise<any> {
        const token = context.header['authorization'] || '';
        const user = await Utils.JWT.verify(token);
        if (!user) {
            throw new BaseError('Relogin required', 401);
        }
        context.req['user'] = user;

        // 取得使用者權限
        const userPermission = await authService.findUserPermissionFromRedis(
            user.id,
        );
        context.req['permission'] = userPermission;

        /**
         * 權限判斷 EndPoint
         * 由於無法在 routing-controller 的 middlesWare 帶入PermissionCode參數
         * 所以改用 url 轉換成 PermissionCode
         * 範例: /business/export -> business.export
         */
        const permissionCode = context.request.url
            .replace(/\/|-/g, '.')
            .replace(/^\./, '');

        await authService.userPermissionCheck(userPermission, [permissionCode]);

        // 權限判斷 查詢Args
        if (!_.isNil(context.request.body)) {
            await authService.authorizedArgs(
                userPermission,
                context.request.body,
            );
        }

        await next();
    }
}

export class TokenInterceptors implements KoaMiddlewareInterface {
    async use(
        context: Koa.Context,
        next: (err?: any) => Promise<any>,
    ): Promise<any> {
        const token = context.header['authorization'] || '';
        const user = await Utils.JWT.verify(token);
        if (!user) {
            throw new BaseError('Relogin required', 401);
        }
        await next();
    }
}

// WebSocket
export const WebSocketUserAuthInterceptor = async (
    ctx: WsContext<any, WsContextExtra>,
): Promise<any> => {
    if (ctx.extra.user) return;
    const token = <string>ctx.connectionParams?.authorization ?? '';
    const user = await Utils.JWT.verify(token);
    ctx.extra.user = user;
    return ctx;
};
