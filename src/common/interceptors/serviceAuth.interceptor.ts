import Koa from 'koa';
import { Context } from 'mali';
import * as _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import Container from 'typedi';
import { AccessKeyService } from '@/modules/accessKey/providers/accessKey.service';
import { KoaMiddlewareInterface } from 'routing-controllers';

const accessKeyService = Container.get(AccessKeyService);

// GRPC
export async function gRPCAccessKeyAuthInterceptor(
    ctx: Context<any>,
    next: any,
) {
    const key = <string>ctx.metadata['access-key'] || '';
    const secret = <string>ctx.metadata['access-secret'] || '';

    await checkAuth({ key, secret });
    await next();
}

// Koa Controller
export class KoaAccessKeyAuthInterceptor implements KoaMiddlewareInterface {
    async use(
        context: Koa.Context,
        next: (err?: any) => Promise<any>,
    ): Promise<any> {
        const key = <string>context.header['access-key'] || '';
        const secret = <string>context.header['access-secret'] || '';

        // webhook does not require auth
        if (!context.path.includes('/webhook')) {
            await checkAuth({ key, secret });
        }

        await next();
    }
}

const checkAuth = async (params: {
    key: string;
    secret: string;
}): Promise<void> => {
    const accessKey = await accessKeyService.findOneByMetadataStorage(
        params.key,
    );
    if (_.isNil(accessKey) || accessKey.secret !== params.secret) {
        throw new BaseError('Access denied', 403);
    }
};
