import { DataSource } from 'typeorm';
import configs from '@/configs';

const { host, port, name, user, password, entities } = configs.database.clinico;
export const ClinicoDataSource = new DataSource({
    type: 'postgres',
    host: host,
    port: port,
    database: name,
    username: user,
    password: password,
    synchronize: false,
    logging: configs.database.logging,
    entities: entities,
    poolSize: 50,
    applicationName: 'clinico-erp-api',
});
