import { DataSource } from 'typeorm';
import configs from '@/configs';

const { host, port, name, user, password } = configs.database.tiptop;
export const TipTopDataSource = new DataSource({
    type: 'oracle',
    host: host,
    port: port,
    database: name,
    serviceName: name,
    username: user,
    password: password,
    synchronize: false,
    logging: configs.database.logging,
    entities: [__dirname + '/models/tiptop/**/*.model.{ts,js}'],
});
