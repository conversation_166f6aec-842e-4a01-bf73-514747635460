import { DataSource } from 'typeorm';
import configs from '@/configs';

const { host, port, name, user, password } = configs.database.yonyou;
export const YonYouDataSource = new DataSource({
    type: 'mssql',
    host: host,
    port: port,
    database: name,
    username: user,
    password: password,
    synchronize: false,
    options: {
        encrypt: false,
    },
    logging: configs.database.logging,
    entities: [__dirname + '/models/yonyou/**/*.model.{ts,js}'],
});
