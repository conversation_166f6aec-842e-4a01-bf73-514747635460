import { createClient } from 'redis';

type RedisClientType = ReturnType<typeof createClient>;

let client: RedisClientType;
export { client };

export const initialize = async (params: {
    host: string;
    port: number;
    password?: string;
}) => {
    client = createClient({
        socket: {
            host: params.host,
            port: params.port,
        },
        password: params.password,
    });
    await client.connect();
};
