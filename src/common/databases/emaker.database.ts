import { DataSource } from 'typeorm';
import configs from '@/configs';

const { host, port, name, user, password } = configs.database.emaker;
export const EmakerDataSource = new DataSource({
    type: 'mssql',
    host: host,
    port: port,
    database: name,
    username: user,
    password: password,
    synchronize: false,
    options: {
        encrypt: false,
    },
    logging: configs.database.logging,
    requestTimeout: 60 * 1000, // 60秒
    entities: [__dirname + '/models/emaker/**/*.model.{ts,js}'],
});
