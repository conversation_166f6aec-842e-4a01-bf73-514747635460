import { Service } from 'typedi';
import { AuthorizedMetadata, AccessKeyMetadata } from './metadataStorage.type';

@Service('MetadataStorage')
export class MetadataStorage {
    authorizedFields: AuthorizedMetadata[] = [];
    accessKeyMetadatas: AccessKeyMetadata[] = [];

    collectAuthorizedFieldMetadata(definition: AuthorizedMetadata) {
        this.authorizedFields.push(definition);
    }

    collectAccessKeyMetadata(definition: AccessKeyMetadata) {
        this.accessKeyMetadatas.push(definition);
    }
}
