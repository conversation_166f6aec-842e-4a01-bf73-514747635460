import { EnumEyeQuotationOrderType } from '@clinico/type-graphql-persistence/models/public/eyeQuotationOrderType.model';
import { registerEnumType } from 'type-graphql';

export enum EnumLanguage {
    'zh-tw',
    'zh-cn',
}

// 排序方向
export enum EnumSortDirection {
    // A-Z 升序（Ascending）
    ASC = 'ASC',
    // Z-A 降序（Descending）
    DESC = 'DESC',
}
registerEnumType(EnumSortDirection, {
    name: 'EnumSortDirection',
    description: '排序方向',
    valuesConfig: {
        ASC: { description: 'A-Z 升序（Ascending）' },
        DESC: { description: 'Z-A 降序（Descending）' },
    },
});

export const OnlyMaterialTypes = [
    EnumEyeQuotationOrderType.ExhibitionConsumption,
    EnumEyeQuotationOrderType.Scrap,
    EnumEyeQuotationOrderType.Requisition,
    EnumEyeQuotationOrderType.Purchase,
    EnumEyeQuotationOrderType.InventoryToFixedAssets,
    EnumEyeQuotationOrderType.ForeignRequisition,
    EnumEyeQuotationOrderType.Repair,
    EnumEyeQuotationOrderType.FemtosecondRepair,
];

// 價格等級配置 - 按權限等級排序（低到高）
export const PRICE_LEVELS = [
    {
        label: '建议价格',
        priceKey: 'salePrice',
        priority: 1,
        belowLabel: '低于出货指导价',
    },
    {
        label: 'DSM 价格',
        priceKey: 'dsmPrice',
        priority: 2,
        belowLabel: '低于DSM价格',
    },
    {
        label: '大区价格',
        priceKey: 'regionalManagerPrice',
        priority: 3,
        belowLabel: '低于大区价格',
    },
    {
        label: '事业主管价格',
        priceKey: 'businessManagerPrice',
        priority: 4,
        belowLabel: '低于事业主管价格',
    },
    {
        label: '总经理价格',
        priceKey: 'generalManagerPrice',
        priority: 5,
        belowLabel: '低于总经理价格',
    },
] as const;
