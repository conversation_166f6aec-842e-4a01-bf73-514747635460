import { BaseError } from '@clinico/base-error';
import { CommonSearchParams, CommonSearchResult } from '../types/common.type';

export abstract class CommonEmakerService<T> {
    get commonDataNotFoundMessage(): string {
        return 'Data not found';
    }

    protected limitSQL(sql: string, offset?: number, limit?: number): string {
        const _offset = (offset ?? 0) + 1;
        const _limit = _offset - 1 + (limit ?? 50); // 預設 50 筆
        return `WITH t AS ( ${sql} ) SELECT * FROM ( SELECT * FROM t) AS sub WHERE sub.row_number BETWEEN ${_offset} AND ${_limit}`;
    }

    protected countSQL(sql: string): string {
        return `WITH t AS ( ${sql} ) SELECT count(*) AS "count" FROM t`;
    }

    protected returnEmpty(): CommonSearchResult<T> {
        return {
            pageInfo: {
                hasNextPage: false,
                hasPreviousPage: false,
            },
            count: 0,
            rows: [],
        };
    }

    abstract search(params: CommonSearchParams): Promise<CommonSearchResult<T>>;
}
