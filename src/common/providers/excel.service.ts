import { Utils } from '@clinico/clinico-node-framework';
import configs from '@/configs';
import _ from 'lodash';

export abstract class ExcelService<QueryParams> {
    /** 指定表單並新增 */
    public sheetName: string;
    /** 報表表頭 */
    public titles: string[];

    private defaultSheetName = 'Sheet1';
    private titleStyle: any = {
        font: {
            size: 10,
            bold: true,
            color: { argb: 'FFFFFFFF' },
            name: 'Arial',
        },
        alignment: {
            horizontal: 'centerContinuous',
            vertical: 'justify',
        },
        fill: {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '46bdc6' },
        },
    };
    private bodyStyle: any = {
        font: {
            size: 10,
            name: 'Arial',
        },
        alignment: {
            horizontal: 'left',
            vertical: 'justify',
        },
    };

    /**
     * 生成Excel Buffer
     */
    public async excel(params: QueryParams): Promise<Buffer> {
        const data = await this.data(params);
        const generator = new Utils.Excel2Templater();
        await generator.load(configs.templateFolder + `/excels/base.xlsx`);

        if (!_.isNil(this.sheetName)) {
            generator.addSheet(this.sheetName);
        }
        generator.sheet(this.sheetName ?? this.defaultSheetName);
        generator.fillRow({
            index: 1,
            data: this.titles ?? Object.keys(data[0]),
        });
        generator.rowStyle({
            index: 1,
            style: this.titleStyle,
        });

        generator.fillRows({
            startRowIndex: 2,
            data: data,
            cellOptions: this.bodyStyle,
        });
        return await generator.saveToBuffer();
    }

    abstract data(params: QueryParams): Promise<any[]>;
}
