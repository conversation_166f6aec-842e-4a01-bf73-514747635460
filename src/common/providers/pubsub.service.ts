import { RedisPubSub } from 'graphql-redis-subscriptions';
import Redis from 'ioredis';
import configs from '@/configs';

const options = {
    host: configs.database.redis.host,
    port: configs.database.redis.port,
    retryStrategy: (times) => {
        return Math.min(times * 50, 2000);
    },
};

export const pubsub = new RedisPubSub({
    publisher: new Redis(options),
    subscriber: new Redis(options),
});
