import { Utils } from '@clinico/clinico-node-framework';
import { URLResolver } from 'graphql-scalars';
import { ClassType, FieldResolver, Float, Resolver, Root } from 'type-graphql';
import { AWSHelper } from '@/common/helpers/AWS.helper';

export const createFileBaseResolver = <T extends ClassType>(root: T) => {
    @Resolver((of) => root, { isAbstract: true })
    abstract class FileBaseResolver {
        @FieldResolver((type) => URLResolver, {
            nullable: true,
            description: '短效期的S3 url',
        })
        async url(@Root() root: T): Promise<string | null> {
            if (!(<any>root).s3Key) return null;
            return Utils.AWS.S3.getSignedUrl({
                bucket: AWSHelper.bucket(),
                key: (<any>root).s3Key,
            });
        }

        @FieldResolver((type) => Float, {
            description: '檔案大小(Bytes)',
        })
        async size(@Root() root: T): Promise<number> {
            if (!(<any>root).s3Key) return 0;
            const S3Object = await Utils.AWS.S3.getHeadObject({
                bucket: AWSHelper.bucket(),
                key: (<any>root).s3Key,
            });
            if (!S3Object?.ContentLength) return 0;
            return S3Object.ContentLength;
        }
    }
    return FileBaseResolver;
};
