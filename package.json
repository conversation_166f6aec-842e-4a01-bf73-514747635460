{"name": "clinico-erp-api", "version": "1.4.132", "description": "", "main": "index.js", "private": true, "author": "", "license": "UNLICENSED", "scripts": {"clean": "npx rimraf ./dist", "build": "npm run clean && npx tsc && cp .env* ./dist", "start:dev": "NODE_ENV=local npx tsc-watch -p tsconfig.json --onSuccess \"node dist/app.js\"", "format": "npx prettier --write \"{src,test}/**/*.ts\"", "lint": "npx eslint \"{src,test}/**/*.ts\" --fix", "prepare": "npx husky install", "test": "npx jest", "buf:gen": "buf generate"}, "_moduleAliases": {"@": "./dist"}, "lint-staged": {"*.ts": ["npx eslint --fix"]}, "devDependencies": {"@types/archiver": "^6.0.3", "@types/big.js": "^6.1.6", "@types/dotenv-flow": "^3.2.0", "@types/graphql-upload": "^8.0.11", "@types/jest": "^29.2.4", "@types/kcors": "^2.2.6", "@types/koa": "^2.13.5", "@types/koa-bodyparser": "^4.3.7", "@types/koa-logger": "^3.1.2", "@types/koa-router": "^7.4.4", "@types/lodash": "^4.14.184", "@types/redis": "^4.0.11", "@types/ua-parser-js": "^0.7.36", "@types/ws": "^8.5.3", "@typescript-eslint/eslint-plugin": "^5.30.7", "@typescript-eslint/parser": "^5.30.7", "cli-progress": "^3.12.0", "eslint": "^8.20.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.3.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "ts-proto": "^1.129.0", "ts-protoc-gen": "^0.15.0", "tsc-watch": "^5.0.3", "tsconfig-paths": "^4.0.0", "typescript": "^4.7.4"}, "dependencies": {"@clinico/base-error": "^0.0.5", "@clinico/clinico-node-framework": "^1.12.46", "@clinico/graphql-upload": "^13.0.2", "@clinico/type-graphql-persistence": "^1.4.7-stage.108", "@clinico/typeorm-persistence": "^1.4.7-stage.119", "@grpc/grpc-js": "^1.6.9", "@grpc/proto-loader": "^0.6.13", "@sentry/node": "^7.17.2", "apollo-server-core": "^3.11.1", "apollo-server-koa": "^3.10.0", "archiver": "^7.0.1", "axios": "^1.1.3", "big.js": "^6.2.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "class-validator-jsonschema": "^5.0.0", "dataloader": "^2.1.0", "dotenv-flow": "^3.2.0", "exceljs": "^4.4.0", "graphql": "^15.9.0", "graphql-redis-subscriptions": "^2.5.0", "graphql-request": "^6.1.0", "graphql-scalars": "^1.18.0", "graphql-ws": "^5.10.2", "http-errors": "^2.0.0", "http-status": "^1.5.3", "i18next": "^21.9.1", "ioredis": "^5.2.3", "joi": "^17.6.0", "kcors": "^2.2.2", "koa": "^2.13.4", "koa-bodyparser": "^4.4.1", "koa-logger": "^3.2.1", "koa-multer": "^1.0.2", "koa-router": "^12.0.0", "koa-static": "^5.0.0", "koa2-swagger-ui": "^5.6.0", "lodash": "^4.17.21", "mali": "^0.46.1", "module-alias": "^2.2.2", "moment": "^2.29.4", "mssql": "^9.1.1", "nice-grpc": "^2.0.1", "oracledb": "^5.5.0", "pg": "^8.7.3", "redis": "^4.2.0", "reflect-metadata": "^0.1.13", "routing-controllers": "^0.10.2", "routing-controllers-openapi": "^4.0.0", "type-graphql": "^1.2.0-rc.1", "typedi": "^0.10.0", "typeorm": "^0.3.13", "ua-parser-js": "^1.0.33", "uuid": "^9.0.0", "winston": "^3.8.1", "ws": "^8.8.1"}}